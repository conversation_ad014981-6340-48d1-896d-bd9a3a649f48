{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-product" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-product" class="form-horizontal">
      <div class="nav-tabs-custom">
        <ul class="nav nav-tabs" id="product-tabs">
          <li class="active"><a href="#tab-general" data-toggle="tab"><i class="fa fa-info-circle"></i> {{ tab_general }}</a></li>
          <li><a href="#tab-variants" data-toggle="tab"><i class="fa fa-th-large"></i> {{ tab_variants }}</a></li>
          <li><a href="#tab-images" data-toggle="tab"><i class="fa fa-images"></i> {{ tab_images }}</a></li>
          <li><a href="#tab-pricing" data-toggle="tab"><i class="fa fa-tag"></i> {{ tab_pricing }}</a></li>
          <li><a href="#tab-units" data-toggle="tab"><i class="fa fa-balance-scale"></i> {{ tab_units }}</a></li>
          <li><a href="#tab-barcode" data-toggle="tab"><i class="fa fa-barcode"></i> {{ tab_barcode }}</a></li>
          <li><a href="#tab-batches" data-toggle="tab"><i class="fa fa-calendar-check"></i> {{ tab_batches }}</a></li>
          <li><a href="#tab-bundles" data-toggle="tab"><i class="fa fa-cubes"></i> {{ tab_bundles }}</a></li>
          <li><a href="#tab-options" data-toggle="tab"><i class="fa fa-list-alt"></i> {{ tab_options }}</a></li>
          <li><a href="#tab-discounts" data-toggle="tab"><i class="fa fa-percent"></i> {{ tab_discounts }}</a></li>
          <li><a href="#tab-inventory" data-toggle="tab"><i class="fa fa-warehouse"></i> {{ tab_inventory }}</a></li>
          <li><a href="#tab-seo" data-toggle="tab"><i class="fa fa-search"></i> {{ tab_seo }}</a></li>
        </ul>

        <div class="tab-content">
          <!-- تبويب المعلومات العامة -->
          <div class="tab-pane active" id="tab-general">
            <div class="row">
              <div class="col-md-8">
                <!-- معلومات المنتج الأساسية -->
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title"><i class="fa fa-info"></i> {{ text_product_info }}</h3>
                  </div>
                  <div class="panel-body">
                    <ul class="nav nav-tabs" id="language">
                      {% for language in languages %}
                      <li{% if loop.first %} class="active"{% endif %}><a href="#language{{ language.language_id }}" data-toggle="tab"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}</a></li>
                      {% endfor %}
                    </ul>
                    <div class="tab-content">
                      {% for language in languages %}
                      <div class="tab-pane{% if loop.first %} active{% endif %}" id="language{{ language.language_id }}">
                        <div class="form-group required">
                          <label class="col-sm-2 control-label" for="input-name{{ language.language_id }}">{{ entry_name }}</label>
                          <div class="col-sm-10">
                            <input type="text" name="product_description[{{ language.language_id }}][name]" value="{{ product_description[language.language_id] ? product_description[language.language_id].name }}" placeholder="{{ entry_name }}" id="input-name{{ language.language_id }}" class="form-control" />
                            {% if error_name[language.language_id] %}
                            <div class="text-danger">{{ error_name[language.language_id] }}</div>
                            {% endif %}
                          </div>
                        </div>
                        <div class="form-group">
                          <label class="col-sm-2 control-label" for="input-description{{ language.language_id }}">{{ entry_description }}</label>
                          <div class="col-sm-10">
                            <textarea name="product_description[{{ language.language_id }}][description]" placeholder="{{ entry_description }}" id="input-description{{ language.language_id }}" data-oc-toggle="ckeditor" data-lang="{{ ckeditor }}" class="form-control">{{ product_description[language.language_id] ? product_description[language.language_id].description }}</textarea>
                          </div>
                        </div>
                        <div class="form-group">
                          <label class="col-sm-2 control-label" for="input-tag{{ language.language_id }}">{{ entry_tag }}</label>
                          <div class="col-sm-10">
                            <input type="text" name="product_description[{{ language.language_id }}][tag]" value="{{ product_description[language.language_id] ? product_description[language.language_id].tag }}" placeholder="{{ entry_tag }}" id="input-tag{{ language.language_id }}" class="form-control" />
                            <div class="help-block">{{ help_tag }}</div>
                          </div>
                        </div>
                      </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>

                <!-- معلومات التصنيف والعلامة التجارية -->
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title"><i class="fa fa-sitemap"></i> {{ text_categorization }}</h3>
                  </div>
                  <div class="panel-body">
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-category">{{ entry_category }}</label>
                      <div class="col-sm-10">
                        <input type="text" name="category" value="" placeholder="{{ entry_category }}" id="input-category" class="form-control" />
                        <div id="product-category" class="well well-sm" style="height: 150px; overflow: auto;">
                          {% for product_category in product_categories %}
                          <div id="product-category{{ product_category.category_id }}"><i class="fa fa-minus-circle"></i> {{ product_category.name }}
                            <input type="hidden" name="product_category[]" value="{{ product_category.category_id }}" />
                          </div>
                          {% endfor %}
                        </div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-manufacturer">{{ entry_manufacturer }}</label>
                      <div class="col-sm-10">
                        <input type="text" name="manufacturer" value="{{ manufacturer }}" placeholder="{{ entry_manufacturer }}" id="input-manufacturer" class="form-control" />
                        <input type="hidden" name="manufacturer_id" value="{{ manufacturer_id }}" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-4">
                <!-- صورة المنتج -->
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title"><i class="fa fa-image"></i> {{ text_product_image }}</h3>
                  </div>
                  <div class="panel-body text-center">
                    <a href="" id="thumb-image" data-toggle="image" class="img-thumbnail">
                      <img src="{{ thumb }}" alt="" title="" data-placeholder="{{ placeholder }}" />
                    </a>
                    <input type="hidden" name="image" value="{{ image }}" id="input-image" />
                  </div>
                </div>

                <!-- معلومات المنتج الفنية -->
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title"><i class="fa fa-cog"></i> {{ text_technical_info }}</h3>
                  </div>
                  <div class="panel-body">
                    <div class="form-group required">
                      <label class="col-sm-4 control-label" for="input-model">{{ entry_model }}</label>
                      <div class="col-sm-8">
                        <div class="input-group">
                          <input type="text" name="model" value="{{ model }}" placeholder="{{ entry_model }}" id="input-model" class="form-control" />
                          <span class="input-group-btn">
                            <button type="button" id="button-generate-code" class="btn btn-info" data-toggle="tooltip" title="{{ button_generate_code }}"><i class="fa fa-magic"></i></button>
                          </span>
                        </div>
                        {% if error_model %}
                        <div class="text-danger">{{ error_model }}</div>
                        {% endif %}
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-4 control-label" for="input-sku">{{ entry_sku }}</label>
                      <div class="col-sm-8">
                        <input type="text" name="sku" value="{{ sku }}" placeholder="{{ entry_sku }}" id="input-sku" class="form-control" />
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-4 control-label" for="input-upc">{{ entry_upc }}</label>
                      <div class="col-sm-8">
                        <input type="text" name="upc" value="{{ upc }}" placeholder="{{ entry_upc }}" id="input-upc" class="form-control" />
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-4 control-label" for="input-ean">{{ entry_ean }}</label>
                      <div class="col-sm-8">
                        <input type="text" name="ean" value="{{ ean }}" placeholder="{{ entry_ean }}" id="input-ean" class="form-control" />
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-4 control-label" for="input-jan">{{ entry_jan }}</label>
                      <div class="col-sm-8">
                        <input type="text" name="jan" value="{{ jan }}" placeholder="{{ entry_jan }}" id="input-jan" class="form-control" />
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-4 control-label" for="input-isbn">{{ entry_isbn }}</label>
                      <div class="col-sm-8">
                        <input type="text" name="isbn" value="{{ isbn }}" placeholder="{{ entry_isbn }}" id="input-isbn" class="form-control" />
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-4 control-label" for="input-mpn">{{ entry_mpn }}</label>
                      <div class="col-sm-8">
                        <input type="text" name="mpn" value="{{ mpn }}" placeholder="{{ entry_mpn }}" id="input-mpn" class="form-control" />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- حالة المنتج -->
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title"><i class="fa fa-toggle-on"></i> {{ text_status_settings }}</h3>
                  </div>
                  <div class="panel-body">
                    <div class="form-group">
                      <label class="col-sm-4 control-label">{{ entry_status }}</label>
                      <div class="col-sm-8">
                        <label class="radio-inline">
                          <input type="radio" name="status" value="1"{% if status %} checked="checked"{% endif %} />
                          {{ text_enabled }}
                        </label>
                        <label class="radio-inline">
                          <input type="radio" name="status" value="0"{% if not status %} checked="checked"{% endif %} />
                          {{ text_disabled }}
                        </label>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-4 control-label" for="input-date-available">{{ entry_date_available }}</label>
                      <div class="col-sm-8">
                        <div class="input-group date">
                          <input type="text" name="date_available" value="{{ date_available }}" placeholder="{{ entry_date_available }}" data-date-format="YYYY-MM-DD" id="input-date-available" class="form-control" />
                          <span class="input-group-btn">
                            <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-4 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
                      <div class="col-sm-8">
                        <input type="text" name="sort_order" value="{{ sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب المتغيرات (المقاسات والألوان) -->
          <div class="tab-pane" id="tab-variants">
            <div class="row">
              <div class="col-md-6">
                <!-- إدارة المقاسات -->
                <div class="panel panel-primary">
                  <div class="panel-heading">
                    <h3 class="panel-title"><i class="fa fa-expand-arrows-alt"></i> إدارة المقاسات</h3>
                    <div class="panel-tools">
                      <button type="button" id="button-size-add" class="btn btn-success btn-sm">
                        <i class="fa fa-plus"></i> إضافة مقاس
                      </button>
                    </div>
                  </div>
                  <div class="panel-body">
                    <div class="table-responsive">
                      <table id="sizes-table" class="table table-striped table-bordered table-hover">
                        <thead>
                          <tr>
                            <th>اسم المقاس</th>
                            <th>الكود</th>
                            <th>الترتيب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                          </tr>
                        </thead>
                        <tbody>
                          {% for size in product_sizes %}
                          <tr id="size-row-{{ size.size_id }}">
                            <td>{{ size.name }}</td>
                            <td>{{ size.code }}</td>
                            <td>{{ size.sort_order }}</td>
                            <td>
                              <span class="label label-{{ size.status ? 'success' : 'danger' }}">
                                {{ size.status ? 'مفعل' : 'معطل' }}
                              </span>
                            </td>
                            <td>
                              <div class="btn-group">
                                <button type="button" class="btn btn-info btn-sm" onclick="editSize({{ size.size_id }})">
                                  <i class="fa fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteSize({{ size.size_id }})">
                                  <i class="fa fa-trash"></i>
                                </button>
                              </div>
                            </td>
                          </tr>
                          {% endfor %}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-md-6">
                <!-- إدارة الألوان -->
                <div class="panel panel-info">
                  <div class="panel-heading">
                    <h3 class="panel-title"><i class="fa fa-palette"></i> إدارة الألوان</h3>
                    <div class="panel-tools">
                      <button type="button" id="button-color-add" class="btn btn-success btn-sm">
                        <i class="fa fa-plus"></i> إضافة لون
                      </button>
                    </div>
                  </div>
                  <div class="panel-body">
                    <div class="table-responsive">
                      <table id="colors-table" class="table table-striped table-bordered table-hover">
                        <thead>
                          <tr>
                            <th>اللون</th>
                            <th>اسم اللون</th>
                            <th>الكود</th>
                            <th>الترتيب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                          </tr>
                        </thead>
                        <tbody>
                          {% for color in product_colors %}
                          <tr id="color-row-{{ color.color_id }}">
                            <td>
                              <div class="color-preview" style="width: 30px; height: 30px; background-color: {{ color.hex_value }}; border: 1px solid #ccc; border-radius: 3px;"></div>
                            </td>
                            <td>{{ color.name }}</td>
                            <td>{{ color.code }}</td>
                            <td>{{ color.sort_order }}</td>
                            <td>
                              <span class="label label-{{ color.status ? 'success' : 'danger' }}">
                                {{ color.status ? 'مفعل' : 'معطل' }}
                              </span>
                            </td>
                            <td>
                              <div class="btn-group">
                                <button type="button" class="btn btn-info btn-sm" onclick="editColor({{ color.color_id }})">
                                  <i class="fa fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="deleteColor({{ color.color_id }})">
                                  <i class="fa fa-trash"></i>
                                </button>
                              </div>
                            </td>
                          </tr>
                          {% endfor %}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- متغيرات المنتج -->
            <div class="panel panel-success">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-th-large"></i> متغيرات المنتج</h3>
                <div class="panel-tools">
                  <button type="button" id="button-variant-add" class="btn btn-primary btn-sm">
                    <i class="fa fa-plus"></i> إضافة متغير
                  </button>
                  <button type="button" id="button-variant-generate" class="btn btn-warning btn-sm">
                    <i class="fa fa-magic"></i> توليد تلقائي
                  </button>
                </div>
              </div>
              <div class="panel-body">
                <div class="table-responsive">
                  <table id="variants-table" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <th>المقاس</th>
                        <th>اللون</th>
                        <th>SKU</th>
                        <th>تعديل السعر</th>
                        <th>الكمية</th>
                        <th>الصورة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% for variant in product_variants %}
                      <tr id="variant-row-{{ variant.variant_id }}">
                        <td>{{ variant.size_name ?: 'غير محدد' }}</td>
                        <td>
                          {% if variant.color_name %}
                            <div class="color-variant">
                              <div class="color-preview-small" style="background-color: {{ variant.hex_value }}"></div>
                              {{ variant.color_name }}
                            </div>
                          {% else %}
                            غير محدد
                          {% endif %}
                        </td>
                        <td>{{ variant.sku }}</td>
                        <td>
                          <span class="price-modifier {{ variant.price_modifier >= 0 ? 'text-success' : 'text-danger' }}">
                            {{ variant.price_modifier >= 0 ? '+' : '' }}{{ variant.price_modifier }}
                          </span>
                        </td>
                        <td>{{ variant.quantity }}</td>
                        <td>
                          {% if variant.image %}
                            <img src="{{ variant.image }}" alt="" class="img-thumbnail" style="width: 40px; height: 40px;">
                          {% else %}
                            <span class="text-muted">لا توجد صورة</span>
                          {% endif %}
                        </td>
                        <td>
                          <span class="label label-{{ variant.status ? 'success' : 'danger' }}">
                            {{ variant.status ? 'مفعل' : 'معطل' }}
                          </span>
                        </td>
                        <td>
                          <div class="btn-group">
                            <button type="button" class="btn btn-info btn-sm" onclick="editVariant({{ variant.variant_id }})">
                              <i class="fa fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteVariant({{ variant.variant_id }})">
                              <i class="fa fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                      {% endfor %}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب الصور المتعددة -->
          <div class="tab-pane" id="tab-images">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-images"></i> إدارة الصور المتعددة</h3>
                <div class="panel-tools">
                  <button type="button" id="button-upload-images" class="btn btn-primary btn-sm">
                    <i class="fa fa-upload"></i> رفع صور متعددة
                  </button>
                </div>
              </div>
              <div class="panel-body">
                <!-- منطقة السحب والإفلات -->
                <div id="dropzone" class="dropzone-area">
                  <div class="dropzone-content">
                    <i class="fa fa-cloud-upload fa-3x text-muted"></i>
                    <h4>اسحب الصور هنا أو انقر للاختيار</h4>
                    <p class="text-muted">يمكنك رفع عدة صور في نفس الوقت (JPG, PNG, GIF, WEBP)</p>
                  </div>
                  <input type="file" id="image-upload" multiple accept="image/*" style="display: none;">
                </div>

                <!-- معاينة الصور -->
                <div id="images-preview" class="images-grid">
                  {% for image in product_images %}
                  <div class="image-item" data-image-id="{{ image.image_id }}" data-sort-order="{{ image.sort_order }}">
                    <div class="image-container">
                      <img src="{{ image.image }}" alt="{{ image.alt_text }}" class="img-responsive">
                      <div class="image-overlay">
                        <div class="image-actions">
                          <button type="button" class="btn btn-info btn-sm" onclick="editImageAlt({{ image.image_id }})">
                            <i class="fa fa-edit"></i>
                          </button>
                          <button type="button" class="btn btn-danger btn-sm" onclick="deleteImage({{ image.image_id }})">
                            <i class="fa fa-trash"></i>
                          </button>
                        </div>
                        <div class="image-sort-handle">
                          <i class="fa fa-arrows-alt"></i>
                        </div>
                      </div>
                    </div>
                    <div class="image-info">
                      <input type="text" class="form-control input-sm" value="{{ image.alt_text }}"
                             placeholder="النص البديل" onchange="updateImageAlt({{ image.image_id }}, this.value)">
                      <small class="text-muted">ترتيب: {{ image.sort_order }}</small>
                    </div>
                  </div>
                  {% endfor %}
                </div>

                <!-- شريط التقدم للرفع -->
                <div id="upload-progress" class="progress" style="display: none;">
                  <div class="progress-bar progress-bar-striped active" role="progressbar" style="width: 0%">
                    <span class="sr-only">0% مكتمل</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب التسعير المتطور (5 مستويات) -->
          <div class="tab-pane" id="tab-pricing">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-tag"></i> {{ text_advanced_pricing }}</h3>
                <div class="panel-tools">
                  <button type="button" id="button-pricing-add" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> {{ button_add_pricing }}</button>
                </div>
              </div>
              <div class="panel-body">
                <div class="alert alert-info">
                  <i class="fa fa-info-circle"></i> {{ help_advanced_pricing }}
                </div>
                <div class="table-responsive">
                  <table id="pricing-table" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <th>{{ column_unit }}</th>
                        <th>{{ column_cost_price }}</th>
                        <th>{{ column_base_price }}</th>
                        <th>{{ column_special_price }}</th>
                        <th>{{ column_wholesale_price }}</th>
                        <th>{{ column_half_wholesale_price }}</th>
                        <th>{{ column_custom_price }}</th>
                        <th>{{ column_profit_margin }}</th>
                        <th>{{ column_action }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% set pricing_row = 0 %}
                      {% for pricing in product_pricing %}
                      <tr id="pricing-row{{ pricing_row }}">
                        <td>
                          <select name="product_pricing[{{ pricing_row }}][unit_id]" class="form-control">
                            {% for unit in units %}
                            <option value="{{ unit.unit_id }}"{% if unit.unit_id == pricing.unit_id %} selected="selected"{% endif %}>{{ unit.name }}</option>
                            {% endfor %}
                          </select>
                        </td>
                        <td><input type="text" name="product_pricing[{{ pricing_row }}][cost_price]" value="{{ pricing.cost_price }}" placeholder="{{ column_cost_price }}" class="form-control cost-price" /></td>
                        <td><input type="text" name="product_pricing[{{ pricing_row }}][base_price]" value="{{ pricing.base_price }}" placeholder="{{ column_base_price }}" class="form-control base-price" /></td>
                        <td><input type="text" name="product_pricing[{{ pricing_row }}][special_price]" value="{{ pricing.special_price }}" placeholder="{{ column_special_price }}" class="form-control" /></td>
                        <td><input type="text" name="product_pricing[{{ pricing_row }}][wholesale_price]" value="{{ pricing.wholesale_price }}" placeholder="{{ column_wholesale_price }}" class="form-control" /></td>
                        <td><input type="text" name="product_pricing[{{ pricing_row }}][half_wholesale_price]" value="{{ pricing.half_wholesale_price }}" placeholder="{{ column_half_wholesale_price }}" class="form-control" /></td>
                        <td><input type="text" name="product_pricing[{{ pricing_row }}][custom_price]" value="{{ pricing.custom_price }}" placeholder="{{ column_custom_price }}" class="form-control" /></td>
                        <td><span class="profit-margin">{{ pricing.profit_margin }}%</span></td>
                        <td><button type="button" onclick="$('#pricing-row{{ pricing_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
                      </tr>
                      {% set pricing_row = pricing_row + 1 %}
                      {% endfor %}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب الوحدات المتطورة -->
          <div class="tab-pane" id="tab-units">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-balance-scale"></i> {{ text_advanced_units }}</h3>
                <div class="panel-tools">
                  <button type="button" id="button-unit-add" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> {{ button_add_unit }}</button>
                </div>
              </div>
              <div class="panel-body">
                <div class="alert alert-info">
                  <i class="fa fa-info-circle"></i> {{ help_advanced_units }}
                </div>
                <div class="table-responsive">
                  <table id="units-table" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <th>{{ column_unit_name }}</th>
                        <th>{{ column_unit_type }}</th>
                        <th>{{ column_conversion_factor }}</th>
                        <th>{{ column_is_base_unit }}</th>
                        <th>{{ column_sort_order }}</th>
                        <th>{{ column_action }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% set unit_row = 0 %}
                      {% for unit in product_units %}
                      <tr id="unit-row{{ unit_row }}">
                        <td>
                          <select name="product_units[{{ unit_row }}][unit_id]" class="form-control">
                            {% for available_unit in units %}
                            <option value="{{ available_unit.unit_id }}"{% if available_unit.unit_id == unit.unit_id %} selected="selected"{% endif %}>{{ available_unit.name }}</option>
                            {% endfor %}
                          </select>
                        </td>
                        <td>
                          <select name="product_units[{{ unit_row }}][unit_type]" class="form-control">
                            <option value="base"{% if unit.unit_type == 'base' %} selected="selected"{% endif %}>{{ text_base_unit }}</option>
                            <option value="sub"{% if unit.unit_type == 'sub' %} selected="selected"{% endif %}>{{ text_sub_unit }}</option>
                            <option value="super"{% if unit.unit_type == 'super' %} selected="selected"{% endif %}>{{ text_super_unit }}</option>
                          </select>
                        </td>
                        <td><input type="text" name="product_units[{{ unit_row }}][conversion_factor]" value="{{ unit.conversion_factor }}" placeholder="{{ column_conversion_factor }}" class="form-control" /></td>
                        <td>
                          <input type="checkbox" name="product_units[{{ unit_row }}][is_base_unit]" value="1"{% if unit.is_base_unit %} checked="checked"{% endif %} />
                        </td>
                        <td><input type="text" name="product_units[{{ unit_row }}][sort_order]" value="{{ unit.sort_order }}" placeholder="{{ column_sort_order }}" class="form-control" /></td>
                        <td><button type="button" onclick="$('#unit-row{{ unit_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
                      </tr>
                      {% set unit_row = unit_row + 1 %}
                      {% endfor %}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب الباركود المتقدم -->
          <div class="tab-pane" id="tab-barcode">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-barcode"></i> {{ text_advanced_barcode }}</h3>
                <div class="panel-tools">
                  <button type="button" id="button-barcode-add" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> {{ button_add_barcode }}</button>
                </div>
              </div>
              <div class="panel-body">
                <div class="alert alert-info">
                  <i class="fa fa-info-circle"></i> {{ help_advanced_barcode }}
                </div>
                <div class="table-responsive">
                  <table id="barcode-table" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <th>{{ column_barcode }}</th>
                        <th>{{ column_barcode_type }}</th>
                        <th>{{ column_linked_unit }}</th>
                        <th>{{ column_linked_option }}</th>
                        <th>{{ column_is_primary }}</th>
                        <th>{{ column_action }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% set barcode_row = 0 %}
                      {% for barcode in product_barcodes %}
                      <tr id="barcode-row{{ barcode_row }}">
                        <td>
                          <div class="input-group">
                            <input type="text" name="product_barcodes[{{ barcode_row }}][barcode]" value="{{ barcode.barcode }}" placeholder="{{ column_barcode }}" class="form-control" />
                            <span class="input-group-btn">
                              <button type="button" class="btn btn-info" onclick="generateBarcode({{ barcode_row }});"><i class="fa fa-magic"></i></button>
                            </span>
                          </div>
                        </td>
                        <td>
                          <select name="product_barcodes[{{ barcode_row }}][barcode_type]" class="form-control">
                            <option value="EAN13"{% if barcode.barcode_type == 'EAN13' %} selected="selected"{% endif %}>{{ text_barcode_ean13 }}</option>
                            <option value="EAN8"{% if barcode.barcode_type == 'EAN8' %} selected="selected"{% endif %}>{{ text_barcode_ean8 }}</option>
                            <option value="UPC"{% if barcode.barcode_type == 'UPC' %} selected="selected"{% endif %}>{{ text_barcode_upc }}</option>
                            <option value="CODE128"{% if barcode.barcode_type == 'CODE128' %} selected="selected"{% endif %}>{{ text_barcode_code128 }}</option>
                            <option value="CODE39"{% if barcode.barcode_type == 'CODE39' %} selected="selected"{% endif %}>{{ text_barcode_code39 }}</option>
                            <option value="ISBN"{% if barcode.barcode_type == 'ISBN' %} selected="selected"{% endif %}>{{ text_barcode_isbn }}</option>
                          </select>
                        </td>
                        <td>
                          <select name="product_barcodes[{{ barcode_row }}][unit_id]" class="form-control">
                            <option value="0">{{ text_none }}</option>
                            {% for unit in units %}
                            <option value="{{ unit.unit_id }}"{% if unit.unit_id == barcode.unit_id %} selected="selected"{% endif %}>{{ unit.name }}</option>
                            {% endfor %}
                          </select>
                        </td>
                        <td>
                          <select name="product_barcodes[{{ barcode_row }}][option_id]" class="form-control">
                            <option value="0">{{ text_none }}</option>
                            {% for option in options %}
                            <option value="{{ option.option_id }}"{% if option.option_id == barcode.option_id %} selected="selected"{% endif %}>{{ option.name }}</option>
                            {% endfor %}
                          </select>
                        </td>
                        <td>
                          <input type="checkbox" name="product_barcodes[{{ barcode_row }}][is_primary]" value="1"{% if barcode.is_primary %} checked="checked"{% endif %} />
                        </td>
                        <td>
                          <div class="btn-group">
                            <button type="button" onclick="$('#barcode-row{{ barcode_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa-minus-circle"></i></button>
                            <button type="button" onclick="printBarcode({{ barcode_row }});" data-toggle="tooltip" title="{{ button_print_barcode }}" class="btn btn-info btn-sm"><i class="fa fa-print"></i></button>
                          </div>
                        </td>
                      </tr>
                      {% set barcode_row = barcode_row + 1 %}
                      {% endfor %}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب إدارة الدفعات وتواريخ الانتهاء -->
          <div class="tab-pane" id="tab-batches">
            <div class="panel panel-warning">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-calendar-check"></i> إدارة الدفعات وتواريخ الانتهاء</h3>
                <div class="panel-tools">
                  <button type="button" id="button-batch-add" class="btn btn-primary btn-sm">
                    <i class="fa fa-plus"></i> إضافة دفعة جديدة
                  </button>
                  <button type="button" id="button-expiry-report" class="btn btn-info btn-sm">
                    <i class="fa fa-exclamation-triangle"></i> تقرير انتهاء الصلاحية
                  </button>
                </div>
              </div>
              <div class="panel-body">
                <!-- إعدادات FIFO -->
                <div class="alert alert-info">
                  <h4><i class="fa fa-info-circle"></i> نظام FIFO (الوارد أولاً يصرف أولاً)</h4>
                  <p>يتم صرف المنتجات تلقائياً حسب تاريخ الانتهاء الأقرب لضمان عدم انتهاء صلاحية المخزون.</p>
                  <label class="checkbox-inline">
                    <input type="checkbox" name="enable_fifo" value="1" {{ enable_fifo ? 'checked' : '' }}>
                    تفعيل نظام FIFO التلقائي
                  </label>
                </div>

                <!-- جدول الدفعات -->
                <div class="table-responsive">
                  <table id="batches-table" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <th>رقم الدفعة</th>
                        <th>تاريخ الإنتاج</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الكمية</th>
                        <th>تكلفة الوحدة</th>
                        <th>المورد</th>
                        <th>الحالة</th>
                        <th>أيام متبقية</th>
                        <th>الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% for batch in product_batches %}
                      <tr id="batch-row-{{ batch.batch_id }}" class="
                        {% if batch.days_to_expiry <= 7 %}danger
                        {% elseif batch.days_to_expiry <= 30 %}warning
                        {% endif %}">
                        <td>
                          <strong>{{ batch.batch_number }}</strong>
                          {% if batch.is_expired %}
                            <span class="label label-danger">منتهي الصلاحية</span>
                          {% endif %}
                        </td>
                        <td>{{ batch.manufacturing_date }}</td>
                        <td>{{ batch.expiry_date }}</td>
                        <td>
                          <span class="badge badge-info">{{ batch.quantity }}</span>
                        </td>
                        <td>{{ batch.cost_price }}</td>
                        <td>{{ batch.supplier_name ?: 'غير محدد' }}</td>
                        <td>
                          <span class="label label-{{ batch.status ? 'success' : 'danger' }}">
                            {{ batch.status ? 'نشط' : 'معطل' }}
                          </span>
                        </td>
                        <td>
                          {% if batch.expiry_date != '0000-00-00' %}
                            {% if batch.days_to_expiry < 0 %}
                              <span class="text-danger">
                                <i class="fa fa-exclamation-triangle"></i>
                                منتهي منذ {{ batch.days_to_expiry|abs }} يوم
                              </span>
                            {% elseif batch.days_to_expiry <= 7 %}
                              <span class="text-danger">
                                <i class="fa fa-clock"></i>
                                {{ batch.days_to_expiry }} يوم
                              </span>
                            {% elseif batch.days_to_expiry <= 30 %}
                              <span class="text-warning">
                                <i class="fa fa-clock"></i>
                                {{ batch.days_to_expiry }} يوم
                              </span>
                            {% else %}
                              <span class="text-success">
                                <i class="fa fa-check"></i>
                                {{ batch.days_to_expiry }} يوم
                              </span>
                            {% endif %}
                          {% else %}
                            <span class="text-muted">لا ينتهي</span>
                          {% endif %}
                        </td>
                        <td>
                          <div class="btn-group">
                            <button type="button" class="btn btn-info btn-sm" onclick="editBatch({{ batch.batch_id }})">
                              <i class="fa fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="adjustBatchQuantity({{ batch.batch_id }})">
                              <i class="fa fa-balance-scale"></i>
                            </button>
                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteBatch({{ batch.batch_id }})">
                              <i class="fa fa-trash"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                      {% endfor %}
                    </tbody>
                  </table>
                </div>

                <!-- إحصائيات الدفعات -->
                <div class="row">
                  <div class="col-md-3">
                    <div class="info-box bg-green">
                      <span class="info-box-icon"><i class="fa fa-check"></i></span>
                      <div class="info-box-content">
                        <span class="info-box-text">دفعات صالحة</span>
                        <span class="info-box-number">{{ batch_stats.valid_batches }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="info-box bg-yellow">
                      <span class="info-box-icon"><i class="fa fa-exclamation-triangle"></i></span>
                      <div class="info-box-content">
                        <span class="info-box-text">قاربة الانتهاء</span>
                        <span class="info-box-number">{{ batch_stats.expiring_batches }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="info-box bg-red">
                      <span class="info-box-icon"><i class="fa fa-times"></i></span>
                      <div class="info-box-content">
                        <span class="info-box-text">منتهية الصلاحية</span>
                        <span class="info-box-number">{{ batch_stats.expired_batches }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="info-box bg-blue">
                      <span class="info-box-icon"><i class="fa fa-cubes"></i></span>
                      <div class="info-box-content">
                        <span class="info-box-text">إجمالي الكمية</span>
                        <span class="info-box-number">{{ batch_stats.total_quantity }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب الخيارات المرتبطة بالوحدات -->
          <div class="tab-pane" id="tab-options">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-list-alt"></i> {{ text_product_options_linked }}</h3>
                <div class="panel-tools">
                  <button type="button" id="button-option-add" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> {{ button_add_option }}</button>
                </div>
              </div>
              <div class="panel-body">
                <div class="alert alert-warning">
                  <i class="fa fa-star"></i> <strong>{{ text_unique_feature }}:</strong> {{ help_options_linked_units }}
                </div>
                <div id="options-container">
                  {% set option_row = 0 %}
                  {% for product_option in product_options %}
                  <div class="panel panel-info" id="option-row{{ option_row }}">
                    <div class="panel-heading">
                      <h4 class="panel-title">
                        {{ product_option.name }}
                        <button type="button" class="btn btn-danger btn-xs pull-right" onclick="$('#option-row{{ option_row }}').remove();"><i class="fa fa-trash"></i></button>
                      </h4>
                    </div>
                    <div class="panel-body">
                      <div class="row">
                        <div class="col-md-4">
                          <div class="form-group">
                            <label>{{ entry_option }}</label>
                            <select name="product_options[{{ option_row }}][option_id]" class="form-control">
                              {% for option in options %}
                              <option value="{{ option.option_id }}"{% if option.option_id == product_option.option_id %} selected="selected"{% endif %}>{{ option.name }}</option>
                              {% endfor %}
                            </select>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-group">
                            <label>{{ entry_linked_unit }}</label>
                            <select name="product_options[{{ option_row }}][unit_id]" class="form-control">
                              <option value="0">{{ text_all_units }}</option>
                              {% for unit in units %}
                              <option value="{{ unit.unit_id }}"{% if unit.unit_id == product_option.unit_id %} selected="selected"{% endif %}>{{ unit.name }}</option>
                              {% endfor %}
                            </select>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-group">
                            <label>{{ entry_required }}</label>
                            <select name="product_options[{{ option_row }}][required]" class="form-control">
                              <option value="1"{% if product_option.required %} selected="selected"{% endif %}>{{ text_yes }}</option>
                              <option value="0"{% if not product_option.required %} selected="selected"{% endif %}>{{ text_no }}</option>
                            </select>
                          </div>
                        </div>
                      </div>

                      <!-- قيم الخيارات -->
                      <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                          <thead>
                            <tr>
                              <th>{{ column_option_value }}</th>
                              <th>{{ column_quantity }}</th>
                              <th>{{ column_price_effect }}</th>
                              <th>{{ column_weight_effect }}</th>
                              <th>{{ column_action }}</th>
                            </tr>
                          </thead>
                          <tbody id="option-value-{{ option_row }}">
                            {% set option_value_row = 0 %}
                            {% for option_value in product_option.product_option_value %}
                            <tr id="option-value-row{{ option_row }}-{{ option_value_row }}">
                              <td>{{ option_value.name }}</td>
                              <td><input type="text" name="product_options[{{ option_row }}][product_option_value][{{ option_value_row }}][quantity]" value="{{ option_value.quantity }}" class="form-control" /></td>
                              <td>
                                <div class="input-group">
                                  <span class="input-group-addon">
                                    <select name="product_options[{{ option_row }}][product_option_value][{{ option_value_row }}][price_prefix]">
                                      <option value="+"{% if option_value.price_prefix == '+' %} selected="selected"{% endif %}>+</option>
                                      <option value="-"{% if option_value.price_prefix == '-' %} selected="selected"{% endif %}>-</option>
                                    </select>
                                  </span>
                                  <input type="text" name="product_options[{{ option_row }}][product_option_value][{{ option_value_row }}][price]" value="{{ option_value.price }}" class="form-control" />
                                </div>
                              </td>
                              <td>
                                <div class="input-group">
                                  <span class="input-group-addon">
                                    <select name="product_options[{{ option_row }}][product_option_value][{{ option_value_row }}][weight_prefix]">
                                      <option value="+"{% if option_value.weight_prefix == '+' %} selected="selected"{% endif %}>+</option>
                                      <option value="-"{% if option_value.weight_prefix == '-' %} selected="selected"{% endif %}>-</option>
                                    </select>
                                  </span>
                                  <input type="text" name="product_options[{{ option_row }}][product_option_value][{{ option_value_row }}][weight]" value="{{ option_value.weight }}" class="form-control" />
                                </div>
                              </td>
                              <td><button type="button" onclick="$(this).closest('tr').remove();" class="btn btn-danger btn-sm"><i class="fa fa-minus-circle"></i></button></td>
                            </tr>
                            {% set option_value_row = option_value_row + 1 %}
                            {% endfor %}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                  {% set option_row = option_row + 1 %}
                  {% endfor %}
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب الباقات والعروض -->
          <div class="tab-pane" id="tab-bundles">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-cubes"></i> {{ text_bundles_offers }}</h3>
                <div class="panel-tools">
                  <button type="button" id="button-bundle-add" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> {{ button_add_bundle }}</button>
                </div>
              </div>
              <div class="panel-body">
                <div class="alert alert-info">
                  <i class="fa fa-info-circle"></i> {{ help_bundles_offers }}
                </div>
                <div id="bundles-container">
                  {% set bundle_row = 0 %}
                  {% for bundle in product_bundles %}
                  <div class="panel panel-success" id="bundle-row{{ bundle_row }}">
                    <div class="panel-heading">
                      <h4 class="panel-title">
                        {{ bundle.name }}
                        <button type="button" class="btn btn-danger btn-xs pull-right" onclick="$('#bundle-row{{ bundle_row }}').remove();"><i class="fa fa-trash"></i></button>
                      </h4>
                    </div>
                    <div class="panel-body">
                      <div class="row">
                        <div class="col-md-6">
                          <div class="form-group">
                            <label>{{ entry_bundle_name }}</label>
                            <input type="text" name="product_bundles[{{ bundle_row }}][name]" value="{{ bundle.name }}" class="form-control" />
                          </div>
                        </div>
                        <div class="col-md-3">
                          <div class="form-group">
                            <label>{{ entry_discount_type }}</label>
                            <select name="product_bundles[{{ bundle_row }}][discount_type]" class="form-control">
                              <option value="percentage"{% if bundle.discount_type == 'percentage' %} selected="selected"{% endif %}>{{ text_percentage }}</option>
                              <option value="fixed"{% if bundle.discount_type == 'fixed' %} selected="selected"{% endif %}>{{ text_fixed_amount }}</option>
                            </select>
                          </div>
                        </div>
                        <div class="col-md-3">
                          <div class="form-group">
                            <label>{{ entry_discount_value }}</label>
                            <input type="text" name="product_bundles[{{ bundle_row }}][discount_value]" value="{{ bundle.discount_value }}" class="form-control" />
                          </div>
                        </div>
                      </div>

                      <!-- عناصر الباقة -->
                      <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                          <thead>
                            <tr>
                              <th>{{ column_product }}</th>
                              <th>{{ column_quantity }}</th>
                              <th>{{ column_unit }}</th>
                              <th>{{ column_is_free }}</th>
                              <th>{{ column_action }}</th>
                            </tr>
                          </thead>
                          <tbody id="bundle-items-{{ bundle_row }}">
                            {% for item in bundle.items %}
                            <tr>
                              <td>{{ item.product_name }}</td>
                              <td><input type="text" name="product_bundles[{{ bundle_row }}][items][{{ loop.index0 }}][quantity]" value="{{ item.quantity }}" class="form-control" /></td>
                              <td>{{ item.unit_name }}</td>
                              <td><input type="checkbox" name="product_bundles[{{ bundle_row }}][items][{{ loop.index0 }}][is_free]" value="1"{% if item.is_free %} checked="checked"{% endif %} /></td>
                              <td><button type="button" onclick="$(this).closest('tr').remove();" class="btn btn-danger btn-sm"><i class="fa fa-minus-circle"></i></button></td>
                            </tr>
                            {% endfor %}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                  {% set bundle_row = bundle_row + 1 %}
                  {% endfor %}
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب الخصومات المتقدمة -->
          <div class="tab-pane" id="tab-discounts">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-percent"></i> {{ text_advanced_discounts }}</h3>
                <div class="panel-tools">
                  <button type="button" id="button-discount-add" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> {{ button_add_discount }}</button>
                </div>
              </div>
              <div class="panel-body">
                <div class="alert alert-info">
                  <i class="fa fa-info-circle"></i> {{ help_advanced_discounts }}
                </div>
                <div class="table-responsive">
                  <table id="discounts-table" class="table table-striped table-bordered table-hover">
                    <thead>
                      <tr>
                        <th>{{ column_discount_name }}</th>
                        <th>{{ column_discount_type }}</th>
                        <th>{{ column_buy_quantity }}</th>
                        <th>{{ column_get_quantity }}</th>
                        <th>{{ column_discount_value }}</th>
                        <th>{{ column_unit }}</th>
                        <th>{{ column_date_range }}</th>
                        <th>{{ column_action }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% set discount_row = 0 %}
                      {% for discount in product_discounts %}
                      <tr id="discount-row{{ discount_row }}">
                        <td><input type="text" name="product_discounts[{{ discount_row }}][name]" value="{{ discount.name }}" class="form-control" /></td>
                        <td>
                          <select name="product_discounts[{{ discount_row }}][type]" class="form-control">
                            <option value="buy_x_get_y"{% if discount.type == 'buy_x_get_y' %} selected="selected"{% endif %}>{{ text_buy_x_get_y }}</option>
                            <option value="buy_x_get_discount"{% if discount.type == 'buy_x_get_discount' %} selected="selected"{% endif %}>{{ text_buy_x_get_discount }}</option>
                            <option value="quantity_discount"{% if discount.type == 'quantity_discount' %} selected="selected"{% endif %}>{{ text_quantity_discount }}</option>
                          </select>
                        </td>
                        <td><input type="text" name="product_discounts[{{ discount_row }}][buy_quantity]" value="{{ discount.buy_quantity }}" class="form-control" /></td>
                        <td><input type="text" name="product_discounts[{{ discount_row }}][get_quantity]" value="{{ discount.get_quantity }}" class="form-control" /></td>
                        <td>
                          <div class="input-group">
                            <input type="text" name="product_discounts[{{ discount_row }}][discount_value]" value="{{ discount.discount_value }}" class="form-control" />
                            <span class="input-group-addon">
                              <select name="product_discounts[{{ discount_row }}][discount_type]">
                                <option value="percentage"{% if discount.discount_type == 'percentage' %} selected="selected"{% endif %}>%</option>
                                <option value="fixed"{% if discount.discount_type == 'fixed' %} selected="selected"{% endif %}>{{ text_currency }}</option>
                              </select>
                            </span>
                          </div>
                        </td>
                        <td>
                          <select name="product_discounts[{{ discount_row }}][unit_id]" class="form-control">
                            <option value="0">{{ text_all_units }}</option>
                            {% for unit in units %}
                            <option value="{{ unit.unit_id }}"{% if unit.unit_id == discount.unit_id %} selected="selected"{% endif %}>{{ unit.name }}</option>
                            {% endfor %}
                          </select>
                        </td>
                        <td>
                          <div class="input-group">
                            <input type="text" name="product_discounts[{{ discount_row }}][date_start]" value="{{ discount.date_start }}" class="form-control date" />
                            <span class="input-group-addon">{{ text_to }}</span>
                            <input type="text" name="product_discounts[{{ discount_row }}][date_end]" value="{{ discount.date_end }}" class="form-control date" />
                          </div>
                        </td>
                        <td><button type="button" onclick="$('#discount-row{{ discount_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa-minus-circle"></i></button></td>
                      </tr>
                      {% set discount_row = discount_row + 1 %}
                      {% endfor %}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب إدارة المخزون -->
          <div class="tab-pane" id="tab-inventory">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-warehouse"></i> {{ text_inventory_management }}</h3>
              </div>
              <div class="panel-body">
                <div class="row">
                  <div class="col-md-6">
                    <div class="panel panel-info">
                      <div class="panel-heading">
                        <h4 class="panel-title">{{ text_stock_settings }}</h4>
                      </div>
                      <div class="panel-body">
                        <div class="form-group">
                          <label class="col-sm-4 control-label" for="input-minimum">{{ entry_minimum }}</label>
                          <div class="col-sm-8">
                            <input type="text" name="minimum" value="{{ minimum }}" placeholder="{{ entry_minimum }}" id="input-minimum" class="form-control" />
                            <div class="help-block">{{ help_minimum }}</div>
                          </div>
                        </div>
                        <div class="form-group">
                          <label class="col-sm-4 control-label" for="input-subtract">{{ entry_subtract }}</label>
                          <div class="col-sm-8">
                            <label class="radio-inline">
                              <input type="radio" name="subtract" value="1"{% if subtract %} checked="checked"{% endif %} />
                              {{ text_yes }}
                            </label>
                            <label class="radio-inline">
                              <input type="radio" name="subtract" value="0"{% if not subtract %} checked="checked"{% endif %} />
                              {{ text_no }}
                            </label>
                          </div>
                        </div>
                        <div class="form-group">
                          <label class="col-sm-4 control-label" for="input-stock-status">{{ entry_stock_status }}</label>
                          <div class="col-sm-8">
                            <select name="stock_status_id" id="input-stock-status" class="form-control">
                              {% for stock_status in stock_statuses %}
                              <option value="{{ stock_status.stock_status_id }}"{% if stock_status.stock_status_id == stock_status_id %} selected="selected"{% endif %}>{{ stock_status.name }}</option>
                              {% endfor %}
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="panel panel-warning">
                      <div class="panel-heading">
                        <h4 class="panel-title">{{ text_branch_inventory }}</h4>
                      </div>
                      <div class="panel-body">
                        <div class="table-responsive">
                          <table class="table table-striped table-bordered">
                            <thead>
                              <tr>
                                <th>{{ column_branch }}</th>
                                <th>{{ column_quantity }}</th>
                                <th>{{ column_location }}</th>
                              </tr>
                            </thead>
                            <tbody>
                              {% for branch in branches %}
                              <tr>
                                <td>{{ branch.name }}</td>
                                <td><input type="text" name="branch_inventory[{{ branch.branch_id }}][quantity]" value="{{ branch_inventory[branch.branch_id] ? branch_inventory[branch.branch_id].quantity : '0' }}" class="form-control" /></td>
                                <td><input type="text" name="branch_inventory[{{ branch.branch_id }}][location]" value="{{ branch_inventory[branch.branch_id] ? branch_inventory[branch.branch_id].location : '' }}" class="form-control" /></td>
                              </tr>
                              {% endfor %}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- تبويب SEO -->
          <div class="tab-pane" id="tab-seo">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-search"></i> {{ text_seo_optimization }}</h3>
              </div>
              <div class="panel-body">
                <ul class="nav nav-tabs" id="seo-language">
                  {% for language in languages %}
                  <li{% if loop.first %} class="active"{% endif %}><a href="#seo-language{{ language.language_id }}" data-toggle="tab"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}</a></li>
                  {% endfor %}
                </ul>
                <div class="tab-content">
                  {% for language in languages %}
                  <div class="tab-pane{% if loop.first %} active{% endif %}" id="seo-language{{ language.language_id }}">
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-meta-title{{ language.language_id }}">{{ entry_meta_title }}</label>
                      <div class="col-sm-10">
                        <input type="text" name="product_description[{{ language.language_id }}][meta_title]" value="{{ product_description[language.language_id] ? product_description[language.language_id].meta_title }}" placeholder="{{ entry_meta_title }}" id="input-meta-title{{ language.language_id }}" class="form-control" />
                        <div class="help-block">{{ help_meta_title }}</div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-meta-description{{ language.language_id }}">{{ entry_meta_description }}</label>
                      <div class="col-sm-10">
                        <textarea name="product_description[{{ language.language_id }}][meta_description]" rows="5" placeholder="{{ entry_meta_description }}" id="input-meta-description{{ language.language_id }}" class="form-control">{{ product_description[language.language_id] ? product_description[language.language_id].meta_description }}</textarea>
                        <div class="help-block">{{ help_meta_description }}</div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="col-sm-2 control-label" for="input-meta-keyword{{ language.language_id }}">{{ entry_meta_keyword }}</label>
                      <div class="col-sm-10">
                        <textarea name="product_description[{{ language.language_id }}][meta_keyword]" rows="5" placeholder="{{ entry_meta_keyword }}" id="input-meta-keyword{{ language.language_id }}" class="form-control">{{ product_description[language.language_id] ? product_description[language.language_id].meta_keyword }}</textarea>
                        <div class="help-block">{{ help_meta_keyword }}</div>
                      </div>
                    </div>
                  </div>
                  {% endfor %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

<script type="text/javascript">
var pricing_row = {{ pricing_row }};
var unit_row = {{ unit_row }};

// إضافة صف تسعير جديد
$('#button-pricing-add').on('click', function() {
    html = '<tr id="pricing-row' + pricing_row + '">';
    html += '  <td><select name="product_pricing[' + pricing_row + '][unit_id]" class="form-control">';
    {% for unit in units %}
    html += '    <option value="{{ unit.unit_id }}">{{ unit.name }}</option>';
    {% endfor %}
    html += '  </select></td>';
    html += '  <td><input type="text" name="product_pricing[' + pricing_row + '][cost_price]" value="" placeholder="{{ column_cost_price }}" class="form-control cost-price" /></td>';
    html += '  <td><input type="text" name="product_pricing[' + pricing_row + '][base_price]" value="" placeholder="{{ column_base_price }}" class="form-control base-price" /></td>';
    html += '  <td><input type="text" name="product_pricing[' + pricing_row + '][special_price]" value="" placeholder="{{ column_special_price }}" class="form-control" /></td>';
    html += '  <td><input type="text" name="product_pricing[' + pricing_row + '][wholesale_price]" value="" placeholder="{{ column_wholesale_price }}" class="form-control" /></td>';
    html += '  <td><input type="text" name="product_pricing[' + pricing_row + '][half_wholesale_price]" value="" placeholder="{{ column_half_wholesale_price }}" class="form-control" /></td>';
    html += '  <td><input type="text" name="product_pricing[' + pricing_row + '][custom_price]" value="" placeholder="{{ column_custom_price }}" class="form-control" /></td>';
    html += '  <td><span class="profit-margin">0%</span></td>';
    html += '  <td><button type="button" onclick="$(\'#pricing-row' + pricing_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
    html += '</tr>';

    $('#pricing-table tbody').append(html);

    pricing_row++;
});

// حساب هامش الربح تلقائياً
$(document).on('keyup', '.cost-price, .base-price', function() {
    var row = $(this).closest('tr');
    var cost_price = parseFloat(row.find('.cost-price').val()) || 0;
    var base_price = parseFloat(row.find('.base-price').val()) || 0;

    if (cost_price > 0) {
        var profit_margin = ((base_price - cost_price) / cost_price * 100).toFixed(2);
        row.find('.profit-margin').text(profit_margin + '%');
    }
});

// إضافة صف وحدة جديد
$('#button-unit-add').on('click', function() {
    html = '<tr id="unit-row' + unit_row + '">';
    html += '  <td><select name="product_units[' + unit_row + '][unit_id]" class="form-control">';
    {% for unit in units %}
    html += '    <option value="{{ unit.unit_id }}">{{ unit.name }}</option>';
    {% endfor %}
    html += '  </select></td>';
    html += '  <td><select name="product_units[' + unit_row + '][unit_type]" class="form-control">';
    html += '    <option value="base">{{ text_base_unit }}</option>';
    html += '    <option value="sub">{{ text_sub_unit }}</option>';
    html += '    <option value="super">{{ text_super_unit }}</option>';
    html += '  </select></td>';
    html += '  <td><input type="text" name="product_units[' + unit_row + '][conversion_factor]" value="1" placeholder="{{ column_conversion_factor }}" class="form-control" /></td>';
    html += '  <td><input type="checkbox" name="product_units[' + unit_row + '][is_base_unit]" value="1" /></td>';
    html += '  <td><input type="text" name="product_units[' + unit_row + '][sort_order]" value="0" placeholder="{{ column_sort_order }}" class="form-control" /></td>';
    html += '  <td><button type="button" onclick="$(\'#unit-row' + unit_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
    html += '</tr>';

    $('#units-table tbody').append(html);

    unit_row++;
});

// توليد كود المنتج التلقائي
$('#button-generate-code').on('click', function() {
    var category_id = $('input[name="product_category[]"]:first').val() || 0;
    var manufacturer_id = $('input[name="manufacturer_id"]').val() || 0;

    $.ajax({
        url: 'index.php?route=inventory/product/generateCode&user_token={{ user_token }}&category_id=' + category_id + '&manufacturer_id=' + manufacturer_id,
        dataType: 'json',
        success: function(json) {
            $('#input-model').val(json.code);
        }
    });
});

// إضافة صف باركود جديد
var barcode_row = {{ barcode_row }};

$('#button-barcode-add').on('click', function() {
    html = '<tr id="barcode-row' + barcode_row + '">';
    html += '  <td>';
    html += '    <div class="input-group">';
    html += '      <input type="text" name="product_barcodes[' + barcode_row + '][barcode]" value="" placeholder="{{ column_barcode }}" class="form-control" />';
    html += '      <span class="input-group-btn">';
    html += '        <button type="button" class="btn btn-info" onclick="generateBarcode(' + barcode_row + ');"><i class="fa fa-magic"></i></button>';
    html += '      </span>';
    html += '    </div>';
    html += '  </td>';
    html += '  <td>';
    html += '    <select name="product_barcodes[' + barcode_row + '][barcode_type]" class="form-control">';
    html += '      <option value="EAN13">{{ text_barcode_ean13 }}</option>';
    html += '      <option value="EAN8">{{ text_barcode_ean8 }}</option>';
    html += '      <option value="UPC">{{ text_barcode_upc }}</option>';
    html += '      <option value="CODE128">{{ text_barcode_code128 }}</option>';
    html += '      <option value="CODE39">{{ text_barcode_code39 }}</option>';
    html += '      <option value="ISBN">{{ text_barcode_isbn }}</option>';
    html += '    </select>';
    html += '  </td>';
    html += '  <td>';
    html += '    <select name="product_barcodes[' + barcode_row + '][unit_id]" class="form-control">';
    html += '      <option value="0">{{ text_none }}</option>';
    {% for unit in units %}
    html += '      <option value="{{ unit.unit_id }}">{{ unit.name }}</option>';
    {% endfor %}
    html += '    </select>';
    html += '  </td>';
    html += '  <td>';
    html += '    <select name="product_barcodes[' + barcode_row + '][option_id]" class="form-control">';
    html += '      <option value="0">{{ text_none }}</option>';
    {% for option in options %}
    html += '      <option value="{{ option.option_id }}">{{ option.name }}</option>';
    {% endfor %}
    html += '    </select>';
    html += '  </td>';
    html += '  <td><input type="checkbox" name="product_barcodes[' + barcode_row + '][is_primary]" value="1" /></td>';
    html += '  <td>';
    html += '    <div class="btn-group">';
    html += '      <button type="button" onclick="$(\'#barcode-row' + barcode_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa-minus-circle"></i></button>';
    html += '      <button type="button" onclick="printBarcode(' + barcode_row + ');" data-toggle="tooltip" title="{{ button_print_barcode }}" class="btn btn-info btn-sm"><i class="fa fa-print"></i></button>';
    html += '    </div>';
    html += '  </td>';
    html += '</tr>';

    $('#barcode-table tbody').append(html);

    barcode_row++;
});

// إضافة صف خصم جديد
var discount_row = {{ discount_row }};

$('#button-discount-add').on('click', function() {
    html = '<tr id="discount-row' + discount_row + '">';
    html += '  <td><input type="text" name="product_discounts[' + discount_row + '][name]" value="" class="form-control" /></td>';
    html += '  <td>';
    html += '    <select name="product_discounts[' + discount_row + '][type]" class="form-control">';
    html += '      <option value="buy_x_get_y">{{ text_buy_x_get_y }}</option>';
    html += '      <option value="buy_x_get_discount">{{ text_buy_x_get_discount }}</option>';
    html += '      <option value="quantity_discount">{{ text_quantity_discount }}</option>';
    html += '    </select>';
    html += '  </td>';
    html += '  <td><input type="text" name="product_discounts[' + discount_row + '][buy_quantity]" value="1" class="form-control" /></td>';
    html += '  <td><input type="text" name="product_discounts[' + discount_row + '][get_quantity]" value="1" class="form-control" /></td>';
    html += '  <td>';
    html += '    <div class="input-group">';
    html += '      <input type="text" name="product_discounts[' + discount_row + '][discount_value]" value="0" class="form-control" />';
    html += '      <span class="input-group-addon">';
    html += '        <select name="product_discounts[' + discount_row + '][discount_type]">';
    html += '          <option value="percentage">%</option>';
    html += '          <option value="fixed">{{ text_currency }}</option>';
    html += '        </select>';
    html += '      </span>';
    html += '    </div>';
    html += '  </td>';
    html += '  <td>';
    html += '    <select name="product_discounts[' + discount_row + '][unit_id]" class="form-control">';
    html += '      <option value="0">{{ text_all_units }}</option>';
    {% for unit in units %}
    html += '      <option value="{{ unit.unit_id }}">{{ unit.name }}</option>';
    {% endfor %}
    html += '    </select>';
    html += '  </td>';
    html += '  <td>';
    html += '    <div class="input-group">';
    html += '      <input type="text" name="product_discounts[' + discount_row + '][date_start]" value="" class="form-control date" />';
    html += '      <span class="input-group-addon">{{ text_to }}</span>';
    html += '      <input type="text" name="product_discounts[' + discount_row + '][date_end]" value="" class="form-control date" />';
    html += '    </div>';
    html += '  </td>';
    html += '  <td><button type="button" onclick="$(\'#discount-row' + discount_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa-minus-circle"></i></button></td>';
    html += '</tr>';

    $('#discounts-table tbody').append(html);

    // تهيئة التواريخ للصف الجديد
    $('#discount-row' + discount_row + ' .date').datetimepicker({
        language: '{{ datepicker }}',
        pickTime: false
    });

    discount_row++;
});

// توليد باركود تلقائي
function generateBarcode(row) {
    var product_id = $('input[name="product_id"]').val() || 0;
    var unit_id = $('select[name="product_barcodes[' + row + '][unit_id]"]').val() || 0;
    var option_id = $('select[name="product_barcodes[' + row + '][option_id]"]').val() || 0;
    var barcode_type = $('select[name="product_barcodes[' + row + '][barcode_type]"]').val();

    $.ajax({
        url: 'index.php?route=inventory/product/generateBarcode&user_token={{ user_token }}',
        type: 'POST',
        data: {
            product_id: product_id,
            unit_id: unit_id,
            option_id: option_id,
            barcode_type: barcode_type
        },
        dataType: 'json',
        success: function(json) {
            if (json.barcode) {
                $('input[name="product_barcodes[' + row + '][barcode]"]').val(json.barcode);
            }
        }
    });
}

// طباعة باركود
function printBarcode(row) {
    var barcode = $('input[name="product_barcodes[' + row + '][barcode]"]').val();
    var barcode_type = $('select[name="product_barcodes[' + row + '][barcode_type]"]').val();

    if (barcode) {
        window.open('index.php?route=inventory/barcode_print&user_token={{ user_token }}&barcode=' + barcode + '&type=' + barcode_type, '_blank');
    } else {
        alert('{{ error_barcode_empty }}');
    }
}

// البحث التلقائي للتصنيفات
$('input[name=\'category\']').autocomplete({
    'source': function(request, response) {
        $.ajax({
            url: 'index.php?route=inventory/category/autocomplete&user_token={{ user_token }}&filter_name=' +  encodeURIComponent(request),
            dataType: 'json',
            success: function(json) {
                response($.map(json, function(item) {
                    return {
                        label: item['name'],
                        value: item['category_id']
                    }
                }));
            }
        });
    },
    'select': function(item) {
        $('input[name=\'category\']').val('');

        $('#product-category' + item['value']).remove();

        $('#product-category').append('<div id="product-category' + item['value'] + '"><i class="fa fa-minus-circle"></i> ' + item['label'] + '<input type="hidden" name="product_category[]" value="' + item['value'] + '" /></div>');
    }
});

$('#product-category').delegate('.fa-minus-circle', 'click', function() {
    $(this).parent().remove();
});

// البحث التلقائي للعلامات التجارية
$('input[name=\'manufacturer\']').autocomplete({
    'source': function(request, response) {
        $.ajax({
            url: 'index.php?route=inventory/manufacturer/autocomplete&user_token={{ user_token }}&filter_name=' +  encodeURIComponent(request),
            dataType: 'json',
            success: function(json) {
                response($.map(json, function(item) {
                    return {
                        label: item['name'],
                        value: item['manufacturer_id']
                    }
                }));
            }
        });
    },
    'select': function(item) {
        $('input[name=\'manufacturer\']').val(item['label']);
        $('input[name=\'manufacturer_id\']').val(item['value']);
    }
});

// تهيئة التواريخ
$('#input-date-available').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

$('.date').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

// تهيئة محرر النصوص
$('textarea[data-oc-toggle=\'ckeditor\']').each(function() {
    CKEDITOR.replace($(this).attr('id'), {
        filebrowserBrowseUrl: 'index.php?route=common/filemanager&user_token={{ user_token }}',
        filebrowserImageBrowseUrl: 'index.php?route=common/filemanager&user_token={{ user_token }}',
        filebrowserFlashBrowseUrl: 'index.php?route=common/filemanager&user_token={{ user_token }}',
        filebrowserUploadUrl: 'index.php?route=common/filemanager&user_token={{ user_token }}',
        filebrowserImageUploadUrl: 'index.php?route=common/filemanager&user_token={{ user_token }}',
        filebrowserFlashUploadUrl: 'index.php?route=common/filemanager&user_token={{ user_token }}'
    });
});

// تهيئة اختيار الصور
$('[data-toggle=\'image\']').on('click', function(e) {
    var $button = $(this);
    var $icon   = $button.find('> i');

    $('#modal-image').remove();

    $.ajax({
        url: 'index.php?route=common/filemanager&user_token={{ user_token }}&target=' + $button.parent().find('input').attr('id') + '&thumb=' + $button.attr('id'),
        dataType: 'html',
        beforeSend: function() {
            $icon.replaceWith('<i class="fa fa-circle-o-notch fa-spin"></i>');
        },
        complete: function() {
            $icon.replaceWith('<i class="fa fa-upload"></i>');
        },
        success: function(html) {
            $('body').append('<div id="modal-image" class="modal">' + html + '</div>');

            $('#modal-image').modal('show');
        }
    });

    e.preventDefault();
});

// التحقق من صحة النموذج قبل الإرسال
$('#form-product').on('submit', function(e) {
    var valid = true;
    var errors = [];

    // التحقق من اسم المنتج
    {% for language in languages %}
    if (!$('input[name="product_description[{{ language.language_id }}][name]"]').val()) {
        errors.push('{{ error_name[language.language_id] }}');
        valid = false;
    }
    {% endfor %}

    // التحقق من الموديل
    if (!$('input[name="model"]').val()) {
        errors.push('{{ error_model }}');
        valid = false;
    }

    if (!valid) {
        alert(errors.join('\n'));
        e.preventDefault();
        return false;
    }
});

// ===== الميزات الجديدة المتقدمة =====

// إدارة المقاسات
$('#button-size-add').on('click', function() {
    var sizeName = prompt('اسم المقاس:');
    var sizeCode = prompt('كود المقاس (اختياري):');

    if (sizeName) {
        $.ajax({
            url: 'index.php?route=inventory/product/addSize&user_token={{ user_token }}',
            type: 'POST',
            data: {
                size_name: sizeName,
                size_code: sizeCode || '',
                sort_order: $('#sizes-table tbody tr').length + 1
            },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
});

function editSize(sizeId) {
    var row = $('#size-row-' + sizeId);
    var currentName = row.find('td:first').text();
    var newName = prompt('اسم المقاس:', currentName);

    if (newName && newName !== currentName) {
        $.ajax({
            url: 'index.php?route=inventory/product/editSize&user_token={{ user_token }}&size_id=' + sizeId,
            type: 'POST',
            data: {
                size_name: newName
            },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

function deleteSize(sizeId) {
    if (confirm('هل أنت متأكد من حذف هذا المقاس؟')) {
        $.ajax({
            url: 'index.php?route=inventory/product/deleteSize&user_token={{ user_token }}&size_id=' + sizeId,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    $('#size-row-' + sizeId).remove();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

// إدارة الألوان
$('#button-color-add').on('click', function() {
    var colorName = prompt('اسم اللون:');
    var colorCode = prompt('كود اللون (اختياري):');
    var hexValue = prompt('قيمة اللون (hex):', '#000000');

    if (colorName) {
        $.ajax({
            url: 'index.php?route=inventory/product/addColor&user_token={{ user_token }}',
            type: 'POST',
            data: {
                color_name: colorName,
                color_code: colorCode || '',
                hex_value: hexValue,
                sort_order: $('#colors-table tbody tr').length + 1
            },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
});

function editColor(colorId) {
    var row = $('#color-row-' + colorId);
    var currentName = row.find('td:nth-child(2)').text();
    var newName = prompt('اسم اللون:', currentName);

    if (newName && newName !== currentName) {
        $.ajax({
            url: 'index.php?route=inventory/product/editColor&user_token={{ user_token }}&color_id=' + colorId,
            type: 'POST',
            data: {
                color_name: newName
            },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

function deleteColor(colorId) {
    if (confirm('هل أنت متأكد من حذف هذا اللون؟')) {
        $.ajax({
            url: 'index.php?route=inventory/product/deleteColor&user_token={{ user_token }}&color_id=' + colorId,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    $('#color-row-' + colorId).remove();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

// إدارة الصور المتعددة
$('#button-upload-images, #dropzone').on('click', function() {
    $('#image-upload').click();
});

$('#image-upload').on('change', function() {
    var files = this.files;
    if (files.length > 0) {
        uploadMultipleImages(files);
    }
});

// السحب والإفلات للصور
$('#dropzone').on('dragover', function(e) {
    e.preventDefault();
    $(this).addClass('dragover');
});

$('#dropzone').on('dragleave', function(e) {
    e.preventDefault();
    $(this).removeClass('dragover');
});

$('#dropzone').on('drop', function(e) {
    e.preventDefault();
    $(this).removeClass('dragover');

    var files = e.originalEvent.dataTransfer.files;
    if (files.length > 0) {
        uploadMultipleImages(files);
    }
});

function uploadMultipleImages(files) {
    var formData = new FormData();

    for (var i = 0; i < files.length; i++) {
        formData.append('images[]', files[i]);
    }

    $.ajax({
        url: 'index.php?route=inventory/product/uploadMultipleImages&user_token={{ user_token }}',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            var xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener("progress", function(evt) {
                if (evt.lengthComputable) {
                    var percentComplete = evt.loaded / evt.total * 100;
                    $('#upload-progress').show();
                    $('#upload-progress .progress-bar').css('width', percentComplete + '%');
                }
            }, false);
            return xhr;
        },
        success: function(json) {
            $('#upload-progress').hide();
            if (json.success) {
                location.reload();
            } else {
                alert(json.error);
            }
        }
    });
}

function deleteImage(imageId) {
    if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
        $.ajax({
            url: 'index.php?route=inventory/product/deleteImage&user_token={{ user_token }}&image_id=' + imageId,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    $('[data-image-id="' + imageId + '"]').remove();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

function updateImageAlt(imageId, altText) {
    $.ajax({
        url: 'index.php?route=inventory/product/updateImageAlt&user_token={{ user_token }}&image_id=' + imageId,
        type: 'POST',
        data: { alt_text: altText },
        dataType: 'json'
    });
}

// ترتيب الصور بالسحب والإفلات
$('#images-preview').sortable({
    handle: '.image-sort-handle',
    update: function(event, ui) {
        var imageOrder = [];
        $('#images-preview .image-item').each(function(index) {
            imageOrder.push($(this).data('image-id'));
        });

        $.ajax({
            url: 'index.php?route=inventory/product/reorderImages&user_token={{ user_token }}',
            type: 'POST',
            data: {
                product_id: $('input[name="product_id"]').val(),
                image_order: imageOrder
            },
            dataType: 'json'
        });
    }
});

// إدارة الدفعات
$('#button-batch-add').on('click', function() {
    var batchNumber = prompt('رقم الدفعة:');
    var expiryDate = prompt('تاريخ الانتهاء (YYYY-MM-DD):');
    var quantity = prompt('الكمية:');

    if (batchNumber && quantity) {
        $.ajax({
            url: 'index.php?route=inventory/product/addBatch&user_token={{ user_token }}',
            type: 'POST',
            data: {
                product_id: $('input[name="product_id"]').val(),
                batch_number: batchNumber,
                expiry_date: expiryDate || '',
                quantity: quantity,
                manufacturing_date: new Date().toISOString().split('T')[0]
            },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
});

function editBatch(batchId) {
    // يمكن تطوير نافذة تحرير متقدمة هنا
    var newQuantity = prompt('الكمية الجديدة:');
    if (newQuantity) {
        $.ajax({
            url: 'index.php?route=inventory/product/editBatch&user_token={{ user_token }}&batch_id=' + batchId,
            type: 'POST',
            data: { quantity: newQuantity },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

function deleteBatch(batchId) {
    if (confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
        $.ajax({
            url: 'index.php?route=inventory/product/deleteBatch&user_token={{ user_token }}&batch_id=' + batchId,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    $('#batch-row-' + batchId).remove();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

// تقرير انتهاء الصلاحية
$('#button-expiry-report').on('click', function() {
    window.open('index.php?route=inventory/product/getExpiringProducts&user_token={{ user_token }}&days=30', '_blank');
});

// التصنيف التلقائي بالذكاء الاصطناعي
$('#button-auto-classify').on('click', function() {
    var productName = $('input[name="product_description[1][name]"]').val();
    var productDescription = $('textarea[name="product_description[1][description]"]').val();

    if (productName) {
        $.ajax({
            url: 'index.php?route=inventory/product/autoClassify&user_token={{ user_token }}',
            type: 'POST',
            data: {
                product_name: productName,
                product_description: productDescription,
                apply_auto_classification: 1
            },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    alert('تم تطبيق التصنيف التلقائي بنجاح!');
                    if (json.suggested_categories) {
                        // عرض التصنيفات المقترحة
                        var suggestions = json.suggested_categories.map(function(cat) {
                            return cat.name;
                        }).join(', ');
                        alert('التصنيفات المقترحة: ' + suggestions);
                    }
                } else {
                    alert(json.error || 'لم يتم العثور على تصنيفات مناسبة');
                }
            }
        });
    } else {
        alert('يرجى إدخال اسم المنتج أولاً');
    }
});

// نسخ المنتج
$('#button-clone-product').on('click', function() {
    var productId = $('input[name="product_id"]').val();
    if (productId) {
        var options = [];
        if (confirm('نسخ الوصف؟')) options.push('descriptions');
        if (confirm('نسخ التصنيفات؟')) options.push('categories');
        if (confirm('نسخ الصور؟')) options.push('images');
        if (confirm('نسخ المتغيرات؟')) options.push('variants');
        if (confirm('نسخ التسعير؟')) options.push('pricing');

        $.ajax({
            url: 'index.php?route=inventory/product/cloneProduct&user_token={{ user_token }}&product_id=' + productId,
            type: 'POST',
            data: { clone_options: options },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    alert('تم نسخ المنتج بنجاح! رقم المنتج الجديد: ' + json.new_product_id);
                    window.location.href = 'index.php?route=inventory/product/edit&user_token={{ user_token }}&product_id=' + json.new_product_id;
                } else {
                    alert(json.error);
                }
            }
        });
    }
});

// CSS للميزات الجديدة
$('<style>')
.prop('type', 'text/css')
.html(`
.dropzone-area {
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.dropzone-area:hover, .dropzone-area.dragover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.image-item {
    position: relative;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.image-container {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-item:hover .image-overlay {
    opacity: 1;
}

.image-actions {
    display: flex;
    gap: 10px;
}

.image-sort-handle {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255,255,255,0.8);
    padding: 5px;
    border-radius: 3px;
    cursor: move;
}

.image-info {
    padding: 10px;
}

.color-preview-small {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid #ccc;
    margin-right: 5px;
    vertical-align: middle;
}

.color-variant {
    display: flex;
    align-items: center;
}

.info-box {
    display: block;
    min-height: 90px;
    background: #fff;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    border-radius: 2px;
    margin-bottom: 15px;
}

.info-box-icon {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px;
    display: block;
    float: left;
    height: 90px;
    width: 90px;
    text-align: center;
    font-size: 45px;
    line-height: 90px;
    background: rgba(0,0,0,0.2);
}

.info-box-content {
    padding: 5px 10px;
    margin-left: 90px;
}

.info-box-number {
    display: block;
    font-weight: bold;
    font-size: 18px;
}

.info-box-text {
    display: block;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.bg-green { background-color: #00a65a !important; color: white; }
.bg-yellow { background-color: #f39c12 !important; color: white; }
.bg-red { background-color: #dd4b39 !important; color: white; }
.bg-blue { background-color: #3c8dbc !important; color: white; }
`)
.appendTo('head');

</script>

{{ footer }}
