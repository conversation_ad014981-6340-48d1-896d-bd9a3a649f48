{{ header }}{{ column_left }}

<!-- CSS مخصص لميزان المراجعة -->
<style>
.trial-balance-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.trial-balance-header {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 20px;
    position: relative;
}

.trial-balance-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.trial-balance-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    position: relative;
    z-index: 1;
}

.form-section {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.form-section:last-child {
    border-bottom: none;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #27ae60;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-left: 10px;
    color: #27ae60;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.filter-item {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.filter-input {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 10px 12px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-input:focus {
    border-color: #27ae60;
    box-shadow: 0 0 0 0.2rem rgba(39, 174, 96, 0.25);
    outline: none;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.checkbox-item:hover {
    background: #e9ecef;
}

.checkbox-item input[type="checkbox"] {
    margin-left: 10px;
    transform: scale(1.2);
    accent-color: #27ae60;
}

.checkbox-item label {
    margin: 0;
    font-weight: 500;
    color: #2c3e50;
    cursor: pointer;
}

.action-buttons {
    background: #f8f9fa;
    padding: 20px;
    text-align: center;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.btn-generate {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-generate:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.quick-filters {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.quick-filter-btn {
    background: #e9ecef;
    border: 1px solid #ced4da;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    color: #495057;
}

.quick-filter-btn:hover,
.quick-filter-btn.active {
    background: #27ae60;
    color: white;
    border-color: #27ae60;
}

.advanced-options {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.advanced-toggle {
    background: none;
    border: none;
    color: #27ae60;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 15px;
}

.advanced-content {
    display: none;
}

.advanced-content.show {
    display: block;
}

.help-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 5px;
    font-style: italic;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .filter-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .checkbox-group {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .quick-filters {
        justify-content: center;
    }
}

@media print {
    .action-buttons, .quick-filters, .advanced-options {
        display: none !important;
    }
}
</style>

<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="pull-right">
                <a href="{{ cancel }}" class="btn btn-secondary">
                    <i class="fa fa-reply"></i> {{ button_cancel }}
                </a>
            </div>
            <h1>{{ heading_title }}</h1>
            <ul class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                <li class="breadcrumb-item">
                    <a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <div class="container-fluid">
        {% if error_warning %}
        <div class="alert alert-danger alert-dismissible">
            <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        {% endif %}

        {% if success %}
        <div class="alert alert-success alert-dismissible">
            <i class="fa fa-check-circle"></i> {{ success }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        {% endif %}

        {% if warning %}
        <div class="alert alert-warning alert-dismissible">
            <i class="fa fa-exclamation-triangle"></i> {{ warning }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        {% endif %}

        <div class="trial-balance-container">
            <div class="trial-balance-header">
                <h2 class="trial-balance-title">
                    <i class="fa fa-balance-scale"></i>
                    إنشاء ميزان المراجعة المتقدم
                </h2>
            </div>

            <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-trial-balance">
                <!-- الفترة الزمنية -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fa fa-calendar"></i>
                        الفترة الزمنية
                    </h3>

                    <!-- فلاتر سريعة -->
                    <div class="quick-filters">
                        <button type="button" class="quick-filter-btn" onclick="setQuickPeriod('current_month')">
                            الشهر الحالي
                        </button>
                        <button type="button" class="quick-filter-btn" onclick="setQuickPeriod('last_month')">
                            الشهر الماضي
                        </button>
                        <button type="button" class="quick-filter-btn" onclick="setQuickPeriod('current_quarter')">
                            الربع الحالي
                        </button>
                        <button type="button" class="quick-filter-btn" onclick="setQuickPeriod('current_year')">
                            السنة الحالية
                        </button>
                        <button type="button" class="quick-filter-btn" onclick="setQuickPeriod('last_year')">
                            السنة الماضية
                        </button>
                    </div>

                    <div class="filter-grid">
                        <div class="filter-item">
                            <label class="filter-label" for="date-start">من تاريخ <span class="text-danger">*</span></label>
                            <input type="date" name="date_start" value="{{ date_start }}" id="date-start" class="filter-input" required />
                            <div class="help-text">تاريخ بداية الفترة المحاسبية</div>
                        </div>
                        <div class="filter-item">
                            <label class="filter-label" for="date-end">إلى تاريخ <span class="text-danger">*</span></label>
                            <input type="date" name="date_end" value="{{ date_end }}" id="date-end" class="filter-input" required />
                            <div class="help-text">تاريخ نهاية الفترة المحاسبية</div>
                        </div>
                    </div>
                </div>

                <!-- نطاق الحسابات -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fa fa-list"></i>
                        نطاق الحسابات
                    </h3>

                    <div class="filter-grid">
                        <div class="filter-item">
                            <label class="filter-label" for="account-start">من حساب</label>
                            <select name="account_start" id="account-start" class="filter-input">
                                <option value="">جميع الحسابات</option>
                                {% for account in accounts %}
                                <option value="{{ account.account_code }}" {% if account_start == account.account_code %}selected{% endif %}>
                                    {{ account.account_code }} - {{ account.name }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="help-text">اختر الحساب الأول في النطاق</div>
                        </div>
                        <div class="filter-item">
                            <label class="filter-label" for="account-end">إلى حساب</label>
                            <select name="account_end" id="account-end" class="filter-input">
                                <option value="">جميع الحسابات</option>
                                {% for account in accounts %}
                                <option value="{{ account.account_code }}" {% if account_end == account.account_code %}selected{% endif %}>
                                    {{ account.account_code }} - {{ account.name }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="help-text">اختر الحساب الأخير في النطاق</div>
                        </div>
                    </div>
                </div>

                <!-- خيارات العرض -->
                <div class="form-section">
                    <h3 class="section-title">
                        <i class="fa fa-cog"></i>
                        خيارات العرض
                    </h3>

                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" name="include_zero_balances" id="include-zero-balances" value="1" {% if include_zero_balances %}checked{% endif %} />
                            <label for="include-zero-balances">عرض الحسابات ذات الأرصدة الصفرية</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="group_by_type" id="group-by-type" value="1" {% if group_by_type %}checked{% endif %} />
                            <label for="group-by-type">تجميع حسب نوع الحساب</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="show_opening_balances" id="show-opening-balances" value="1" {% if show_opening_balances %}checked{% endif %} />
                            <label for="show-opening-balances">عرض الأرصدة الافتتاحية</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="show_period_movements" id="show-period-movements" value="1" {% if show_period_movements %}checked{% endif %} />
                            <label for="show-period-movements">عرض حركة الفترة</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" name="show_closing_balances" id="show-closing-balances" value="1" {% if show_closing_balances %}checked{% endif %} />
                            <label for="show-closing-balances">عرض الأرصدة الختامية</label>
                        </div>
                    </div>
                </div>

                <!-- الخيارات المتقدمة -->
                <div class="form-section">
                    <div class="advanced-options">
                        <button type="button" class="advanced-toggle" onclick="toggleAdvancedOptions()">
                            <i class="fa fa-chevron-down" id="advanced-icon"></i>
                            خيارات متقدمة
                        </button>

                        <div class="advanced-content" id="advanced-content">
                            <div class="filter-grid">
                                <div class="filter-item">
                                    <label class="filter-label" for="currency">العملة</label>
                                    <select name="currency" id="currency" class="filter-input">
                                        {% for curr in currencies %}
                                        <option value="{{ curr.code }}" {% if currency == curr.code %}selected{% endif %}>
                                            {{ curr.title }} ({{ curr.code }})
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="help-text">اختر عملة العرض</div>
                                </div>
                                <div class="filter-item">
                                    <label class="filter-label" for="cost-center">مركز التكلفة</label>
                                    <select name="cost_center_id" id="cost-center" class="filter-input">
                                        <option value="">جميع مراكز التكلفة</option>
                                        {% for cost_center in cost_centers %}
                                        <option value="{{ cost_center.cost_center_id }}" {% if cost_center_id == cost_center.cost_center_id %}selected{% endif %}>
                                            {{ cost_center.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="help-text">فلترة حسب مركز التكلفة</div>
                                </div>
                                <div class="filter-item">
                                    <label class="filter-label" for="project">المشروع</label>
                                    <select name="project_id" id="project" class="filter-input">
                                        <option value="">جميع المشاريع</option>
                                        {% for project in projects %}
                                        <option value="{{ project.project_id }}" {% if project_id == project.project_id %}selected{% endif %}>
                                            {{ project.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="help-text">فلترة حسب المشروع</div>
                                </div>
                                <div class="filter-item">
                                    <label class="filter-label" for="department">القسم</label>
                                    <select name="department_id" id="department" class="filter-input">
                                        <option value="">جميع الأقسام</option>
                                        {% for department in departments %}
                                        <option value="{{ department.department_id }}" {% if department_id == department.department_id %}selected{% endif %}>
                                            {{ department.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="help-text">فلترة حسب القسم</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="action-buttons">
                    <button type="submit" class="btn-generate">
                        <i class="fa fa-play"></i>
                        إنشاء ميزان المراجعة
                    </button>
                    <button type="button" class="btn-secondary" onclick="resetForm()">
                        <i class="fa fa-refresh"></i>
                        إعادة تعيين
                    </button>
                    <button type="button" class="btn-secondary" onclick="validateIntegrity()">
                        <i class="fa fa-check"></i>
                        فحص التكامل
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loading-overlay">
    <div class="loading-spinner"></div>
</div>

<!-- JavaScript -->
<script>
// تعيين الفترات السريعة
function setQuickPeriod(period) {
    const today = new Date();
    let startDate, endDate;

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.quick-filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // إضافة الفئة النشطة للزر المحدد
    event.target.classList.add('active');

    switch(period) {
        case 'current_month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case 'last_month':
            startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            endDate = new Date(today.getFullYear(), today.getMonth(), 0);
            break;
        case 'current_quarter':
            const quarter = Math.floor(today.getMonth() / 3);
            startDate = new Date(today.getFullYear(), quarter * 3, 1);
            endDate = new Date(today.getFullYear(), quarter * 3 + 3, 0);
            break;
        case 'current_year':
            startDate = new Date(today.getFullYear(), 0, 1);
            endDate = new Date(today.getFullYear(), 11, 31);
            break;
        case 'last_year':
            startDate = new Date(today.getFullYear() - 1, 0, 1);
            endDate = new Date(today.getFullYear() - 1, 11, 31);
            break;
    }

    document.getElementById('date-start').value = startDate.toISOString().split('T')[0];
    document.getElementById('date-end').value = endDate.toISOString().split('T')[0];
}

// تبديل الخيارات المتقدمة
function toggleAdvancedOptions() {
    const content = document.getElementById('advanced-content');
    const icon = document.getElementById('advanced-icon');

    if (content.classList.contains('show')) {
        content.classList.remove('show');
        icon.classList.remove('fa-chevron-up');
        icon.classList.add('fa-chevron-down');
    } else {
        content.classList.add('show');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-up');
    }
}

// إعادة تعيين النموذج
function resetForm() {
    document.getElementById('form-trial-balance').reset();

    // إعادة تعيين التواريخ للشهر الحالي
    setQuickPeriod('current_month');

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.quick-filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
}

// فحص التكامل المحاسبي
function validateIntegrity() {
    showLoading();

    fetch('{{ integrity_check_url }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();

        if (data.success) {
            alert('✅ ' + data.success);
        } else if (data.warning) {
            alert('⚠️ ' + data.warning);
        } else if (data.error) {
            alert('❌ ' + data.error);
        }
    })
    .catch(error => {
        hideLoading();
        alert('خطأ في الاتصال: ' + error.message);
    });
}

// عرض شاشة التحميل
function showLoading() {
    document.getElementById('loading-overlay').style.display = 'flex';
}

// إخفاء شاشة التحميل
function hideLoading() {
    document.getElementById('loading-overlay').style.display = 'none';
}

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('form-trial-balance').addEventListener('submit', function(e) {
    const startDate = document.getElementById('date-start').value;
    const endDate = document.getElementById('date-end').value;

    if (!startDate || !endDate) {
        e.preventDefault();
        alert('يرجى تحديد تاريخ البداية والنهاية');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        e.preventDefault();
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }

    showLoading();
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تعيين الشهر الحالي كافتراضي إذا لم تكن التواريخ محددة
    if (!document.getElementById('date-start').value) {
        setQuickPeriod('current_month');
    }
});
</script>

{{ footer }}
