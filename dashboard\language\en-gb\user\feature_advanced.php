<?php

$_['heading_title'] = 'Advanced Feature Permissions';

$_['text_list'] = 'Permissions List';
$_['text_form'] = 'Permissions Form';
$_['text_no_results'] = 'No Permissions.';
$_['text_success'] = 'Save Successfully!';
$_['text_key_help'] = 'If left blank the system will create a key such as permission_1. You can change it to something more meaningful.';

// Inputs
$_['entry_name'] = 'Permission Name';
$_['entry_key'] = 'Key (Key)';
$_['entry_key_placeholder'] = 'Leave blank to create a key automatically';
$_['entry_type'] = 'Permission Type';
$_['entry_user_groups'] = 'User groups';

// Buttons
$_['button_add'] = 'Add new permission';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_save'] = 'Save';
$_['button_cancel'] = 'Cancel';

// Warnings
$_['error_permission'] = 'Warning: You do not have edit permission!';
$_['error_name'] = 'Permission name must be greater than 3 characters!';
$_['text_confirm'] = 'Are you sure?';