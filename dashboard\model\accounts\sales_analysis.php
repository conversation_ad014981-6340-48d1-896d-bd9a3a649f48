<?php
class ModelAccountsSalesAnalysis extends Model {
    public function getSalesAnalysisData($date_start, $date_end) {
        $currency_code = $this->config->get('config_currency');

        // نفترض أن حالات الطلب المسددة أو المكتملة هي التي تحتسب في المبيعات. 
        // مثلاً نعتبر order_status_id=5 يعني الطلب مكتمل/مدفوع.
        $completed_status = 5;

        // استعلام لجلب المبيعات حسب المنتج
        $sql = "SELECT op.product_id, pd.name AS product_name,
                       SUM(op.quantity) AS total_quantity,
                       SUM(op.total) AS total_sales,
                       (SUM(op.total)/SUM(op.quantity)) AS avg_price
                FROM " . DB_PREFIX . "order_product op
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (op.product_id = pd.product_id AND pd.language_id=".(int)$this->config->get('config_language_id').")
                LEFT JOIN " . DB_PREFIX . "order o ON (op.order_id = o.order_id)
                WHERE o.order_status_id = '" . (int)$completed_status . "'
                AND o.date_added BETWEEN '" . $this->db->escape($date_start) . "' AND '" . $this->db->escape($date_end) . "'
                GROUP BY op.product_id
                ORDER BY total_sales DESC";

        $query = $this->db->query($sql);
        $products = $query->rows;

        $results = [];
        $total_sales = 0.0;

        foreach ($products as $p) {
            $sales_amount = (float)$p['total_sales'];
            $total_sales += $sales_amount;

            $results[] = [
                'product_id' => $p['product_id'],
                'product_name' => $p['product_name'],
                'total_quantity' => (int)$p['total_quantity'],
                'total_sales' => $this->currency->format($sales_amount, $currency_code),
                'avg_price' => $this->currency->format((float)$p['avg_price'], $currency_code)
            ];
        }

        return [
            'products' => $results,
            'total_sales' => $this->currency->format($total_sales, $currency_code)
        ];
    }

    // تحسين تحليل المبيعات مع التخزين المؤقت
    public function getOptimizedSalesAnalysis($filter_data) {
        $cache_key = 'sales_analysis_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $result = $this->getAdvancedSalesAnalysis($filter_data);
        $this->cache->set($cache_key, $result, 1800);

        return $result;
    }

    // تحليل متقدم للمبيعات حسب العميل
    public function getCustomerSalesAnalysis($filter_data) {
        $cache_key = 'customer_sales_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $analysis = array();

        // تحليل المبيعات حسب العميل
        $customer_query = $this->db->query("
            SELECT
                c.customer_id,
                CONCAT(c.firstname, ' ', c.lastname) as customer_name,
                COUNT(DISTINCT o.order_id) as order_count,
                COALESCE(SUM(o.total), 0) as total_sales,
                COALESCE(AVG(o.total), 0) as avg_order_value,
                COALESCE(SUM(CASE WHEN o.order_status_id = 5 THEN o.total ELSE 0 END), 0) as completed_sales
            FROM " . DB_PREFIX . "customer c
            LEFT JOIN " . DB_PREFIX . "order o ON c.customer_id = o.customer_id
            WHERE o.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
            AND '" . $this->db->escape($filter_data['date_end']) . "'
            AND o.order_status_id > 0
            GROUP BY c.customer_id, c.firstname, c.lastname
            HAVING total_sales > 0
            ORDER BY total_sales DESC
            LIMIT 50
        ");

        $analysis['customers'] = $customer_query->rows;

        // حساب إجماليات
        $total_sales = array_sum(array_column($analysis['customers'], 'total_sales'));
        $total_orders = array_sum(array_column($analysis['customers'], 'order_count'));

        $analysis['totals'] = array(
            'total_sales' => $total_sales,
            'total_orders' => $total_orders,
            'avg_order_value' => $total_orders > 0 ? round($total_sales / $total_orders, 2) : 0,
            'customer_count' => count($analysis['customers'])
        );

        $this->cache->set($cache_key, $analysis, 2400);

        return $analysis;
    }

    // تحليل المبيعات حسب المنتج
    public function getProductSalesAnalysis($filter_data) {
        $cache_key = 'product_sales_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $analysis = array();

        // تحليل المبيعات حسب المنتج
        $product_query = $this->db->query("
            SELECT
                p.product_id,
                pd.name as product_name,
                COUNT(DISTINCT op.order_id) as order_count,
                COALESCE(SUM(op.quantity), 0) as total_quantity,
                COALESCE(SUM(op.total), 0) as total_sales,
                COALESCE(AVG(op.price), 0) as avg_price
            FROM " . DB_PREFIX . "product p
            LEFT JOIN " . DB_PREFIX . "product_description pd ON p.product_id = pd.product_id
            LEFT JOIN " . DB_PREFIX . "order_product op ON p.product_id = op.product_id
            LEFT JOIN " . DB_PREFIX . "order o ON op.order_id = o.order_id
            WHERE o.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
            AND '" . $this->db->escape($filter_data['date_end']) . "'
            AND o.order_status_id > 0
            GROUP BY p.product_id, pd.name
            HAVING total_sales > 0
            ORDER BY total_sales DESC
            LIMIT 50
        ");

        $analysis['products'] = $product_query->rows;

        $this->cache->set($cache_key, $analysis, 2400);

        return $analysis;
    }

    // التحقق من صحة البيانات
    private function validateSalesData($filter_data) {
        $errors = array();

        if (empty($filter_data['date_start']) || !$this->validateDate($filter_data['date_start'])) {
            $errors[] = 'Invalid start date';
        }

        if (empty($filter_data['date_end']) || !$this->validateDate($filter_data['date_end'])) {
            $errors[] = 'Invalid end date';
        }

        return $errors;
    }

    private function validateDate($date) {
        if (empty($date)) return false;

        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
}
