{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Advanced Budget Management -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --budget-color: #8e44ad;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.budget-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.budget-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--budget-color), var(--primary-color), var(--secondary-color));
}

.budget-header {
    text-align: center;
    border-bottom: 3px solid var(--budget-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.budget-header h2 {
    color: var(--budget-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.budget-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.budget-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.budget-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.budget-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.budget-card.total::before { background: var(--budget-color); }
.budget-card.approved::before { background: var(--success-color); }
.budget-card.draft::before { background: var(--warning-color); }
.budget-card.variance::before { background: var(--danger-color); }

.budget-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.budget-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.budget-card .count {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-total .amount { color: var(--budget-color); }
.card-approved .amount { color: var(--success-color); }
.card-draft .amount { color: var(--warning-color); }
.card-variance .amount { color: var(--danger-color); }

.budget-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.budget-table th {
    background: linear-gradient(135deg, var(--budget-color), #7d3c98);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.budget-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.budget-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.budget-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.status-draft { 
    color: var(--warning-color); 
    font-weight: 600;
}

.status-approved { 
    color: var(--success-color); 
    font-weight: 600;
}

.status-active { 
    color: var(--info-color); 
    font-weight: 600;
}

.status-closed { 
    color: var(--danger-color); 
    font-weight: 600;
}

.budget-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.budget-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

.variance-positive { color: var(--success-color); }
.variance-negative { color: var(--danger-color); }

/* RTL Support */
[dir="rtl"] .budget-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .budget-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .budget-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .budget-table {
        font-size: 0.8rem;
    }
    
    .budget-table th,
    .budget-table td {
        padding: 8px 6px;
    }
    
    .budget-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .budget-actions {
        flex-direction: column;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <a href="{{ add }}" class="btn btn-success btn-lg" data-bs-toggle="tooltip" title="{{ button_add }}">
            <i class="fas fa-plus me-2"></i> {{ button_add }}
          </a>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_budget_operations }}">
              <i class="fas fa-chart-line me-2"></i> {{ text_budget_operations }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="{{ analysis }}">
                <i class="fas fa-analytics text-primary me-2"></i> {{ text_budget_analysis }}
              </a></li>
              <li><a class="dropdown-item" href="{{ variance }}">
                <i class="fas fa-chart-bar text-warning me-2"></i> {{ text_variance_analysis }}
              </a></li>
              <li><a class="dropdown-item" href="{{ performance }}">
                <i class="fas fa-tachometer-alt text-info me-2"></i> {{ text_performance_analysis }}
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="{{ import }}">
                <i class="fas fa-upload text-success me-2"></i> {{ text_import }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-primary" onclick="refreshBudgets()"
                  data-bs-toggle="tooltip" title="{{ text_refresh_budgets }}">
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show">
      <i class="fas fa-check-circle"></i>
      {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Budget Summary Cards -->
    <div class="budget-summary-cards">
      <div class="budget-card card-total total">
        <h4>{{ text_total_budgets }}</h4>
        <div class="amount">{{ summary.total_budgets }}</div>
        <div class="count">{{ text_budgets }}</div>
      </div>
      <div class="budget-card card-approved approved">
        <h4>{{ text_approved_budgets }}</h4>
        <div class="amount">{{ summary.approved_budgets }}</div>
        <div class="count">{{ text_approved }}</div>
      </div>
      <div class="budget-card card-draft draft">
        <h4>{{ text_draft_budgets }}</h4>
        <div class="amount">{{ summary.draft_budgets }}</div>
        <div class="count">{{ text_draft }}</div>
      </div>
      <div class="budget-card card-variance variance">
        <h4>{{ text_total_variance }}</h4>
        <div class="amount">{{ summary.total_variance_formatted }}</div>
        <div class="count">{{ text_variance }}</div>
      </div>
    </div>

    <!-- Budget Management Table -->
    <div class="budget-container">
      <div class="budget-header">
        <h2>{{ text_budget_list }}</h2>
      </div>

      <div class="table-responsive">
        <table class="budget-table" id="budget-table">
          <thead>
            <tr>
              <th>{{ column_budget_name }}</th>
              <th>{{ column_budget_code }}</th>
              <th>{{ column_budget_type }}</th>
              <th>{{ column_budget_year }}</th>
              <th>{{ column_department }}</th>
              <th>{{ column_total_amount }}</th>
              <th>{{ column_actual_amount }}</th>
              <th>{{ column_variance }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_action }}</th>
            </tr>
          </thead>
          <tbody>
            {% for budget in budgets %}
            <tr data-budget-id="{{ budget.budget_id }}">
              <td>
                <strong>{{ budget.budget_name }}</strong>
                <br>
                <small class="text-muted">{{ budget.description }}</small>
              </td>
              <td>{{ budget.budget_code }}</td>
              <td>{{ budget.budget_type_text }}</td>
              <td>{{ budget.budget_year }}</td>
              <td>{{ budget.department_name }}</td>
              <td class="amount-cell">
                <strong>{{ budget.total_amount_formatted }}</strong>
              </td>
              <td class="amount-cell">
                {{ budget.actual_amount_formatted }}
              </td>
              <td class="amount-cell">
                <span class="{% if budget.variance >= 0 %}variance-positive{% else %}variance-negative{% endif %}">
                  {{ budget.variance_formatted }} ({{ budget.variance_percentage }}%)
                </span>
              </td>
              <td>
                <span class="status-{{ budget.status }}">
                  {{ budget.status_text }}
                </span>
              </td>
              <td>
                <div class="budget-actions">
                  <a href="{{ budget.edit }}" class="btn btn-outline-primary btn-sm" 
                     data-bs-toggle="tooltip" title="{{ button_edit }}">
                    <i class="fas fa-edit"></i>
                  </a>
                  <a href="{{ budget.view }}" class="btn btn-outline-info btn-sm"
                     data-bs-toggle="tooltip" title="{{ button_view }}">
                    <i class="fas fa-eye"></i>
                  </a>
                  <a href="{{ budget.copy }}" class="btn btn-outline-secondary btn-sm"
                     data-bs-toggle="tooltip" title="{{ button_copy }}">
                    <i class="fas fa-copy"></i>
                  </a>
                  {% if budget.status == 'draft' or budget.status == 'submitted' %}
                  <button type="button" class="btn btn-outline-success btn-sm" 
                          onclick="approveBudget({{ budget.budget_id }})"
                          data-bs-toggle="tooltip" title="{{ button_approve }}">
                    <i class="fas fa-check"></i>
                  </button>
                  {% endif %}
                  {% if budget.status == 'draft' %}
                  <button type="button" class="btn btn-outline-danger btn-sm" 
                          onclick="deleteBudget({{ budget.budget_id }})"
                          data-bs-toggle="tooltip" title="{{ button_delete }}">
                    <i class="fas fa-trash"></i>
                  </button>
                  {% endif %}
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <div class="row">
        <div class="col-sm-6 text-start">{{ pagination }}</div>
        <div class="col-sm-6 text-end">{{ results }}</div>
      </div>
    </div>
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Advanced Budget Management
class AdvancedBudgetManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeAutoRefresh();
        this.initializeCharts();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeDataTable() {
        const table = document.getElementById('budget-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'asc']], // Sort by budget name
                columnDefs: [
                    { targets: [5, 6, 7], className: 'text-end' },
                    { targets: [9], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        window.location.href = '{{ add }}';
                        break;
                    case 'r':
                        e.preventDefault();
                        this.refreshBudgets();
                        break;
                    case 'a':
                        e.preventDefault();
                        window.location.href = '{{ analysis }}';
                        break;
                }
            }
        });
    }

    initializeAutoRefresh() {
        // Auto-refresh budget data every 10 minutes
        setInterval(() => {
            this.refreshBudgets(true);
        }, 600000);
    }

    initializeCharts() {
        // Initialize budget variance charts if Chart.js is available
        if (typeof Chart !== 'undefined') {
            this.createVarianceChart();
            this.createBudgetTrendChart();
        }
    }

    refreshBudgets(silent = false) {
        if (!silent) {
            this.showAlert('{{ text_refreshing_budgets }}...', 'info');
        }

        fetch('{{ refresh_url }}', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                if (!silent) {
                    this.showAlert('{{ text_budgets_refreshed }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                }
                this.updateBudgetDisplay(data.budgets);
            } else {
                if (!silent) {
                    this.showAlert(data.error || '{{ error_refresh_budgets }}', 'danger');
                }
            }
        })
        .catch(error => {
            if (!silent) {
                this.showAlert('{{ error_refresh_budgets }}: ' + error.message, 'danger');
            }
        });
    }

    approveBudget(budgetId) {
        if (!confirm('{{ warning_approve }}')) {
            return;
        }

        fetch('{{ approve_url }}', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                budget_id: budgetId
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                this.showAlert('{{ success_approve }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_approve_budget }}', 'danger');
            }
        })
        .catch(error => {
            this.showAlert('{{ error_approve_budget }}: ' + error.message, 'danger');
        });
    }

    deleteBudget(budgetId) {
        if (!confirm('{{ warning_delete }}')) {
            return;
        }

        fetch('{{ delete_url }}', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                selected: [budgetId]
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                this.showAlert('{{ success_delete }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_delete_budget }}', 'danger');
            }
        })
        .catch(error => {
            this.showAlert('{{ error_delete_budget }}: ' + error.message, 'danger');
        });
    }

    createVarianceChart() {
        const ctx = document.getElementById('varianceChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: {{ budget_names|json_encode|raw }},
                datasets: [{
                    label: '{{ text_variance }}',
                    data: {{ variance_data|json_encode|raw }},
                    backgroundColor: function(context) {
                        const value = context.parsed.y;
                        return value >= 0 ? '#27ae60' : '#e74c3c';
                    }
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_variance_analysis }}'
                    }
                }
            }
        });
    }

    createBudgetTrendChart() {
        const ctx = document.getElementById('trendChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ months|json_encode|raw }},
                datasets: [{
                    label: '{{ text_planned_amount }}',
                    data: {{ planned_amounts|json_encode|raw }},
                    borderColor: '#8e44ad',
                    backgroundColor: 'rgba(142, 68, 173, 0.1)'
                }, {
                    label: '{{ text_actual_amount }}',
                    data: {{ actual_amounts|json_encode|raw }},
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_budget_vs_actual }}'
                    }
                }
            }
        });
    }

    updateBudgetDisplay(budgets) {
        // Update budget display without full page reload
        budgets.forEach(budget => {
            const row = document.querySelector(`[data-budget-id="${budget.budget_id}"]`);
            if (row) {
                const actualCell = row.querySelector('.amount-cell:nth-child(7)');
                const varianceCell = row.querySelector('.amount-cell:nth-child(8)');

                if (actualCell) {
                    actualCell.textContent = budget.actual_amount_formatted;
                }

                if (varianceCell) {
                    const varianceSpan = varianceCell.querySelector('span');
                    if (varianceSpan) {
                        varianceSpan.textContent = `${budget.variance_formatted} (${budget.variance_percentage}%)`;
                        varianceSpan.className = budget.variance >= 0 ? 'variance-positive' : 'variance-negative';
                    }
                }
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function refreshBudgets() {
    budgetManager.refreshBudgets();
}

function approveBudget(budgetId) {
    budgetManager.approveBudget(budgetId);
}

function deleteBudget(budgetId) {
    budgetManager.deleteBudget(budgetId);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.budgetManager = new AdvancedBudgetManager();
});
</script>

{{ footer }}
