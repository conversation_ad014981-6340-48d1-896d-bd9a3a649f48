{% extends "layout/layout.twig" %}
{% block title %}{{ heading }}{% endblock %}
{% block content %}
<div class="page-header">
    <div class="container-fluid">
        <div class="pull-right">
            <button type="button" id="button-process" data-toggle="tooltip" title="{{ button_process }}" class="btn btn-primary">
                <i class="fa fa-play"></i> {{ button_process }}
            </button>
            <button type="button" id="button-cleanup" data-toggle="tooltip" title="{{ button_cleanup }}" class="btn btn-warning">
                <i class="fa fa-trash"></i> {{ button_cleanup }}
            </button>
            <button type="button" id="button-reset-stuck" data-toggle="tooltip" title="{{ button_reset_stuck }}" class="btn btn-danger">
                <i class="fa fa-refresh"></i> {{ button_reset_stuck }}
            </button>
        </div>
        <h1>{{ heading }}</h1>
        <ul class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
                <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-clock-o fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <div class="huge" id="pending-count">{{ stats.pending|default(0) }}</div>
                            <div>{{ text_pending }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="panel panel-yellow">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-cog fa-spin fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <div class="huge" id="processing-count">{{ stats.processing|default(0) }}</div>
                            <div>{{ text_processing }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="panel panel-green">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-check fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <div class="huge" id="completed-count">{{ stats.completed|default(0) }}</div>
                            <div>{{ text_completed }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="panel panel-red">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-times fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <div class="huge" id="failed-count">{{ stats.failed|default(0) }}</div>
                            <div>{{ text_failed }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- فلاتر البحث -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="control-label" for="input-status">{{ entry_status }}</label>
                        <select name="filter_status" id="input-status" class="form-control">
                            <option value="">{{ text_all }}</option>
                            <option value="pending" {% if filter_status == 'pending' %}selected="selected"{% endif %}>{{ text_pending }}</option>
                            <option value="processing" {% if filter_status == 'processing' %}selected="selected"{% endif %}>{{ text_processing }}</option>
                            <option value="done" {% if filter_status == 'done' %}selected="selected"{% endif %}>{{ text_completed }}</option>
                            <option value="failed" {% if filter_status == 'failed' %}selected="selected"{% endif %}>{{ text_failed }}</option>
                            <option value="cancelled" {% if filter_status == 'cancelled' %}selected="selected"{% endif %}>{{ text_cancelled }}</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="control-label" for="input-job-type">{{ entry_job_type }}</label>
                        <select name="filter_job_type" id="input-job-type" class="form-control">
                            <option value="">{{ text_all }}</option>
                            <option value="eta_invoice" {% if filter_job_type == 'eta_invoice' %}selected="selected"{% endif %}>ETA Invoice</option>
                            <option value="eta_receipt" {% if filter_job_type == 'eta_receipt' %}selected="selected"{% endif %}>ETA Receipt</option>
                            <option value="eta_credit_note" {% if filter_job_type == 'eta_credit_note' %}selected="selected"{% endif %}>ETA Credit Note</option>
                            <option value="eta_debit_note" {% if filter_job_type == 'eta_debit_note' %}selected="selected"{% endif %}>ETA Debit Note</option>
                            <option value="inventory_update" {% if filter_job_type == 'inventory_update' %}selected="selected"{% endif %}>Inventory Update</option>
                            <option value="accounting_entry" {% if filter_job_type == 'accounting_entry' %}selected="selected"{% endif %}>Accounting Entry</option>
                            <option value="email_notification" {% if filter_job_type == 'email_notification' %}selected="selected"{% endif %}>Email Notification</option>
                            <option value="report_generation" {% if filter_job_type == 'report_generation' %}selected="selected"{% endif %}>Report Generation</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-sm-2">
                    <div class="form-group">
                        <label class="control-label" for="input-priority">{{ entry_priority }}</label>
                        <select name="filter_priority" id="input-priority" class="form-control">
                            <option value="">{{ text_all }}</option>
                            <option value="1" {% if filter_priority == '1' %}selected="selected"{% endif %}>{{ text_low }}</option>
                            <option value="2" {% if filter_priority == '2' %}selected="selected"{% endif %}>{{ text_normal }}</option>
                            <option value="3" {% if filter_priority == '3' %}selected="selected"{% endif %}>{{ text_high }}</option>
                            <option value="4" {% if filter_priority == '4' %}selected="selected"{% endif %}>{{ text_critical }}</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-sm-2">
                    <div class="form-group">
                        <label class="control-label" for="input-date-from">{{ entry_date_from }}</label>
                        <div class="input-group date">
                            <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="{{ entry_date_from }}" data-date-format="YYYY-MM-DD" id="input-date-from" class="form-control" />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-2">
                    <div class="form-group">
                        <label class="control-label" for="input-date-to">{{ entry_date_to }}</label>
                        <div class="input-group date">
                            <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ entry_date_to }}" data-date-format="YYYY-MM-DD" id="input-date-to" class="form-control" />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-sm-12">
                    <button type="button" id="button-filter" class="btn btn-primary pull-right">
                        <i class="fa fa-search"></i> {{ button_filter }}
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- جدول المهام -->
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
        </div>
        <div class="panel-body">
            <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-queue">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <td style="width: 1px;" class="text-center">
                                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                                </td>
                                <td class="text-left">{{ column_id }}</td>
                                <td class="text-left">{{ column_job_type }}</td>
                                <td class="text-center">{{ column_priority }}</td>
                                <td class="text-center">{{ column_status }}</td>
                                <td class="text-center">{{ column_attempts }}</td>
                                <td class="text-left">{{ column_created }}</td>
                                <td class="text-left">{{ column_updated }}</td>
                                <td class="text-right">{{ column_action }}</td>
                            </tr>
                        </thead>
                        <tbody>
                            {% if jobs %}
                                {% for job in jobs %}
                                    <tr>
                                        <td class="text-center">
                                            <input type="checkbox" name="selected[]" value="{{ job.id }}" />
                                        </td>
                                        <td class="text-left">{{ job.id }}</td>
                                        <td class="text-left">
                                            <span class="label label-info">{{ job.job_type }}</span>
                                        </td>
                                        <td class="text-center">
                                            {% if job.priority == 1 %}
                                                <span class="label label-default">{{ text_low }}</span>
                                            {% elseif job.priority == 2 %}
                                                <span class="label label-primary">{{ text_normal }}</span>
                                            {% elseif job.priority == 3 %}
                                                <span class="label label-warning">{{ text_high }}</span>
                                            {% elseif job.priority == 4 %}
                                                <span class="label label-danger">{{ text_critical }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">
                                            {% if job.status == 'pending' %}
                                                <span class="label label-warning">{{ text_pending }}</span>
                                            {% elseif job.status == 'processing' %}
                                                <span class="label label-info">{{ text_processing }}</span>
                                            {% elseif job.status == 'done' %}
                                                <span class="label label-success">{{ text_completed }}</span>
                                            {% elseif job.status == 'failed' %}
                                                <span class="label label-danger">{{ text_failed }}</span>
                                            {% elseif job.status == 'cancelled' %}
                                                <span class="label label-default">{{ text_cancelled }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center">{{ job.attempts }}/{{ job.max_attempts }}</td>
                                        <td class="text-left">{{ job.created_at }}</td>
                                        <td class="text-left">{{ job.updated_at }}</td>
                                        <td class="text-right">
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-info btn-sm" onclick="viewJob({{ job.id }})" data-toggle="tooltip" title="{{ button_view }}">
                                                    <i class="fa fa-eye"></i>
                                                </button>
                                                {% if job.status == 'failed' %}
                                                    <button type="button" class="btn btn-warning btn-sm" onclick="retryJob({{ job.id }})" data-toggle="tooltip" title="{{ button_retry }}">
                                                        <i class="fa fa-refresh"></i>
                                                    </button>
                                                {% endif %}
                                                {% if job.status in ['pending', 'processing'] %}
                                                    <button type="button" class="btn btn-danger btn-sm" onclick="cancelJob({{ job.id }})" data-toggle="tooltip" title="{{ button_cancel }}">
                                                        <i class="fa fa-times"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td class="text-center" colspan="9">{{ text_no_results }}</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </form>
            
            <div class="row">
                <div class="col-sm-6 text-left">{{ pagination }}</div>
                <div class="col-sm-6 text-right">{{ results }}</div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل المهمة -->
<div class="modal fade" id="modal-job-details" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">{{ text_job_details }}</h4>
            </div>
            <div class="modal-body" id="job-details-content">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
// تحديث الإحصائيات كل 30 ثانية
setInterval(function() {
    updateStats();
}, 30000);

function updateStats() {
    $.ajax({
        url: 'index.php?route=queue/queue/getStats&user_token={{ user_token }}',
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                $('#pending-count').text(json.stats.pending || 0);
                $('#processing-count').text(json.stats.processing || 0);
                $('#completed-count').text(json.stats.completed || 0);
                $('#failed-count').text(json.stats.failed || 0);
            }
        }
    });
}

// معالجة المهام
$('#button-process').on('click', function() {
    var button = $(this);
    button.button('loading');
    
    $.ajax({
        url: 'index.php?route=queue/queue/process&user_token={{ user_token }}',
        dataType: 'json',
        success: function(json) {
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                location.reload();
            }
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
        },
        complete: function() {
            button.button('reset');
        }
    });
});

// تنظيف المهام القديمة
$('#button-cleanup').on('click', function() {
    if (confirm('{{ text_confirm_cleanup }}')) {
        var button = $(this);
        button.button('loading');
        
        $.ajax({
            url: 'index.php?route=queue/queue/cleanup&user_token={{ user_token }}',
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    location.reload();
                }
                
                if (json['error']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                }
            },
            complete: function() {
                button.button('reset');
            }
        });
    }
});

// إعادة تعيين المهام المعلقة
$('#button-reset-stuck').on('click', function() {
    if (confirm('{{ text_confirm_reset }}')) {
        var button = $(this);
        button.button('loading');
        
        $.ajax({
            url: 'index.php?route=queue/queue/resetStuck&user_token={{ user_token }}',
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    location.reload();
                }
                
                if (json['error']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                }
            },
            complete: function() {
                button.button('reset');
            }
        });
    }
});

// فلترة النتائج
$('#button-filter').on('click', function() {
    var url = 'index.php?route=queue/queue&user_token={{ user_token }}';
    
    var filter_status = $('select[name="filter_status"]').val();
    if (filter_status) {
        url += '&filter_status=' + encodeURIComponent(filter_status);
    }
    
    var filter_job_type = $('select[name="filter_job_type"]').val();
    if (filter_job_type) {
        url += '&filter_job_type=' + encodeURIComponent(filter_job_type);
    }
    
    var filter_priority = $('select[name="filter_priority"]').val();
    if (filter_priority) {
        url += '&filter_priority=' + encodeURIComponent(filter_priority);
    }
    
    var filter_date_from = $('input[name="filter_date_from"]').val();
    if (filter_date_from) {
        url += '&filter_date_from=' + encodeURIComponent(filter_date_from);
    }
    
    var filter_date_to = $('input[name="filter_date_to"]').val();
    if (filter_date_to) {
        url += '&filter_date_to=' + encodeURIComponent(filter_date_to);
    }
    
    location = url;
});

// عرض تفاصيل المهمة
function viewJob(job_id) {
    $.ajax({
        url: 'index.php?route=queue/queue/getJobDetails&user_token={{ user_token }}&job_id=' + job_id,
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                $('#job-details-content').html(json.html);
                $('#modal-job-details').modal('show');
            } else {
                alert(json.error || 'Error loading job details');
            }
        }
    });
}

// إعادة تشغيل مهمة فاشلة
function retryJob(job_id) {
    if (confirm('{{ text_confirm_retry }}')) {
        $.ajax({
            url: 'index.php?route=queue/queue/retry&user_token={{ user_token }}&job_id=' + job_id,
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    location.reload();
                }
                
                if (json['error']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                }
            }
        });
    }
}

// إلغاء مهمة
function cancelJob(job_id) {
    if (confirm('{{ text_confirm_cancel }}')) {
        $.ajax({
            url: 'index.php?route=queue/queue/cancel&user_token={{ user_token }}&job_id=' + job_id,
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    location.reload();
                }
                
                if (json['error']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                }
            }
        });
    }
}

// تهيئة التواريخ
$('.date').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

// تهيئة tooltips
$('[data-toggle="tooltip"]').tooltip();
</script>

<style>
.huge {
    font-size: 40px;
}

.panel-green {
    border-color: #5cb85c;
}

.panel-green > .panel-heading {
    border-color: #5cb85c;
    color: white;
    background-color: #5cb85c;
}

.panel-red {
    border-color: #d9534f;
}

.panel-red > .panel-heading {
    border-color: #d9534f;
    color: white;
    background-color: #d9534f;
}

.panel-yellow {
    border-color: #f0ad4e;
}

.panel-yellow > .panel-heading {
    border-color: #f0ad4e;
    color: white;
    background-color: #f0ad4e;
}

.btn-group .btn {
    margin-right: 2px;
}

.table > tbody > tr > td {
    vertical-align: middle;
}
</style>
{% endblock %}