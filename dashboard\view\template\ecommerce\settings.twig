{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" data-bs-toggle="tooltip" title="{{ button_test_connection }}" onclick="testConnection()" class="btn btn-info"><i class="fas fa-plug"></i></button>
        <button type="button" data-bs-toggle="tooltip" title="{{ button_sync_products }}" onclick="syncProducts()" class="btn btn-warning"><i class="fas fa-sync"></i></button>
        <button type="button" data-bs-toggle="tooltip" title="{{ button_save }}" onclick="$('#form-settings').submit();" class="btn btn-primary"><i class="fas fa-save"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{ error_warning }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    
    <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-settings" class="form-horizontal">
      <div class="row">
        <!-- التبويبات الجانبية -->
        <div class="col-lg-2">
          <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
            <button class="nav-link active" id="v-pills-general-tab" data-bs-toggle="pill" data-bs-target="#v-pills-general" type="button" role="tab">
              <i class="fas fa-cog"></i> {{ tab_general }}
            </button>
            <button class="nav-link" id="v-pills-products-tab" data-bs-toggle="pill" data-bs-target="#v-pills-products" type="button" role="tab">
              <i class="fas fa-box"></i> {{ tab_products }}
            </button>
            <button class="nav-link" id="v-pills-orders-tab" data-bs-toggle="pill" data-bs-target="#v-pills-orders" type="button" role="tab">
              <i class="fas fa-shopping-cart"></i> {{ tab_orders }}
            </button>
            <button class="nav-link" id="v-pills-payment-tab" data-bs-toggle="pill" data-bs-target="#v-pills-payment" type="button" role="tab">
              <i class="fas fa-credit-card"></i> {{ tab_payment }}
            </button>
            <button class="nav-link" id="v-pills-shipping-tab" data-bs-toggle="pill" data-bs-target="#v-pills-shipping" type="button" role="tab">
              <i class="fas fa-truck"></i> {{ tab_shipping }}
            </button>
            <button class="nav-link" id="v-pills-seo-tab" data-bs-toggle="pill" data-bs-target="#v-pills-seo" type="button" role="tab">
              <i class="fas fa-search"></i> {{ tab_seo }}
            </button>
            <button class="nav-link" id="v-pills-analytics-tab" data-bs-toggle="pill" data-bs-target="#v-pills-analytics" type="button" role="tab">
              <i class="fas fa-chart-bar"></i> {{ tab_analytics }}
            </button>
            <button class="nav-link" id="v-pills-security-tab" data-bs-toggle="pill" data-bs-target="#v-pills-security" type="button" role="tab">
              <i class="fas fa-shield-alt"></i> {{ tab_security }}
            </button>
          </div>
        </div>
        
        <!-- محتوى التبويبات -->
        <div class="col-lg-10">
          <div class="tab-content" id="v-pills-tabContent">
            
            <!-- تبويب الإعدادات العامة -->
            <div class="tab-pane fade show active" id="v-pills-general" role="tabpanel">
              <div class="card">
                <div class="card-header">
                  <h3 class="card-title"><i class="fas fa-cog"></i> {{ tab_general }}</h3>
                </div>
                <div class="card-body">
                  <div class="row mb-3">
                    <label for="input-ecommerce-status" class="col-sm-2 col-form-label">{{ entry_ecommerce_status }}</label>
                    <div class="col-sm-10">
                      <div class="form-check form-switch">
                        <input type="checkbox" name="ecommerce_status" value="1" id="input-ecommerce-status" class="form-check-input" {% if ecommerce_status %}checked{% endif %}>
                        <label class="form-check-label" for="input-ecommerce-status">{{ text_enabled }}</label>
                      </div>
                      <div class="form-text">{{ help_ecommerce_status }}</div>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-store-name" class="col-sm-2 col-form-label"><span class="required">*</span> {{ entry_store_name }}</label>
                    <div class="col-sm-10">
                      <input type="text" name="store_name" value="{{ store_name }}" placeholder="{{ entry_store_name }}" id="input-store-name" class="form-control"/>
                      {% if error_store_name %}
                        <div class="text-danger">{{ error_store_name }}</div>
                      {% endif %}
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-store-description" class="col-sm-2 col-form-label">{{ entry_store_description }}</label>
                    <div class="col-sm-10">
                      <textarea name="store_description" rows="3" placeholder="{{ entry_store_description }}" id="input-store-description" class="form-control">{{ store_description }}</textarea>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-store-email" class="col-sm-2 col-form-label"><span class="required">*</span> {{ entry_store_email }}</label>
                    <div class="col-sm-10">
                      <input type="email" name="store_email" value="{{ store_email }}" placeholder="{{ entry_store_email }}" id="input-store-email" class="form-control"/>
                      {% if error_store_email %}
                        <div class="text-danger">{{ error_store_email }}</div>
                      {% endif %}
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-store-phone" class="col-sm-2 col-form-label">{{ entry_store_phone }}</label>
                    <div class="col-sm-10">
                      <input type="text" name="store_phone" value="{{ store_phone }}" placeholder="{{ entry_store_phone }}" id="input-store-phone" class="form-control"/>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-store-address" class="col-sm-2 col-form-label">{{ entry_store_address }}</label>
                    <div class="col-sm-10">
                      <textarea name="store_address" rows="3" placeholder="{{ entry_store_address }}" id="input-store-address" class="form-control">{{ store_address }}</textarea>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- تبويب إعدادات المنتجات -->
            <div class="tab-pane fade" id="v-pills-products" role="tabpanel">
              <div class="card">
                <div class="card-header">
                  <h3 class="card-title"><i class="fas fa-box"></i> {{ tab_products }}</h3>
                </div>
                <div class="card-body">
                  <div class="row mb-3">
                    <label for="input-product-sync" class="col-sm-2 col-form-label">{{ entry_product_sync }}</label>
                    <div class="col-sm-10">
                      <div class="form-check form-switch">
                        <input type="checkbox" name="product_sync_enabled" value="1" id="input-product-sync" class="form-check-input" {% if product_sync_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="input-product-sync">{{ text_enabled }}</label>
                      </div>
                      <div class="form-text">{{ help_product_sync }}</div>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-auto-publish" class="col-sm-2 col-form-label">{{ entry_auto_publish }}</label>
                    <div class="col-sm-10">
                      <div class="form-check form-switch">
                        <input type="checkbox" name="auto_publish_products" value="1" id="input-auto-publish" class="form-check-input" {% if auto_publish_products %}checked{% endif %}>
                        <label class="form-check-label" for="input-auto-publish">{{ text_enabled }}</label>
                      </div>
                      <div class="form-text">{{ help_auto_publish }}</div>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-stock-sync" class="col-sm-2 col-form-label">{{ entry_stock_sync }}</label>
                    <div class="col-sm-10">
                      <div class="form-check form-switch">
                        <input type="checkbox" name="stock_sync_enabled" value="1" id="input-stock-sync" class="form-check-input" {% if stock_sync_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="input-stock-sync">{{ text_enabled }}</label>
                      </div>
                      <div class="form-text">{{ help_stock_sync }}</div>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-price-sync" class="col-sm-2 col-form-label">{{ entry_price_sync }}</label>
                    <div class="col-sm-10">
                      <div class="form-check form-switch">
                        <input type="checkbox" name="price_sync_enabled" value="1" id="input-price-sync" class="form-check-input" {% if price_sync_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="input-price-sync">{{ text_enabled }}</label>
                      </div>
                      <div class="form-text">{{ help_price_sync }}</div>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-image-sync" class="col-sm-2 col-form-label">{{ entry_image_sync }}</label>
                    <div class="col-sm-10">
                      <div class="form-check form-switch">
                        <input type="checkbox" name="image_sync_enabled" value="1" id="input-image-sync" class="form-check-input" {% if image_sync_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="input-image-sync">{{ text_enabled }}</label>
                      </div>
                      <div class="form-text">{{ help_image_sync }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- تبويب إعدادات الطلبات -->
            <div class="tab-pane fade" id="v-pills-orders" role="tabpanel">
              <div class="card">
                <div class="card-header">
                  <h3 class="card-title"><i class="fas fa-shopping-cart"></i> {{ tab_orders }}</h3>
                </div>
                <div class="card-body">
                  <div class="row mb-3">
                    <label for="input-order-sync" class="col-sm-2 col-form-label">{{ entry_order_sync }}</label>
                    <div class="col-sm-10">
                      <div class="form-check form-switch">
                        <input type="checkbox" name="order_sync_enabled" value="1" id="input-order-sync" class="form-check-input" {% if order_sync_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="input-order-sync">{{ text_enabled }}</label>
                      </div>
                      <div class="form-text">{{ help_order_sync }}</div>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-auto-confirm" class="col-sm-2 col-form-label">{{ entry_auto_confirm }}</label>
                    <div class="col-sm-10">
                      <div class="form-check form-switch">
                        <input type="checkbox" name="auto_confirm_orders" value="1" id="input-auto-confirm" class="form-check-input" {% if auto_confirm_orders %}checked{% endif %}>
                        <label class="form-check-label" for="input-auto-confirm">{{ text_enabled }}</label>
                      </div>
                      <div class="form-text">{{ help_auto_confirm }}</div>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-inventory-deduction" class="col-sm-2 col-form-label">{{ entry_inventory_deduction }}</label>
                    <div class="col-sm-10">
                      <select name="inventory_deduction" id="input-inventory-deduction" class="form-select">
                        {% for key, value in inventory_deduction_options %}
                          <option value="{{ key }}" {% if key == inventory_deduction %}selected{% endif %}>{{ value }}</option>
                        {% endfor %}
                      </select>
                      <div class="form-text">{{ help_inventory_deduction }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- تبويب إعدادات الدفع -->
            <div class="tab-pane fade" id="v-pills-payment" role="tabpanel">
              <div class="card">
                <div class="card-header">
                  <h3 class="card-title"><i class="fas fa-credit-card"></i> {{ tab_payment }}</h3>
                </div>
                <div class="card-body">
                  <div class="row mb-3">
                    <label for="input-default-currency" class="col-sm-2 col-form-label">{{ entry_default_currency }}</label>
                    <div class="col-sm-10">
                      <select name="default_currency" id="input-default-currency" class="form-select">
                        {% for currency in currencies %}
                          <option value="{{ currency.code }}" {% if currency.code == default_currency %}selected{% endif %}>{{ currency.title }}</option>
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-tax-enabled" class="col-sm-2 col-form-label">{{ entry_tax_enabled }}</label>
                    <div class="col-sm-10">
                      <div class="form-check form-switch">
                        <input type="checkbox" name="tax_enabled" value="1" id="input-tax-enabled" class="form-check-input" {% if tax_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="input-tax-enabled">{{ text_enabled }}</label>
                      </div>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label for="input-tax-rate" class="col-sm-2 col-form-label">{{ entry_tax_rate }}</label>
                    <div class="col-sm-10">
                      <div class="input-group">
                        <input type="number" name="tax_rate" value="{{ tax_rate }}" placeholder="{{ entry_tax_rate }}" id="input-tax-rate" class="form-control" step="0.01" min="0" max="100"/>
                        <span class="input-group-text">%</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="row mb-3">
                    <label class="col-sm-2 col-form-label">{{ entry_payment_methods }}</label>
                    <div class="col-sm-10">
                      {% for key, value in payment_method_options %}
                        <div class="form-check">
                          <input type="checkbox" name="payment_methods[]" value="{{ key }}" id="payment-{{ key }}" class="form-check-input" {% if key in payment_methods %}checked{% endif %}>
                          <label class="form-check-label" for="payment-{{ key }}">{{ value }}</label>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- باقي التبويبات... -->
            
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

<script type="text/javascript">
// اختبار الاتصال
function testConnection() {
    $.ajax({
        url: '{{ test_connection }}',
        type: 'GET',
        dataType: 'json',
        beforeSend: function() {
            $('#button-test').html('<i class="fas fa-spinner fa-spin"></i> {{ text_testing }}');
        },
        success: function(json) {
            if (json.success) {
                alert('{{ text_connection_success }}\n\n' + 
                      '{{ text_active_products }}: ' + json.details.active_products + '\n' +
                      '{{ text_total_orders }}: ' + json.details.total_orders + '\n' +
                      '{{ text_active_customers }}: ' + json.details.active_customers);
            } else {
                alert('{{ text_connection_failed }}: ' + json.error);
            }
        },
        complete: function() {
            $('#button-test').html('<i class="fas fa-plug"></i> {{ button_test_connection }}');
        }
    });
}

// مزامنة المنتجات
function syncProducts() {
    if (confirm('{{ text_confirm_sync }}')) {
        $.ajax({
            url: '{{ sync_products }}',
            type: 'GET',
            dataType: 'json',
            beforeSend: function() {
                $('#button-sync').html('<i class="fas fa-spinner fa-spin"></i> {{ text_syncing }}');
            },
            success: function(json) {
                if (json.success) {
                    alert(json.success);
                } else {
                    alert('{{ text_sync_failed }}: ' + json.error);
                }
            },
            complete: function() {
                $('#button-sync').html('<i class="fas fa-sync"></i> {{ button_sync_products }}');
            }
        });
    }
}
</script>

{{ footer }}
