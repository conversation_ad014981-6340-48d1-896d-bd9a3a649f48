{{ header }}
<div id="account-register" class="container">
  <div class="row">
    {{ column_left }}
    <div id="content" class="col" style="padding-left:10px;padding-right:10px;padding-top:10px">{{ content_top }}
      <p>{{ text_account_already }}</p>
      <form action="{{ action }}" method="post" enctype="multipart/form-data" class="form-horizontal">
        <fieldset id="account">
            <div class="row mb-3 required">
              <label class="col-sm-1 col-form-label">{{ entry_customer_group }}</label>
              <div class="col-sm-5">
                <select name="customer_group_id" id="input-customer-group" class="form-select">
                  {% for customer_group in customer_groups %}
                    <option value="{{ customer_group.customer_group_id }}"{% if customer_group.customer_group_id == customer_group_id %} selected{% endif %}>{{ customer_group.name }}</option>
                  {% endfor %}
                </select>
              </div>
              <label class="col-sm-1 col-form-label">{{ entry_firstname }}</label>
              
            <div class="col-sm-5 required">
              <input type="text" name="firstname" value="" placeholder="{{ entry_firstname }}" id="input-firstname" class="form-control"/>
              {% if error_firstname %}
                <div class="text-danger">{{ error_firstname }}</div>
              {% endif %}

            </div>              
            </div>
            

          <div class="row mb-3 required">
              <label class="col-sm-1 col-form-label">{{ entry_telephone }}</label>
             <div class="col-sm-5 required">
              <input type="telephone" name="telephone" value="" placeholder="{{ entry_telephone }}" id="input-telephone" class="form-control"/>
                {% if error_telephone %}
                  <div class="text-danger">{{ error_telephone }}</div>
                {% endif %}

            </div>
              <label class="col-sm-1 col-form-label">{{ entry_email }}</label>
            <div class="col-sm-5">
              <input type="email" name="email" value="" placeholder="{{ entry_email }}" id="input-email" class="form-control"/>
 
               {% if error_email %}
                <div class="text-danger">{{ error_email }}</div>
              {% endif %}
              
            </div>
            

          </div>
          <div class="row mb-3 required">
              <label class="col-sm-1 col-form-label">{{ entry_password }}</label>

            <div class="col-sm-11">
              <input type="password" name="password" value="" placeholder="{{ entry_password }}" id="input-password" class="form-control" autocomplete="new-password"/>

              {% if error_password %}
                <div class="text-danger">{{ error_password }}</div>
              {% endif %}
              
            </div>
          </div>          
          <div style="display:none" class="row mb-3 required">
            <div class="col-sm-12">
              <select name="country_id" id="input-country" class="form-select">
                <option value="63" selected>{{ entry_country }}</option>
              </select>

            </div>
          </div>
          <div class="row mb-3 required">
              <label class="col-sm-1 col-form-label">{{ entry_zone }}</label>
              
            <div class="col-sm-5">
              <select name="zone_id" id="input-zone" class="form-select"></select>
              {% if error_zone %}
                <div class="text-danger">{{ error_zone }}</div>
              {% endif %}
            </div>
            <label for="input-city" class="col-sm-1 col-form-label">{{ entry_city }}</label>
            <div class="col-sm-5">
              <input type="text" name="city" value="{{ city }}" placeholder="{{ entry_city }}" id="input-city" class="form-control"/>

              {% if error_city %}
                <div class="text-danger">{{ error_city }}</div>
              {% endif %}
              
            </div>
            
          </div>
          
          <div class="row mb-3 required">
            <label for="input-address-1" class="col-sm-1 col-form-label">{{ entry_address_1 }}</label>
            <div class="col-sm-5">
              <input type="text" name="address_1" value="{{ address_1 }}" placeholder="{{ entry_address_1 }}" id="input-address-1" class="form-control"/>

              {% if error_address_1 %}
                <div class="text-danger">{{ error_address_1 }}</div>
              {% endif %}
              
            </div>
                <label for="input-address-2" class="col-sm-1 col-form-label">{{ entry_address_2 }}</label>
            <div class="col-sm-5">
              <input type="text" name="address_2" value="{{ address_2 }}" placeholder="{{ entry_address_2 }}" id="input-address-2" class="form-control"/>
                  {% if error_address_2 %}
                <div class="text-danger">{{ error_address_2 }}</div>
              {% endif %}          
            </div>        
            
          </div>

          {% for custom_field in custom_fields %}

            {% if custom_field.type == 'select' %}
              <div class="row mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <select name="custom_field[{{ custom_field.custom_field_id }}]" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-select">
                    <option value="">{{ text_select }}</option>
                    {% for custom_field_value in custom_field.custom_field_value %}
                      <option value="{{ custom_field_value.custom_field_value_id }}">{{ custom_field_value.name }}</option>
                    {% endfor %}
                  </select>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'radio' %}
              <div class="row mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div id="input-custom-field-{{ custom_field.custom_field_id }}">
                    {% for custom_field_value in custom_field.custom_field_value %}
                      <div class="form-check">
                        <input type="radio" name="custom_field[{{ custom_field.custom_field_id }}]" value="{{ custom_field_value.custom_field_value_id }}" id="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"/> <label for="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                      </div>
                    {% endfor %}
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'checkbox' %}
              <div class="row mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div id="input-custom-field-{{ custom_field.custom_field_id }}">
                    {% for custom_field_value in custom_field.custom_field_value %}
                      <div class="form-check">
                        <input type="checkbox" name="custom_field[{{ custom_field.custom_field_id }}][]" value="{{ custom_field_value.custom_field_value_id }}" id="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"/> <label for="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                      </div>
                    {% endfor %}
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'text' %}
              <div class="row mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <input type="text" name="custom_field[{{ custom_field.custom_field_id }}]" value="{{ custom_field.value }}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'textarea' %}
              <div class="row mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <textarea name="custom_field[{{ custom_field.custom_field_id }}]" rows="5" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control">{{ custom_field.value }}</textarea>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'file' %}
              <div class="row mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div>
                    <button type="button" data-oc-toggle="upload" data-oc-url="{{ upload }}" data-oc-size-max="{{ config_file_max_size }}" data-oc-size-error="{{ error_upload_size }}" data-oc-target="#input-custom-field-{{ custom_field.custom_field_id }}" class="btn btn-light"><i class="fa-solid fa-upload"></i> {{ button_upload }}</button>
                    <input type="hidden" name="custom_field[{{ custom_field.custom_field_id }}]" value="" id="input-custom-field-{{ custom_field.custom_field_id }}"/>
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'date' %}
              <div class="row mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div class="input-group">
                    <input type="text" name="custom_field[{{ custom_field.custom_field_id }}]" value="{{ custom_field.value }}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control date"/>
                    <div class="input-group-text"><i class="fa-regular fa-calendar"></i></div>
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'time' %}
              <div class="row mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div class="input-group">
                    <input type="text" name="custom_field[{{ custom_field.custom_field_id }}]" value="{{ custom_field.value }}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control time"/>
                    <div class="input-group-text"><i class="fa-regular fa-calendar"></i></div>
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'datetime' %}
              <div class="row mb-3 custom-field custom-field-{{ custom_field.custom_field_id }}">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div class="input-group">
                    <input type="text" name="custom_field[{{ custom_field.custom_field_id }}]" value="{{ custom_field.value }}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control datetime"/>
                    <div class="input-group-text"><i class="fa-regular fa-calendar"></i></div>
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

          {% endfor %}
        </fieldset>

        <fieldset style="display:none">
          <legend>{{ text_newsletter }}</legend>
          <div class="row mb-3">
            <div class="col-sm-12">
              <div class="form-check form-switch form-switch-lg">
                <input type="hidden" name="newsletter" value="0"/> <input type="checkbox" name="newsletter" value="1" id="input-newsletter" class="form-check-input" checked/>
              </div>
            </div>
          </div>
        </fieldset>
        {{ captcha }}
        <div  class="text-end">
          {% if text_agree %}
            <div style="display:none" class="form-check form-switch form-switch-lg form-check-reverse form-check-inline">
              <label class="form-check-label">{{ text_agree }}</label> <input type="checkbox" name="agree" value="1" class="form-check-input" checked/>
            </div>
          {% endif %}
          <button type="submit" class="btn btn-warning bold-btn mt-2" style="color:#000 !important;font-size:12px;width:100%">{{ button_register }}</button>
        </div>
      </form>
      {{ content_bottom }}
    </div>
    {{ column_right }}
  </div>
</div>
<script type="text/javascript"><!--
// Sort the custom fields
$('#account .form-group[data-sort]').detach().each(function () {
    if ($(this).attr('data-sort') >= 0 && $(this).attr('data-sort') <= $('#account .form-group').length) {
        $('#account .form-group').eq($(this).attr('data-sort')).before(this);
    }

    if ($(this).attr('data-sort') > $('#account .form-group').length) {
        $('#account .form-group:last').after(this);
    }

    if ($(this).attr('data-sort') == $('#account .form-group').length) {
        $('#account .form-group:last').after(this);
    }

    if ($(this).attr('data-sort') < -$('#account .form-group').length) {
        $('#account .form-group:first').before(this);
    }
});

//--></script>
<script type="text/javascript"><!--
    $.ajax({
        url: 'index.php?route=account/account/country&country_id=63',
        dataType: 'json',
        beforeSend: function () {
            $('select[name=\'country_id\']').prop('disabled', true);
        },
        complete: function () {
            $('select[name=\'country_id\']').prop('disabled', false);
        },
        success: function (json) {
            if (json['postcode_required'] == '1') {
                $('input[name=\'postcode\']').parent().parent().addClass('required');
            } else {
                $('input[name=\'postcode\']').parent().parent().removeClass('required');
            }

            html = '<option value="">{{ text_select|escape('js') }}</option>';

            if (json['zone'] && json['zone'] != '') {
                for (i = 0; i < json['zone'].length; i++) {
                    html += '<option value="' + json['zone'][i]['zone_id'] + '"';

                    if (json['zone'][i]['zone_id'] == '{{ zone_id }}') {
                        html += ' selected="selected"';
                    }

                    html += '>' + json['zone'][i]['name'] + '</option>';
                }
            } else {
                html += '<option value="0" selected="selected">{{ text_none|escape('js') }}</option>';
            }

            $('select[name=\'zone_id\']').html(html);
        },
        error: function (xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });

//--></script>


{{ footer }}
