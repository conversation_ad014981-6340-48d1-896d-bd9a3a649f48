{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- Filters -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <form id="filter-form" class="form-inline">
          <div class="form-group">
            <label for="filter_order_id">{{ text_order_id }}</label>
            <input type="text" name="filter_order_id" id="filter_order_id" class="form-control" style="width:100px;" placeholder="{{ text_enter_order_id }}" />
          </div>
          <div class="form-group">
            <label for="filter_status">{{ text_warranty_status }}</label>
            <select name="filter_status" id="filter_status" class="form-control select2" style="width:200px;">
              <option value="">{{ text_all_statuses }}</option>
              <option value="active">{{ text_active }}</option>
              <option value="expired">{{ text_expired }}</option>
              <option value="claimed">{{ text_claimed }}</option>
              <option value="void">{{ text_void }}</option>
            </select>
          </div>
          <button type="button" id="btn-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
          <button type="button" id="btn-reset" class="btn btn-default"><i class="fa fa-eraser"></i> {{ button_reset }}</button>
        </form>
      </div>
    </div>

    <div class="text-right">
      <button type="button" class="btn btn-success" id="btn-add"><i class="fa fa-plus"></i> {{ button_add_warranty }}</button>
    </div>

    <div class="panel panel-default" style="margin-top:10px;">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-shield"></i> {{ text_warranty_list }}</h3>
      </div>
      <div class="panel-body">
        <table id="warranty-table" class="table table-bordered table-striped">
          <thead>
            <tr>
              <th>{{ column_order_id }}</th>
              <th>{{ column_customer }}</th>
              <th>{{ column_product }}</th>
              <th>{{ column_start_date }}</th>
              <th>{{ column_end_date }}</th>
              <th>{{ column_warranty_status }}</th>
              <th>{{ column_actions }}</th>
            </tr>
          </thead>
          <tbody>
            <!-- DataTables AJAX -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Modal Add/Edit Warranty -->
<div class="modal fade" id="modal-warranty" tabindex="-1" role="dialog" aria-labelledby="modalWarrantyLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form id="warranty-form">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
          <h4 class="modal-title" id="modalWarrantyLabel">{{ text_add_warranty }}</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" name="warranty_id" value="" />
          <div class="form-group">
            <label>{{ text_order_id }}</label>
            <input type="number" name="order_id" class="form-control" required />
          </div>
          <div class="form-group">
            <label>{{ text_product_id }}</label>
            <input type="number" name="product_id" class="form-control" required />
          </div>
          <div class="form-group">
            <label>{{ text_customer_id }}</label>
            <input type="number" name="customer_id" class="form-control" required />
          </div>
          <div class="form-group">
            <label>{{ text_start_date }}</label>
            <div class='input-group date' id='start_date_picker'>
              <input type='text' name="start_date" class="form-control" required />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <div class="form-group">
            <label>{{ text_end_date }}</label>
            <div class='input-group date' id='end_date_picker'>
              <input type='text' name="end_date" class="form-control" required />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <div class="form-group">
            <label>{{ text_warranty_status }}</label>
            <select name="warranty_status" class="form-control">
              <option value="active">{{ text_active }}</option>
              <option value="expired">{{ text_expired }}</option>
              <option value="claimed">{{ text_claimed }}</option>
              <option value="void">{{ text_void }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_notes }}</label>
            <textarea name="notes" class="form-control" rows="3"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
          <button type="submit" class="btn btn-primary">{{ button_save }}</button>
        </div>
      </form>
    </div>
  </div>
</div>

{{ footer }}

<script type="text/javascript">
$(document).ready(function() {
    $('#filter_status').select2({ placeholder: "{{ text_all_statuses }}" });

    $('#start_date_picker').datetimepicker({ format: 'YYYY-MM-DD' });
    $('#end_date_picker').datetimepicker({ format: 'YYYY-MM-DD', useCurrent:false });

    $("#start_date_picker").on("dp.change", function (e) {
        $('#end_date_picker').data("DateTimePicker").minDate(e.date);
    });
    $("#end_date_picker").on("dp.change", function (e) {
        $('#start_date_picker').data("DateTimePicker").maxDate(e.date);
    });

    var table = $('#warranty-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ ajax_list_url }}",
            "type": "POST",
            "data": function (d) {
                d.user_token = "{{ user_token }}";
                d.filter_order_id = $('#filter_order_id').val();
                d.filter_status = $('#filter_status').val();
            }
        },
        "columns": [
            { "data": "order_id" },
            { "data": "customer_name" },
            { "data": "product_name" },
            { "data": "start_date" },
            { "data": "end_date" },
            { "data": "warranty_status" },
            { "data": "actions", "orderable": false, "searchable": false }
        ]
    });

    $('#btn-filter').on('click', function() {
        table.ajax.reload();
    });
    $('#btn-reset').on('click', function() {
        $('#filter_order_id').val('');
        $('#filter_status').val('').trigger('change');
        table.ajax.reload();
    });

    $('#btn-add').on('click', function() {
        $('#warranty-form')[0].reset();
        $('select[name="warranty_status"]').val('active');
        $('#modalWarrantyLabel').text("{{ text_add_warranty }}");
        $('#modal-warranty').modal('show');
    });

    $('#warranty-form').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: "{{ ajax_save_url }}",
            type: "POST",
            data: $(this).serialize()+'&user_token={{ user_token }}',
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else if (json.success) {
                    toastr.success(json.success);
                    $('#modal-warranty').modal('hide');
                    table.ajax.reload();
                }
            },
            error: function() {
                toastr.error("{{ text_ajax_error }}");
            }
        });
    });

    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        $.ajax({
            url: "{{ ajax_get_url }}",
            type: "POST",
            data: {warranty_id:id,user_token:"{{ user_token }}"},
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else {
                    var data = json.data;
                    $('input[name="warranty_id"]').val(data.warranty_id);
                    $('input[name="order_id"]').val(data.order_id);
                    $('input[name="product_id"]').val(data.product_id);
                    $('input[name="customer_id"]').val(data.customer_id);
                    $('input[name="start_date"]').val(data.start_date);
                    $('input[name="end_date"]').val(data.end_date);
                    $('select[name="warranty_status"]').val(data.warranty_status);
                    $('textarea[name="notes"]').val(data.notes);
                    $('#modalWarrantyLabel').text("{{ text_edit_warranty }}");
                    $('#modal-warranty').modal('show');
                }
            },
            error: function() {
                toastr.error("{{ text_ajax_error }}");
            }
        });
    });

    $(document).on('click', '.btn-delete', function() {
        var id = $(this).data('id');
        if (confirm("{{ text_confirm_delete }}")) {
            $.ajax({
                url: "{{ ajax_delete_url }}",
                type: "POST",
                data: {warranty_id:id,user_token:"{{ user_token }}"},
                dataType: "json",
                success: function(json) {
                    if (json.error) {
                        toastr.error(json.error);
                    } else {
                        toastr.success(json.success);
                        table.ajax.reload();
                    }
                },
                error: function() {
                    toastr.error("{{ text_ajax_error }}");
                }
            });
        }
    });
});
</script>
