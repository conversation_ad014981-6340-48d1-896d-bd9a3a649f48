<?php
class Controller<PERSON><PERSON><PERSON><PERSON>ooter extends Controller {
	public function index() {
		$this->load->language('common/footer');

		// Load central services
		$this->load->model('activity_log');
		$this->load->model('setting/setting');

		// System information
		$data['text_footer'] = $this->language->get('text_footer');
		$data['text_version'] = sprintf($this->language->get('text_version'), 'v2.0.0 - AI Edition');
		$data['text_powered_by'] = $this->language->get('text_powered_by');
		$data['text_support'] = $this->language->get('text_support');
		$data['text_documentation'] = $this->language->get('text_documentation');
		$data['text_system_status'] = $this->language->get('text_system_status');
		$data['text_last_login'] = $this->language->get('text_last_login');
		$data['text_session_time'] = $this->language->get('text_session_time');
		$data['text_memory_usage'] = $this->language->get('text_memory_usage');
		$data['text_server_time'] = $this->language->get('text_server_time');
		$data['text_database_queries'] = $this->language->get('text_database_queries');
		$data['text_response_time'] = $this->language->get('text_response_time');

		// Performance metrics with error handling
		$data['memory_usage'] = $this->formatBytes(memory_get_usage(true));
		$data['memory_peak'] = $this->formatBytes(memory_get_peak_usage(true));
		$data['server_time'] = date('Y-m-d H:i:s');
		$data['php_version'] = PHP_VERSION;

		// Safe database version check
		try {
			$version_result = $this->db->query("SELECT VERSION() as version");
			$data['mysql_version'] = isset($version_result->row['version']) ? $version_result->row['version'] : 'Unknown';
		} catch (Exception $e) {
			$data['mysql_version'] = 'Connection Error';
			error_log('MySQL version check failed in footer: ' . $e->getMessage());
		}

		// Session information with safety checks
		if ($this->session && isset($this->session->data['user_id'])) {
			$data['last_login'] = isset($this->session->data['last_login']) ?
				date('Y-m-d H:i:s', strtotime($this->session->data['last_login'])) :
				$this->language->get('text_unknown');
			$data['session_start'] = isset($this->session->data['session_start']) ?
				$this->session->data['session_start'] : time();
			$data['session_duration'] = $this->formatDuration(time() - $data['session_start']);
		} else {
			$data['last_login'] = $this->language->get('text_not_logged_in');
			$data['session_duration'] = '00:00:00';
		}

		// System status
		$data['system_status'] = $this->getSystemStatus();
		$data['database_queries'] = isset($this->db->query_count) ? $this->db->query_count : 0;
		$start_time = (defined('START_TIME') && constant('START_TIME')) ? constant('START_TIME') : (isset($_SERVER['REQUEST_TIME_FLOAT']) ? $_SERVER['REQUEST_TIME_FLOAT'] : microtime(true));
		$data['response_time'] = round((microtime(true) - $start_time) * 1000, 2);

		// Links
		$data['support_url'] = 'https://support.aym-erp.com';
		$data['documentation_url'] = 'https://docs.aym-erp.com';
		$data['company_url'] = 'https://aym-erp.com';

		// User permissions with fallback
		$data['show_system_info'] = $this->user && method_exists($this->user, 'hasPermission') ?
			$this->user->hasPermission('access', 'common/footer') : true;
		$data['show_performance'] = $this->user && method_exists($this->user, 'hasPermission') ?
			$this->user->hasPermission('access', 'tool/performance') : true;

		// Direction for RTL/LTR
		$data['direction'] = $this->language->get('direction');

		// Log footer view with safety checks
		if ($this->model_activity_log && method_exists($this->model_activity_log, 'addActivity')) {
			$user_id = ($this->user && method_exists($this->user, 'getId')) ? $this->user->getId() : 0;
			$this->model_activity_log->addActivity('footer', 'Footer viewed', [
				'user_id' => $user_id,
				'ip' => isset($this->request->server['REMOTE_ADDR']) ? $this->request->server['REMOTE_ADDR'] : 'unknown',
				'user_agent' => isset($this->request->server['HTTP_USER_AGENT']) ? $this->request->server['HTTP_USER_AGENT'] : 'unknown'
			]);
		}

		return $this->load->view('common/footer', $data);
	}

	public function status() {
		$this->load->language('common/footer');

		$json = [];

		if ($this->request->server['REQUEST_METHOD'] == 'GET' && isset($this->request->server['HTTP_X_REQUESTED_WITH'])) {
			// Get current system status
			$json['status'] = $this->getSystemStatus();

			// Status text mapping
			$status_texts = [
				'operational' => $this->language->get('text_operational'),
				'warning' => $this->language->get('text_warning'),
				'error' => $this->language->get('text_error'),
				'high_memory' => $this->language->get('text_high_memory'),
				'low_disk_space' => $this->language->get('text_low_disk_space'),
				'database_error' => $this->language->get('text_database_error')
			];

			$json['status_text'] = isset($status_texts[$json['status']]) ? $status_texts[$json['status']] : $this->language->get('text_unknown');

			// Updated metrics
			$start_time = (defined('START_TIME') && constant('START_TIME')) ? constant('START_TIME') : (isset($_SERVER['REQUEST_TIME_FLOAT']) ? $_SERVER['REQUEST_TIME_FLOAT'] : microtime(true));
			$json['metrics'] = [
				'memory_usage' => $this->formatBytes(memory_get_usage(true)),
				'response_time' => round((microtime(true) - $start_time) * 1000, 2) . 'ms',
				'database_queries' => isset($this->db->query_count) ? $this->db->query_count : 0,
				'server_time' => date('H:i:s')
			];
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	private function formatBytes($size, $precision = 2) {
		$units = ['B', 'KB', 'MB', 'GB', 'TB'];
		$base = log($size, 1024);
		return round(pow(1024, $base - floor($base)), $precision) . ' ' . $units[floor($base)];
	}

	private function formatDuration($seconds) {
		$hours = floor($seconds / 3600);
		$minutes = floor(($seconds % 3600) / 60);
		$seconds = $seconds % 60;
		return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
	}

	private function getSystemStatus() {
		$status = 'operational';

		// Check database connection
		try {
			$this->db->query("SELECT 1");
		} catch (Exception $exception) {
			$status = 'database_error';
			error_log('Database connection error in footer: ' . $exception->getMessage());
		}

		// Check memory usage
		$memory_usage = memory_get_usage(true);
		$memory_limit = $this->parseSize(ini_get('memory_limit'));
		if ($memory_usage > ($memory_limit * 0.9)) {
			$status = 'high_memory';
		}

		// Check disk space
		$free_space = disk_free_space(DIR_APPLICATION);
		$total_space = disk_total_space(DIR_APPLICATION);
		if ($free_space < ($total_space * 0.1)) {
			$status = 'low_disk_space';
		}

		return $status;
	}

	private function parseSize($size) {
		$unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
		$size = preg_replace('/[^0-9\.]/', '', $size);
		if ($unit) {
			return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
		} else {
			return round($size);
		}
	}
}
