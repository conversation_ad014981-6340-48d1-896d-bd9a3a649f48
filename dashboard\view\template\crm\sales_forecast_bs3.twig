{{ header }}{{ column_left }}

{# توقعات المبيعات - Sales Forecast #}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-forecast').toggleClass('hidden');" class="btn btn-default visible-xs">
          <i class="fa fa-filter"></i>
        </button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
        <button type="button" data-toggle="tooltip" title="{{ button_compare }}" onclick="$('#modal-compare').modal('show');" class="btn btn-info">
          <i class="fa fa-line-chart"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="{{ button_export }}" onclick="exportForecasts();" class="btn btn-success">
          <i class="fa fa-download"></i>
        </button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {# إحصائيات سريعة #}
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-body text-center">
            <div class="row">
              <div class="col-xs-8">
                <h4>{{ statistics.total_forecasts }}</h4>
                <p>{{ text_total_forecasts }}</p>
              </div>
              <div class="col-xs-4">
                <i class="fa fa-line-chart fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-body text-center">
            <div class="row">
              <div class="col-xs-8">
                <h4>{{ statistics.avg_accuracy }}%</h4>
                <p>{{ text_avg_accuracy }}</p>
              </div>
              <div class="col-xs-4">
                <i class="fa fa-bullseye fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-body text-center">
            <div class="row">
              <div class="col-xs-8">
                <h4>{{ statistics.best_method }}</h4>
                <p>{{ text_best_method }}</p>
              </div>
              <div class="col-xs-4">
                <i class="fa fa-trophy fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-body text-center">
            <div class="row">
              <div class="col-xs-8">
                <h4>{{ statistics.active_forecasts }}</h4>
                <p>{{ text_active_forecasts }}</p>
              </div>
              <div class="col-xs-4">
                <i class="fa fa-clock-o fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    {# الرسوم البيانية المتقدمة #}
    <div class="row">
      <div class="col-lg-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-area-chart"></i>
              {{ text_forecast_vs_actual }}
            </h3>
            <div class="pull-right">
              <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-default btn-sm">
                  <input type="radio" name="sales-period" value="7" autocomplete="off"> 7{{ text_days }}
                </label>
                <label class="btn btn-default btn-sm active">
                  <input type="radio" name="sales-period" value="30" autocomplete="off" checked> 30{{ text_days }}
                </label>
                <label class="btn btn-default btn-sm">
                  <input type="radio" name="sales-period" value="90" autocomplete="off"> 90{{ text_days }}
                </label>
              </div>
            </div>
          </div>
          <div class="panel-body">
            <canvas id="forecastVsActualChart" height="400"></canvas>
          </div>
        </div>
      </div>
      
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-pie-chart"></i>
              {{ text_method_accuracy }}
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="methodAccuracyChart" height="400"></canvas>
          </div>
        </div>
      </div>
    </div>

    {# اتجاه الدقة #}
    <div class="row">
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-line-chart"></i>
              {{ text_accuracy_trend }}
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="accuracyTrendChart" height="300"></canvas>
          </div>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-bar-chart"></i>
              {{ text_confidence_intervals }}
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="confidenceIntervalsChart" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>

    {# الفلاتر المتقدمة #}
    <div class="panel panel-default hidden-xs" id="filter-forecast">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i>
          {{ text_filter }}
        </h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-period" class="control-label">{{ entry_period }}</label>
              <select name="filter_period" id="input-period" class="form-control">
                <option value="">{{ text_all_periods }}</option>
                {% for period in periods %}
                  <option value="{{ period.value }}"{% if period.value == filter_period %} selected{% endif %}>{{ period.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-type" class="control-label">{{ entry_type }}</label>
              <select name="filter_type" id="input-type" class="form-control">
                <option value="">{{ text_all_types }}</option>
                {% for type in types %}
                  <option value="{{ type.value }}"{% if type.value == filter_type %} selected{% endif %}>{{ type.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-method" class="control-label">{{ entry_method }}</label>
              <select name="filter_method" id="input-method" class="form-control">
                <option value="">{{ text_all_methods }}</option>
                {% for method in methods %}
                  <option value="{{ method.value }}"{% if method.value == filter_method %} selected{% endif %}>{{ method.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-status" class="control-label">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                {% for status in statuses %}
                  <option value="{{ status.value }}"{% if status.value == filter_status %} selected{% endif %}>{{ status.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-accuracy" class="control-label">{{ entry_accuracy }}</label>
              <select name="filter_accuracy" id="input-accuracy" class="form-control">
                <option value="">{{ text_all_accuracy }}</option>
                <option value="high"{% if filter_accuracy == 'high' %} selected{% endif %}>{{ text_high_accuracy }} (>90%)</option>
                <option value="medium"{% if filter_accuracy == 'medium' %} selected{% endif %}>{{ text_medium_accuracy }} (70-90%)</option>
                <option value="low"{% if filter_accuracy == 'low' %} selected{% endif %}>{{ text_low_accuracy }} (<70%)</option>
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-date-from" class="control-label">{{ entry_date_from }}</label>
              <input type="date" name="filter_date_from" value="{{ filter_date_from }}" id="input-date-from" class="form-control">
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-date-to" class="control-label">{{ entry_date_to }}</label>
              <input type="date" name="filter_date_to" value="{{ filter_date_to }}" id="input-date-to" class="form-control">
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label class="control-label">&nbsp;</label>
              <div>
                <button type="button" onclick="filter();" class="btn btn-primary btn-block">
                  <i class="fa fa-search"></i> {{ button_search }}
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="text-right">
          <button type="button" id="button-filter" class="btn btn-default">
            <i class="fa fa-eraser"></i> {{ button_clear }}
          </button>
        </div>
      </div>
    </div>

    {# جدول التوقعات #}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-list"></i>
          {{ text_list }}
        </h3>
        <div class="pull-right">
          <div class="input-group input-group-sm" style="width: 200px;">
            <input type="text" name="search" class="form-control" placeholder="{{ text_search }}" value="{{ search }}">
            <span class="input-group-btn">
              <button type="submit" class="btn btn-default">
                <i class="fa fa-search"></i>
              </button>
            </span>
          </div>
        </div>
      </div>
      <div class="panel-body table-responsive" style="padding: 0;">
        <form id="form-forecast" method="post" data-oc-toggle="ajax" data-oc-load="{{ action }}" data-oc-target="#content">
          <table class="table table-hover">
            <thead>
              <tr>
                <th style="width: 1px;" class="text-center">
                  <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', $(this).prop('checked'));">
                </th>
                <th>
                  <a href="{{ sort_period }}">
                    {{ column_period }}
                    {% if sort == 'period' %}
                      <i class="fa fa-sort-{{ order|lower == 'desc' ? 'desc' : 'asc' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th>{{ column_type }}</th>
                <th>{{ column_method }}</th>
                <th>
                  <a href="{{ sort_predicted }}">
                    {{ column_predicted_amount }}
                    {% if sort == 'predicted_amount' %}
                      <i class="fa fa-sort-{{ order|lower == 'desc' ? 'desc' : 'asc' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th>{{ column_actual_amount }}</th>
                <th>
                  <a href="{{ sort_accuracy }}">
                    {{ column_accuracy }}
                    {% if sort == 'accuracy' %}
                      <i class="fa fa-sort-{{ order|lower == 'desc' ? 'desc' : 'asc' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th>{{ column_confidence }}</th>
                <th>{{ column_status }}</th>
                <th>
                  <a href="{{ sort_date }}">
                    {{ column_date_created }}
                    {% if sort == 'date_created' %}
                      <i class="fa fa-sort-{{ order|lower == 'desc' ? 'desc' : 'asc' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th class="text-right">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% if forecasts %}
                {% for forecast in forecasts %}
                  <tr>
                    <td class="text-center">
                      <input type="checkbox" name="selected[]" value="{{ forecast.forecast_id }}">
                    </td>
                    <td>
                      <div>
                        <strong>{{ forecast.period }}</strong>
                        <br><small class="text-muted">{{ forecast.start_date }} - {{ forecast.end_date }}</small>
                      </div>
                    </td>
                    <td>
                      <span class="label label-{{ forecast.type_color }}">{{ forecast.forecast_type }}</span>
                    </td>
                    <td>
                      <span class="label label-{{ forecast.method_color }}">{{ forecast.method }}</span>
                    </td>
                    <td>
                      <strong>{{ forecast.predicted_amount }}</strong>
                      {% if forecast.confidence_interval_lower and forecast.confidence_interval_upper %}
                        <br><small class="text-muted">{{ forecast.confidence_interval_lower }} - {{ forecast.confidence_interval_upper }}</small>
                      {% endif %}
                    </td>
                    <td>
                      {% if forecast.actual_amount %}
                        <strong>{{ forecast.actual_amount }}</strong>
                        {% if forecast.variance %}
                          <br><small class="text-{{ forecast.variance > 0 ? 'success' : 'danger' }}">
                            {{ forecast.variance > 0 ? '+' : '' }}{{ forecast.variance }}%
                          </small>
                        {% endif %}
                      {% else %}
                        <span class="text-muted">{{ text_pending }}</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if forecast.accuracy %}
                        <div>
                          <div class="progress" style="margin-bottom: 5px;">
                            <div class="progress-bar progress-bar-{{ forecast.accuracy_color }}" style="width: {{ forecast.accuracy }}%"></div>
                          </div>
                          <span class="label label-default">{{ forecast.accuracy }}%</span>
                        </div>
                      {% else %}
                        <span class="text-muted">{{ text_not_validated }}</span>
                      {% endif %}
                    </td>
                    <td>
                      <span class="label label-default">{{ forecast.confidence_level }}%</span>
                    </td>
                    <td>
                      <span class="label label-{{ forecast.status_color }}">{{ forecast.status }}</span>
                    </td>
                    <td>{{ forecast.date_created }}</td>
                    <td class="text-right">
                      <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">
                          <i class="fa fa-cog"></i> <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-right">
                          <li><a href="{{ forecast.view }}"><i class="fa fa-eye"></i> {{ text_view }}</a></li>
                          <li><a href="{{ forecast.edit }}"><i class="fa fa-edit"></i> {{ text_edit }}</a></li>
                          <li class="divider"></li>
                          {% if not forecast.actual_amount %}
                            <li><a href="javascript:void(0);" onclick="validateForecast({{ forecast.forecast_id }});"><i class="fa fa-check"></i> {{ text_validate }}</a></li>
                          {% endif %}
                          <li><a href="javascript:void(0);" onclick="duplicateForecast({{ forecast.forecast_id }});"><i class="fa fa-copy"></i> {{ text_duplicate }}</a></li>
                          <li class="divider"></li>
                          <li><a href="javascript:void(0);" onclick="deleteForecast({{ forecast.forecast_id }});" class="text-danger"><i class="fa fa-trash"></i> {{ text_delete }}</a></li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              {% else %}
                <tr>
                  <td class="text-center" colspan="11">{{ text_no_results }}</td>
                </tr>
              {% endif %}
            </tbody>
          </table>
        </form>
      </div>
      <div class="panel-footer">
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>
