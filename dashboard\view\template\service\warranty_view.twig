{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="service\warranty-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="service\warranty-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_delete_url">{{ text_ajax_delete_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_delete_url" value="{{ ajax_delete_url }}" placeholder="{{ text_ajax_delete_url }}" id="input-ajax_delete_url" class="form-control" />
              {% if error_ajax_delete_url %}
                <div class="invalid-feedback">{{ error_ajax_delete_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_get_url">{{ text_ajax_get_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_get_url" value="{{ ajax_get_url }}" placeholder="{{ text_ajax_get_url }}" id="input-ajax_get_url" class="form-control" />
              {% if error_ajax_get_url %}
                <div class="invalid-feedback">{{ error_ajax_get_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_list_url">{{ text_ajax_list_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_list_url" value="{{ ajax_list_url }}" placeholder="{{ text_ajax_list_url }}" id="input-ajax_list_url" class="form-control" />
              {% if error_ajax_list_url %}
                <div class="invalid-feedback">{{ error_ajax_list_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_save_url">{{ text_ajax_save_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_save_url" value="{{ ajax_save_url }}" placeholder="{{ text_ajax_save_url }}" id="input-ajax_save_url" class="form-control" />
              {% if error_ajax_save_url %}
                <div class="invalid-feedback">{{ error_ajax_save_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_add_warranty">{{ text_button_add_warranty }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_add_warranty" value="{{ button_add_warranty }}" placeholder="{{ text_button_add_warranty }}" id="input-button_add_warranty" class="form-control" />
              {% if error_button_add_warranty %}
                <div class="invalid-feedback">{{ error_button_add_warranty }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_close">{{ text_button_close }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_close" value="{{ button_close }}" placeholder="{{ text_button_close }}" id="input-button_close" class="form-control" />
              {% if error_button_close %}
                <div class="invalid-feedback">{{ error_button_close }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_filter">{{ text_button_filter }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_filter" value="{{ button_filter }}" placeholder="{{ text_button_filter }}" id="input-button_filter" class="form-control" />
              {% if error_button_filter %}
                <div class="invalid-feedback">{{ error_button_filter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_reset">{{ text_button_reset }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_reset" value="{{ button_reset }}" placeholder="{{ text_button_reset }}" id="input-button_reset" class="form-control" />
              {% if error_button_reset %}
                <div class="invalid-feedback">{{ error_button_reset }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_save">{{ text_button_save }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_save" value="{{ button_save }}" placeholder="{{ text_button_save }}" id="input-button_save" class="form-control" />
              {% if error_button_save %}
                <div class="invalid-feedback">{{ error_button_save }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_actions">{{ text_column_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_actions" value="{{ column_actions }}" placeholder="{{ text_column_actions }}" id="input-column_actions" class="form-control" />
              {% if error_column_actions %}
                <div class="invalid-feedback">{{ error_column_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_customer">{{ text_column_customer }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_customer" value="{{ column_customer }}" placeholder="{{ text_column_customer }}" id="input-column_customer" class="form-control" />
              {% if error_column_customer %}
                <div class="invalid-feedback">{{ error_column_customer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_end_date">{{ text_column_end_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_end_date" value="{{ column_end_date }}" placeholder="{{ text_column_end_date }}" id="input-column_end_date" class="form-control" />
              {% if error_column_end_date %}
                <div class="invalid-feedback">{{ error_column_end_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_order_id">{{ text_column_order_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_order_id" value="{{ column_order_id }}" placeholder="{{ text_column_order_id }}" id="input-column_order_id" class="form-control" />
              {% if error_column_order_id %}
                <div class="invalid-feedback">{{ error_column_order_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_product">{{ text_column_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_product" value="{{ column_product }}" placeholder="{{ text_column_product }}" id="input-column_product" class="form-control" />
              {% if error_column_product %}
                <div class="invalid-feedback">{{ error_column_product }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_start_date">{{ text_column_start_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_start_date" value="{{ column_start_date }}" placeholder="{{ text_column_start_date }}" id="input-column_start_date" class="form-control" />
              {% if error_column_start_date %}
                <div class="invalid-feedback">{{ error_column_start_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_warranty_status">{{ text_column_warranty_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_warranty_status" value="{{ column_warranty_status }}" placeholder="{{ text_column_warranty_status }}" id="input-column_warranty_status" class="form-control" />
              {% if error_column_warranty_status %}
                <div class="invalid-feedback">{{ error_column_warranty_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-customer_id">{{ text_customer_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="customer_id" value="{{ customer_id }}" placeholder="{{ text_customer_id }}" id="input-customer_id" class="form-control" />
              {% if error_customer_id %}
                <div class="invalid-feedback">{{ error_customer_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-description">{{ text_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="description" value="{{ description }}" placeholder="{{ text_description }}" id="input-description" class="form-control" />
              {% if error_description %}
                <div class="invalid-feedback">{{ error_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order_id">{{ text_order_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="order_id" value="{{ order_id }}" placeholder="{{ text_order_id }}" id="input-order_id" class="form-control" />
              {% if error_order_id %}
                <div class="invalid-feedback">{{ error_order_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-period_unit">{{ text_period_unit }}</label>
            <div class="col-sm-10">
              <input type="text" name="period_unit" value="{{ period_unit }}" placeholder="{{ text_period_unit }}" id="input-period_unit" class="form-control" />
              {% if error_period_unit %}
                <div class="invalid-feedback">{{ error_period_unit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_id">{{ text_product_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_id" value="{{ product_id }}" placeholder="{{ text_product_id }}" id="input-product_id" class="form-control" />
              {% if error_product_id %}
                <div class="invalid-feedback">{{ error_product_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-start_date">{{ text_start_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="start_date" value="{{ start_date }}" placeholder="{{ text_start_date }}" id="input-start_date" class="form-control" />
              {% if error_start_date %}
                <div class="invalid-feedback">{{ error_start_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-terms_conditions">{{ text_terms_conditions }}</label>
            <div class="col-sm-10">
              <input type="text" name="terms_conditions" value="{{ terms_conditions }}" placeholder="{{ text_terms_conditions }}" id="input-terms_conditions" class="form-control" />
              {% if error_terms_conditions %}
                <div class="invalid-feedback">{{ error_terms_conditions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_active">{{ text_text_active }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_active" value="{{ text_active }}" placeholder="{{ text_text_active }}" id="input-text_active" class="form-control" />
              {% if error_text_active %}
                <div class="invalid-feedback">{{ error_text_active }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_add_warranty">{{ text_text_add_warranty }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_add_warranty" value="{{ text_add_warranty }}" placeholder="{{ text_text_add_warranty }}" id="input-text_add_warranty" class="form-control" />
              {% if error_text_add_warranty %}
                <div class="invalid-feedback">{{ error_text_add_warranty }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_ajax_error">{{ text_text_ajax_error }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_ajax_error" value="{{ text_ajax_error }}" placeholder="{{ text_text_ajax_error }}" id="input-text_ajax_error" class="form-control" />
              {% if error_text_ajax_error %}
                <div class="invalid-feedback">{{ error_text_ajax_error }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_all_statuses">{{ text_text_all_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_all_statuses" value="{{ text_all_statuses }}" placeholder="{{ text_text_all_statuses }}" id="input-text_all_statuses" class="form-control" />
              {% if error_text_all_statuses %}
                <div class="invalid-feedback">{{ error_text_all_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_claimed">{{ text_text_claimed }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_claimed" value="{{ text_claimed }}" placeholder="{{ text_text_claimed }}" id="input-text_claimed" class="form-control" />
              {% if error_text_claimed %}
                <div class="invalid-feedback">{{ error_text_claimed }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_confirm_delete">{{ text_text_confirm_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_confirm_delete" value="{{ text_confirm_delete }}" placeholder="{{ text_text_confirm_delete }}" id="input-text_confirm_delete" class="form-control" />
              {% if error_text_confirm_delete %}
                <div class="invalid-feedback">{{ error_text_confirm_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_customer_id">{{ text_text_customer_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_customer_id" value="{{ text_customer_id }}" placeholder="{{ text_text_customer_id }}" id="input-text_customer_id" class="form-control" />
              {% if error_text_customer_id %}
                <div class="invalid-feedback">{{ error_text_customer_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_edit_warranty">{{ text_text_edit_warranty }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_edit_warranty" value="{{ text_edit_warranty }}" placeholder="{{ text_text_edit_warranty }}" id="input-text_edit_warranty" class="form-control" />
              {% if error_text_edit_warranty %}
                <div class="invalid-feedback">{{ error_text_edit_warranty }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_end_date">{{ text_text_end_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_end_date" value="{{ text_end_date }}" placeholder="{{ text_text_end_date }}" id="input-text_end_date" class="form-control" />
              {% if error_text_end_date %}
                <div class="invalid-feedback">{{ error_text_end_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_enter_order_id">{{ text_text_enter_order_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_enter_order_id" value="{{ text_enter_order_id }}" placeholder="{{ text_text_enter_order_id }}" id="input-text_enter_order_id" class="form-control" />
              {% if error_text_enter_order_id %}
                <div class="invalid-feedback">{{ error_text_enter_order_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_expired">{{ text_text_expired }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_expired" value="{{ text_expired }}" placeholder="{{ text_text_expired }}" id="input-text_expired" class="form-control" />
              {% if error_text_expired %}
                <div class="invalid-feedback">{{ error_text_expired }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_filter">{{ text_text_filter }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_filter" value="{{ text_filter }}" placeholder="{{ text_text_filter }}" id="input-text_filter" class="form-control" />
              {% if error_text_filter %}
                <div class="invalid-feedback">{{ error_text_filter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_notes">{{ text_text_notes }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_notes" value="{{ text_notes }}" placeholder="{{ text_text_notes }}" id="input-text_notes" class="form-control" />
              {% if error_text_notes %}
                <div class="invalid-feedback">{{ error_text_notes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_order_id">{{ text_text_order_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_order_id" value="{{ text_order_id }}" placeholder="{{ text_text_order_id }}" id="input-text_order_id" class="form-control" />
              {% if error_text_order_id %}
                <div class="invalid-feedback">{{ error_text_order_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_product_id">{{ text_text_product_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_product_id" value="{{ text_product_id }}" placeholder="{{ text_text_product_id }}" id="input-text_product_id" class="form-control" />
              {% if error_text_product_id %}
                <div class="invalid-feedback">{{ error_text_product_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_start_date">{{ text_text_start_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_start_date" value="{{ text_start_date }}" placeholder="{{ text_text_start_date }}" id="input-text_start_date" class="form-control" />
              {% if error_text_start_date %}
                <div class="invalid-feedback">{{ error_text_start_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_void">{{ text_text_void }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_void" value="{{ text_void }}" placeholder="{{ text_text_void }}" id="input-text_void" class="form-control" />
              {% if error_text_void %}
                <div class="invalid-feedback">{{ error_text_void }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_warranty_list">{{ text_text_warranty_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_warranty_list" value="{{ text_warranty_list }}" placeholder="{{ text_text_warranty_list }}" id="input-text_warranty_list" class="form-control" />
              {% if error_text_warranty_list %}
                <div class="invalid-feedback">{{ error_text_warranty_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_warranty_status">{{ text_text_warranty_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_warranty_status" value="{{ text_warranty_status }}" placeholder="{{ text_text_warranty_status }}" id="input-text_warranty_status" class="form-control" />
              {% if error_text_warranty_status %}
                <div class="invalid-feedback">{{ error_text_warranty_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warranties">{{ text_warranties }}</label>
            <div class="col-sm-10">
              <input type="text" name="warranties" value="{{ warranties }}" placeholder="{{ text_warranties }}" id="input-warranties" class="form-control" />
              {% if error_warranties %}
                <div class="invalid-feedback">{{ error_warranties }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warranty">{{ text_warranty }}</label>
            <div class="col-sm-10">
              <input type="text" name="warranty" value="{{ warranty }}" placeholder="{{ text_warranty }}" id="input-warranty" class="form-control" />
              {% if error_warranty %}
                <div class="invalid-feedback">{{ error_warranty }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warranty_history">{{ text_warranty_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="warranty_history" value="{{ warranty_history }}" placeholder="{{ text_warranty_history }}" id="input-warranty_history" class="form-control" />
              {% if error_warranty_history %}
                <div class="invalid-feedback">{{ error_warranty_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warranty_number">{{ text_warranty_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="warranty_number" value="{{ warranty_number }}" placeholder="{{ text_warranty_number }}" id="input-warranty_number" class="form-control" />
              {% if error_warranty_number %}
                <div class="invalid-feedback">{{ error_warranty_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warranty_period">{{ text_warranty_period }}</label>
            <div class="col-sm-10">
              <input type="text" name="warranty_period" value="{{ warranty_period }}" placeholder="{{ text_warranty_period }}" id="input-warranty_period" class="form-control" />
              {% if error_warranty_period %}
                <div class="invalid-feedback">{{ error_warranty_period }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warranty_type">{{ text_warranty_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="warranty_type" value="{{ warranty_type }}" placeholder="{{ text_warranty_type }}" id="input-warranty_type" class="form-control" />
              {% if error_warranty_type %}
                <div class="invalid-feedback">{{ error_warranty_type }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}