{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    <!-- Filters -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <form id="filter-form" class="form-inline">
          <div class="form-group">
            <label>{{ text_campaign_name }}</label>
            <input type="text" name="filter_name" id="filter_name" class="form-control" placeholder="{{ text_enter_campaign_name }}" style="width:200px;" />
          </div>
          <div class="form-group">
            <label>{{ text_date_start }}</label>
            <div class='input-group date' id='filter_date_start'>
              <input type='text' name="filter_date_start" class="form-control" placeholder="YYYY-MM-DD" />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <div class="form-group">
            <label>{{ text_date_end }}</label>
            <div class='input-group date' id='filter_date_end'>
              <input type='text' name="filter_date_end" class="form-control" placeholder="YYYY-MM-DD" />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <button type="button" id="btn-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
          <button type="button" id="btn-reset" class="btn btn-default"><i class="fa fa-eraser"></i> {{ button_reset }}</button>
        </form>
      </div>
    </div>
    
    <div class="text-right">
      <button type="button" class="btn btn-success" id="btn-add"><i class="fa fa-plus"></i> {{ button_add_campaign }}</button>
    </div>
    
    <div class="panel panel-default" style="margin-top:10px;">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bullhorn"></i> {{ text_campaign_list }}</h3>
      </div>
      <div class="panel-body">
        <table id="campaign-table" class="table table-bordered table-striped">
          <thead>
            <tr>
              <th>{{ column_name }}</th>
              <th>{{ column_type }}</th>
              <th>{{ column_start_date }}</th>
              <th>{{ column_end_date }}</th>
              <th>{{ column_budget }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_actions }}</th>
            </tr>
          </thead>
          <tbody>
            <!-- DataTables AJAX -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- عرض احصائيات الفترة: زيارات وأوامر -->
    <div class="panel panel-default">
      <div class="panel-heading"><h3 class="panel-title">{{ text_period_stats }}</h3></div>
      <div class="panel-body" id="period-stats">
        <p>{{ text_visits }}: <span id="stats-visits">0</span></p>
        <p>{{ text_orders }}: <span id="stats-orders">0</span></p>
      </div>
    </div>
  </div>
</div>

<!-- Modal Add/Edit Campaign -->
<div class="modal fade" id="modal-campaign" tabindex="-1" role="dialog" aria-labelledby="modalCampaignLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form id="campaign-form">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
          <h4 class="modal-title" id="modalCampaignLabel">{{ text_add_campaign }}</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" name="campaign_id" value="" />
          <div class="form-group">
            <label>{{ text_name }}</label>
            <input type="text" name="name" class="form-control" required />
          </div>
          <div class="form-group">
            <label>{{ text_type }}</label>
            <select name="type" class="form-control">
              <option value="other">{{ text_type_other }}</option>
              <option value="seo">SEO</option>
              <option value="adwords">AdWords</option>
              <option value="social_media">Social Media</option>
              <option value="email">Email</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_start_date }}</label>
            <div class='input-group date' id='start_date_picker'>
              <input type='text' name="start_date" class="form-control" required />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <div class="form-group">
            <label>{{ text_end_date }}</label>
            <div class='input-group date' id='end_date_picker'>
              <input type='text' name="end_date" class="form-control" required />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <div class="form-group">
            <label>{{ text_budget }}</label>
            <input type="number" step="0.0001" name="budget" class="form-control" required />
          </div>
          <div class="form-group">
            <label>{{ text_code }}</label>
            <input type="text" name="code" class="form-control" placeholder="{{ text_code_help }}" />
          </div>
          <div class="form-group">
            <label>{{ text_status }}</label>
            <select name="status" class="form-control">
              <option value="active">{{ text_status_active }}</option>
              <option value="inactive">{{ text_status_inactive }}</option>
              <option value="completed">{{ text_status_completed }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_assigned_to }}</label>
            <select name="assigned_to_user_id" class="form-control select2" style="width:100%;">
              <option value="">{{ text_select_user }}</option>
              {% for user in users %}
              <option value="{{ user.user_id }}">{{ user.firstname }} {{ user.lastname }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_actual_spend }}</label>
            <input type="number" step="0.0001" name="actual_spend" class="form-control" value="0.0000" />
          </div>
          <div class="form-group">
            <label>{{ text_invoice_reference }}</label>
            <input type="text" name="invoice_reference" class="form-control" placeholder="{{ text_invoice_reference_help }}" />
          </div>
          <div class="form-group">
            <label><input type="checkbox" name="add_expense" value="1" /> {{ text_add_expense }}</label>
            <p class="help-block">{{ text_add_expense_help }}</p>
          </div>
          <div class="form-group">
            <label>{{ text_notes }}</label>
            <textarea name="notes" class="form-control" rows="3"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
          <button type="submit" class="btn btn-primary">{{ button_save }}</button>
        </div>
      </form>
    </div>
  </div>
</div>

{{ footer }}

<script type="text/javascript">
$(document).ready(function() {
    $('#filter_date_start').datetimepicker({ format: 'YYYY-MM-DD' });
    $('#filter_date_end').datetimepicker({ format: 'YYYY-MM-DD', useCurrent:false });

    $("#filter_date_start").on("dp.change", function (e) {
        $('#filter_date_end').data("DateTimePicker").minDate(e.date);
    });
    $("#filter_date_end").on("dp.change", function (e) {
        $('#filter_date_start').data("DateTimePicker").maxDate(e.date);
    });

    $('#start_date_picker').datetimepicker({ format: 'YYYY-MM-DD' });
    $('#end_date_picker').datetimepicker({ format: 'YYYY-MM-DD' });

    $('select[name="assigned_to_user_id"]').select2({ placeholder: "{{ text_select_user }}" });

    var table = $('#campaign-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ ajax_list_url }}",
            "type": "POST",
            "data": function (d) {
                d.user_token = "{{ user_token }}";
                d.filter_name = $('#filter_name').val();
                d.filter_date_start = $('input[name="filter_date_start"]').val();
                d.filter_date_end = $('input[name="filter_date_end"]').val();
            }
        },
        "columns": [
            { "data": "name" },
            { "data": "type" },
            { "data": "start_date" },
            { "data": "end_date" },
            { "data": "budget" },
            { "data": "status" },
            { "data": "actions", "orderable": false, "searchable": false }
        ]
    });

    // تحديث الاحصائيات الفترة
    function loadPeriodStats() {
        $.ajax({
            url: "{{ ajax_period_stats_url }}",
            type: "POST",
            data: {
                user_token: "{{ user_token }}",
                filter_date_start: $('input[name="filter_date_start"]').val(),
                filter_date_end: $('input[name="filter_date_end"]').val()
            },
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else {
                    $('#stats-visits').text(json.visits);
                    $('#stats-orders').text(json.orders);
                }
            },
            error: function() {
                toastr.error("{{ text_ajax_error }}");
            }
        });
    }

    $('#btn-filter').on('click', function() {
        table.ajax.reload();
        loadPeriodStats();
    });

    $('#btn-reset').on('click', function() {
        $('#filter_name').val('');
        $('input[name="filter_date_start"]').val('');
        $('input[name="filter_date_end"]').val('');
        table.ajax.reload();
        loadPeriodStats();
    });

    $('#btn-add').on('click', function() {
        $('#campaign-form')[0].reset();
        $('input[name="campaign_id"]').val('');
        $('select[name="type"]').val('other');
        $('select[name="status"]').val('active');
        $('select[name="assigned_to_user_id"]').val('').trigger('change');
        $('input[name="add_expense"]').prop('checked', false);
        $('#modalCampaignLabel').text("{{ text_add_campaign }}");
        $('#modal-campaign').modal('show');
    });

    $('#campaign-form').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: "{{ ajax_save_url }}",
            type: "POST",
            data: $(this).serialize()+'&user_token={{ user_token }}',
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else if (json.success) {
                    toastr.success(json.success);
                    $('#modal-campaign').modal('hide');
                    table.ajax.reload();
                }
            },
            error: function() {
                toastr.error("{{ text_ajax_error }}");
            }
        });
    });

    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        $.ajax({
            url: "{{ ajax_get_url }}",
            type: "POST",
            data: {campaign_id:id,user_token:"{{ user_token }}"},
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else {
                    var data = json.data;
                    $('input[name="campaign_id"]').val(data.campaign_id);
                    $('input[name="name"]').val(data.name);
                    $('select[name="type"]').val(data.type);
                    $('input[name="start_date"]').val(data.start_date);
                    $('input[name="end_date"]').val(data.end_date);
                    $('input[name="budget"]').val(data.budget);
                    $('input[name="code"]').val(data.code);
                    $('select[name="status"]').val(data.status);
                    $('select[name="assigned_to_user_id"]').val(data.assigned_to_user_id).trigger('change');
                    $('input[name="actual_spend"]').val(data.actual_spend);
                    $('input[name="invoice_reference"]').val(data.invoice_reference);
                    if (data.add_expense == '1') {
                        $('input[name="add_expense"]').prop('checked', true);
                    } else {
                        $('input[name="add_expense"]').prop('checked', false);
                    }
                    $('textarea[name="notes"]').val(data.notes);
                    $('#modalCampaignLabel').text("{{ text_edit_campaign }}");
                    $('#modal-campaign').modal('show');
                }
            },
            error: function() {
                toastr.error("{{ text_ajax_error }}");
            }
        });
    });

    $(document).on('click', '.btn-delete', function() {
        var id = $(this).data('id');
        if (confirm("{{ text_confirm_delete }}")) {
            $.ajax({
                url: "{{ ajax_delete_url }}",
                type: "POST",
                data: {campaign_id:id,user_token:"{{ user_token }}"},
                dataType:"json",
                success: function(json) {
                    if (json.error) {
                        toastr.error(json.error);
                    } else {
                        toastr.success(json.success);
                        table.ajax.reload();
                    }
                },
                error: function() {
                    toastr.error("{{ text_ajax_error }}");
                }
            });
        }
    });

    // تحميل الاحصائيات عند البداية
    loadPeriodStats();
});
</script>
