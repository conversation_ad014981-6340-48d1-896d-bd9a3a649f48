<?php
// Heading
$_['heading_title']          = 'Blog Tags';

// Text
$_['text_success_add']       = 'Tag added successfully!';
$_['text_success_edit']      = 'Tag updated successfully!';
$_['text_success_delete']    = 'Tag(s) deleted successfully!';
$_['text_list']              = 'Blog Tag List';
$_['text_add']               = 'Add New Tag';
$_['text_edit']              = 'Edit Tag';
$_['text_no_results']        = 'No results';
$_['text_form']              = 'Tag Form';
$_['text_filter']            = 'Filter';
$_['text_none']              = '-- None --';
$_['text_select']            = '-- Select --';
$_['text_confirm']           = 'Are you sure?';

// Column
$_['column_name']            = 'Tag Name';
$_['column_slug']            = 'Slug';
$_['column_posts']           = 'Number of Posts';
$_['column_action']          = 'Action';

// Entry
$_['entry_name']             = 'Tag Name';
$_['entry_slug']             = 'Slug';
$_['entry_filter']           = 'Filter by Name';

// Help
$_['help_slug']              = 'The slug is the URL-friendly version of the tag name. It must be unique.';

// Tab
$_['tab_general']            = 'General';
$_['tab_posts']              = 'Posts';

// Button
$_['button_save']            = 'Save';
$_['button_cancel']          = 'Cancel';
$_['button_add']             = 'Add New';
$_['button_edit']            = 'Edit';
$_['button_delete']          = 'Delete';
$_['button_filter']          = 'Filter';
$_['button_clear']           = 'Clear';
$_['button_generate']        = 'Generate';
$_['button_back_to_blog']    = 'Back to Blog';

// Error
$_['error_permission']       = 'Warning: You do not have permission to modify blog tags!';
$_['error_name']             = 'Tag Name must be between 1 and 64 characters!';
$_['error_slug']             = 'Slug is required!';
$_['error_slug_exists']      = 'This slug already exists!';
$_['error_posts']            = 'Cannot delete this tag as it is used in %s post(s)!';
$_['error_ajax']             = 'Error in operation! Please try again.';