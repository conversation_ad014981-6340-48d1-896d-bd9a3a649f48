<?php
// Heading
$_['heading_title']       					= '<span style="color:#1e91cf; font-weight:bold">منصة باي للتجارة الإلكترونية (موصى بها)</span>'; 
$_['heading_title_main']  					= 'منصة باي للتجارة الإلكترونية';

// Text
$_['text_paypal']		 					= '<a target="_BLANK" href="https://www.paypal.com/uk/mrb/pal=V4T754QB63XXL"><img src="view/image/payment/paypal.png" alt="PayPal Checkout Integration (Highly Recommended)" title="PayPal Checkout Integration (Highly Recommended)" style="border: 1px solid #EEEEEE;" /></a>';
$_['text_extensions']     					= 'الموديولات';
$_['text_edit']          					= 'تحرير';
$_['text_welcome']							= 'PayPal Checkout lets you offer PayPal, Venmo (US only), Pay Later options, debit & credit cards, plus local payment methods — all designed to help you maximize conversion.';
$_['text_checkout_express']					= 'If your country is not available in the list when going through the PayPal onboarding experience please <a href="%s" target="_blank" class="button-connect-express-checkout" data-paypal-button="PPLtBlue" data-paypal-onboard-complete="onBoardedCallback">click here</a>.';
$_['text_support']							= 'If you face any issue during the onboarding process, please try other browser (e.g. Chrome) or clear your browser cache. If it doesn\'t help, please <a href="mailto:<EMAIL>?subject=%s%%20-%%20an%%20issue%%20during%%20the%%20onboarding%%20process&body=Please%%20provide%%20your%%20contact%%20information%%20and%%20a%%20detailed%%20description%%20of%%20the%%20issue%%20you%%20have%%20encountered.%%20It%%20would%%20be%%20helpful%%20if%%20you%%20could%%20also%%20include%%20a%%20screenshot%%20or%%20video%%20of%%20the%%20problem.">contact</a> our support team.';
$_['text_version']							= 'New PayPal Checkout Integration version available. You can download it <a href="%s" target="_blank" class="alert-link">here</a>!';
$_['text_tab_dashboard']					= 'Back to the dashboard';
$_['text_tab_general']				 		= 'General';
$_['text_tab_button']						= 'Buttons';
$_['text_tab_applepay_button']				= 'ApplePay';
$_['text_tab_card']							= 'Advanced Cards';
$_['text_tab_message']						= 'Pay Later messaging';
$_['text_tab_order_status']					= 'Order Statuses';
$_['text_tab_contact']						= 'Contact PayPal';
$_['text_all_sales']						= 'All Sales';
$_['text_paypal_sales']						= 'PayPal Sales';
$_['text_panel_statistic']					= 'Statistic';
$_['text_panel_sale_analytics']				= 'Sales Analytics';
$_['text_statistic_title']					= 'The platform that grows with you';
$_['text_statistic_description']			= 'The platform has 416 million customers';
$_['text_button_settings']					= 'Button settings';
$_['text_applepay_button_settings']			= 'ApplePay Button settings';
$_['text_card_settings']					= 'Card settings';
$_['text_message_settings']					= 'Message settings';
$_['text_day']         						= 'Today';
$_['text_week']        						= 'Week';
$_['text_month']       						= 'Month';
$_['text_year']        						= 'Year';
$_['text_on']								= 'On';
$_['text_off']								= 'Off';
$_['text_home']								= 'Home';
$_['text_product']							= 'Product';
$_['text_cart']								= 'Cart';
$_['text_checkout']							= 'Checkout';
$_['text_production']			 	 		= 'Production';
$_['text_sandbox']			 				= 'Sandbox';
$_['text_multi_button']			 			= 'Multi Button';
$_['text_one_button']			 			= 'One Button';
$_['text_authorization']			 		= 'Authorization';
$_['text_sale']			 	 				= 'Sale';
$_['text_connect']							= 'Your seller account has been connected.<br /><strong>Client ID</strong> = %s<br /><strong>Secret</strong> = %s<br /><strong>Merchant ID</strong> = %s<br /><strong>Webhook ID</strong> = %s<br /><strong>Environment</strong> = %s<br />If you would like to connect another account, please, disconnect.';
$_['text_applepay_alert']					= '<strong>You need to verify any domain names in your environment that will show an Apple Pay button.</strong><br /><br />If Apple hasn’t verified a domain, they will reject any payments from that domain. The Apple Pay payment method won’t work if the domain isn’t registered.';
$_['text_applepay_step_1']					= '<strong>Download and host live domain association file</strong><br />1. Download the domain association file for your live environment.<br />2. Host the file on your production site at /.well-known/apple-developer-merchantid-domain-association.';
$_['text_applepay_step_2']					= '<strong>Register your live domain</strong><br />1. Go to the Payment Methods page on your PayPal account.<br />2. Register all high-level domains such as business.example.com, and subdomains such as checkout.business.example.com, that show the Apple Pay button.';
$_['text_message_alert_uk']					= '<strong>Turn browsers into buyers with Pay in 3.¹</strong> Help increase sales while giving your customers flexible payments and more buying power. With Pay in 3, customers can pay over time in three interest-free payments while you get paid in full, up front on purchases — at no additional cost.';
$_['text_message_footnote_uk']				= '¹Pay in 3 availability is subject to merchant status, sector and integration. Consumer eligibility is subject to status and approval. See <a href="https://www.paypal.com/uk/webapps/mpp/paypal-payin3/terms" target="_blank">product terms</a> for more details.';
$_['text_message_alert_us']					= '<strong>Help increase your sales with our built-in Pay Later options.</strong> With PayPal Pay Later, your business can offer Pay in 4 and Pay Monthly¹ — two valuable ways for your customers to make a purchase and pay for it over time while you get paid in full, up front. Both are included at no additional cost to your business.';
$_['text_message_footnote_us']				= '¹About Pay in 4: Loans to California residents are made or arranged pursuant to a California Financing Law License. PayPal, Inc. is a Georgia Installment Lender Licensee, NMLS #910457. Rhode Island Small Loan Lender Licensee.<br />Pay Monthly is subject to consumer credit approval. 9.99-29.99% APR based on the customer’s creditworthiness. PayPal, Inc.: RI Loan Broker Licensee. The lender for Pay Monthly is WebBank.';
$_['text_currency_aud']						= 'Australian Dollar';
$_['text_currency_brl']						= 'Brazilian Real';
$_['text_currency_cad']						= 'Canadian Dollar';
$_['text_currency_czk']						= 'Czech Krone';
$_['text_currency_dkk']						= 'Danish Krone';
$_['text_currency_eur']						= 'Euro';
$_['text_currency_hkd']						= 'Hong Kong Dollar';
$_['text_currency_huf']						= 'Hungarian Forint';
$_['text_currency_inr']						= 'Indian Rupee';
$_['text_currency_ils']						= 'Israeli New Shekel';
$_['text_currency_jpy']						= 'Japanese Yen';
$_['text_currency_myr']						= 'Malaysian Ringgit';
$_['text_currency_mxn']						= 'Mexican Peso';
$_['text_currency_twd']						= 'New Taiwan Dollar';
$_['text_currency_nzd']						= 'New Zealand Dollar';
$_['text_currency_nok']						= 'Norwegian Krone';
$_['text_currency_php']						= 'Philippine peso';
$_['text_currency_pln']						= 'Polish Zloty';
$_['text_currency_gbp']						= 'Pound Sterling';
$_['text_currency_rub']						= 'Russian Ruble';
$_['text_currency_sgd']						= 'Singapore Dollar';
$_['text_currency_sek']						= 'Swedish Krone';
$_['text_currency_chf']						= 'Swiss Frank';
$_['text_currency_thb']						= 'Thai Baht';
$_['text_currency_usd']						= 'US Dollar';
$_['text_completed_status']					= 'Completed Status';
$_['text_denied_status']					= 'Denied Status';
$_['text_failed_status']					= 'Failed Status';
$_['text_pending_status']					= 'Pending Status';
$_['text_refunded_status']					= 'Refunded Status';
$_['text_reversed_status']					= 'Reversed Status';
$_['text_voided_status']					= 'Voided Status';
$_['text_insert_prepend']					= 'Insert Into Begin';
$_['text_insert_append']					= 'Insert Into End';
$_['text_insert_before']					= 'Insert Before';
$_['text_insert_after']						= 'Insert After';
$_['text_align_left']						= 'Align Left';
$_['text_align_center']						= 'Align Center';
$_['text_align_right']						= 'Align Right';
$_['text_small']			 				= 'Small';
$_['text_medium']			 	 			= 'Medium';
$_['text_large']			 	 			= 'Large';
$_['text_responsive']			 	 		= 'Responsive';
$_['text_gold']			 	 				= 'Gold';
$_['text_blue']			 	 				= 'Blue';
$_['text_silver']			 	 			= 'Silver';
$_['text_white']			 	 			= 'White';
$_['text_white_outline']			 	 	= 'White Outline';
$_['text_black']			 	 			= 'Black';
$_['text_pill']			 	 				= 'Pill';
$_['text_rect']			 	 				= 'Rect';
$_['text_checkout']			 	 			= 'Checkout';
$_['text_pay']			 	 				= 'Pay';
$_['text_buy_now']			 	 			= 'Buy Now';
$_['text_pay_pal']			 	 			= 'PayPal';
$_['text_installment']			 	 		= 'Installment';
$_['text_buy']			 	 				= 'Buy';
$_['text_donate']			 	 			= 'Donate';
$_['text_plain']			 	 			= 'Plain';
$_['text_check_out']			 	 		= 'Check out';
$_['text_card']								= 'Credit or debit cards';
$_['text_credit']							= 'PayPal Credit';
$_['text_bancontact']						= 'Bancontact';
$_['text_blik']								= 'BLIK';
$_['text_eps']								= 'Eps';
$_['text_giropay']							= 'Giropay';
$_['text_ideal']							= 'iDEAL';
$_['text_mercadopago']						= 'Mercado Pago';
$_['text_mybank']							= 'MyBank';
$_['text_p24']								= 'Przelewy24';
$_['text_sepa']								= 'SEPA-Lastschrift';
$_['text_sofort']							= 'Sofort';
$_['text_venmo']							= 'Venmo';
$_['text_paylater']							= 'Pay Later';
$_['text_auto']								= 'Auto';
$_['text_text']								= 'Text Message';
$_['text_flex']								= 'Flexible Banner';
$_['text_accept']			 	 			= 'Accept';
$_['text_decline']			 	 			= 'Decline';
$_['text_recommended']			 	 		= '(recommended)';
$_['text_3ds_failed_authentication']		= 'Failed authentication.';
$_['text_3ds_rejected_authentication']		= 'Rejected authentication.';
$_['text_3ds_attempted_authentication'] 	= 'Attempted authentication.';
$_['text_3ds_unable_authentication']		= 'Unable to complete authentication.';
$_['text_3ds_challenge_authentication']		= 'Challenge required for authentication.';
$_['text_3ds_card_ineligible']				= 'Card type and issuing bank are not ready to complete a 3D Secure authentication.';
$_['text_3ds_system_unavailable']			= 'System is unavailable at the time of the request.';
$_['text_3ds_system_bypassed'] 				= 'System has bypassed authentication.';
$_['text_payment_method_paypal']			= 'PayPal (Pay with PayPal, Card)';
$_['text_payment_method_paypal_paylater']	= 'Buy Now Pay Later with PayPal';
$_['text_payment_method_cod']				= 'Cash On Delivery';
$_['text_contact_book']						= 'Book a contact with PayPal';
$_['text_contact_form']						= 'Fill out the form to request a call back from a PayPal sales expert';
$_['text_contact_business']					= 'Business Information';
$_['text_contact_product']					= 'Product Interest';
$_['text_none']								= ' --- None --- ';
$_['text_bt_dcc']							= 'Credit Card/Debit Card Processing';
$_['text_express_checkout']					= 'PayPal Button';
$_['text_credit_installments']				= 'PayPal Credit';
$_['text_point_of_sale']					= 'PayPal InStore';
$_['text_invoicing_api']					= 'Invoicing';
$_['text_paypal_working_capital']			= 'Working Capital/Bsuiness Loans';
$_['text_risk_servicing']					= 'Risk Servicing';
$_['text_paypal_here']						= 'Subscriptions';
$_['text_payouts']							= 'Payouts';
$_['text_marketing_solutions']				= 'Marketing Solutions';
$_['text_confirm']							= 'Are you sure?';
$_['text_menu_desktops']					= 'Desktops';
$_['text_menu_laptops']						= 'Laptops';
$_['text_menu_components']					= 'Components';
$_['text_menu_tablets']						= 'Tablets';
$_['text_menu_software']					= 'Software';
$_['text_menu_cameras']						= 'Cameras';
$_['text_product_name']						= 'Product Name';
$_['text_product_price']					= '$33.00';
$_['text_product_manufacturer']				= 'Brand: DLX-sd';
$_['text_product_model']					= 'Product Code: product 11';
$_['text_product_stock']					= 'Availability: In Stock';
$_['text_cart_product_image']				= 'Image';
$_['text_cart_product_name']				= 'Product Name';
$_['text_cart_product_model']				= 'Model';
$_['text_cart_product_quantity']			= 'Quantity';
$_['text_cart_product_price']				= 'Unit Price';
$_['text_cart_product_total']				= 'Total';
$_['text_cart_product_name_value']			= 'Phone';
$_['text_cart_product_model_value']			= 'product 11';
$_['text_cart_product_quantity_value']		= '1';
$_['text_cart_product_price_value']			= '$33.00';
$_['text_cart_product_total_value']			= '$33.00';
$_['text_cart_sub_total']					= 'Sub-Total:';
$_['text_cart_total']						= 'Total:';
$_['text_step_coupon']						= 'Use Coupon Code';
$_['text_step_shipping']					= 'Estimate Shipping & Taxes';
$_['text_step_payment_method']				= 'Step 5: Payment Method';
$_['text_step_confirm_order']				= 'Step 6: Confirm Order';

// Entry
$_['entry_connect']	 						= 'Connect';
$_['entry_environment']				 		= 'Environment';
$_['entry_debug']				 			= 'Debug Logging';
$_['entry_sale_analytics_range'] 			= 'Sales Analytics Range';
$_['entry_checkout_mode']	 				= 'Checkout Mode';
$_['entry_transaction_method']	 			= 'Settlement Method';
$_['entry_total']		 					= 'Total';
$_['entry_geo_zone']	 					= 'Geo Zone';
$_['entry_status']		 					= 'Status';
$_['entry_sort_order']	 					= 'Sort Order';
$_['entry_country_code']	 				= 'Country';
$_['entry_currency_code']	 				= 'Currency';
$_['entry_currency_value']	 				= 'Currency Value';
$_['entry_card_currency_code']	 			= 'Card Currency';
$_['entry_card_currency_value']	 			= 'Card Currency Value';
$_['entry_button_insert_tag']     			= 'Button Insert Tag';
$_['entry_button_insert_type']     			= 'Button Insert Type';
$_['entry_button_align']     				= 'Button Align';
$_['entry_button_size'] 					= 'Button Size';
$_['entry_button_color'] 					= 'Button Color';
$_['entry_button_shape'] 					= 'Button Shape';
$_['entry_button_label'] 					= 'Button Label';
$_['entry_button_tagline'] 					= 'Button Tagline';
$_['entry_applepay_button_align']     		= 'Button Align';
$_['entry_applepay_button_size'] 			= 'Button Size';
$_['entry_applepay_button_color'] 			= 'Button Color';
$_['entry_applepay_button_shape'] 			= 'Button Shape';
$_['entry_applepay_button_type'] 			= 'Button Type';
$_['entry_card_align']     					= 'Card Align';
$_['entry_card_size'] 						= 'Card Size';
$_['entry_card_secure_status'] 				= 'Card 3D Secure Status';
$_['entry_card_secure_scenario'] 			= 'Card 3D Secure Scenarios';
$_['entry_card_number']						= 'Card Number';
$_['entry_expiration_date']					= 'Expiration Date';
$_['entry_cvv']								= 'CVV';
$_['entry_message_insert_tag']     			= 'Message Insert Tag';
$_['entry_message_insert_type']     		= 'Message Insert Type';
$_['entry_message_align']     				= 'Message Align';
$_['entry_message_size'] 					= 'Message Size';
$_['entry_message_layout'] 					= 'Message Layout';
$_['entry_message_text_color'] 				= 'Message Text Color';
$_['entry_message_text_size'] 				= 'Message Text Size';
$_['entry_message_flex_color'] 				= 'Message Banner Color';
$_['entry_message_flex_ratio'] 				= 'Message Banner Ratio';
$_['entry_contact_company'] 				= 'Company';
$_['entry_contact_first_name'] 				= 'First Name';
$_['entry_contact_last_name'] 				= 'Last Name';
$_['entry_contact_email'] 					= 'E-mail';
$_['entry_contact_url'] 					= 'Website';
$_['entry_contact_sales'] 					= 'Annual Online Sales';
$_['entry_contact_phone'] 					= 'Telephone';
$_['entry_contact_country'] 				= 'Country';
$_['entry_contact_notes'] 					= 'Tell us more about your needs';
$_['entry_contact_merchant'] 				= 'Completed on Behalf of Merchant';
$_['entry_contact_merchant_name'] 			= 'Completed by';
$_['entry_contact_product'] 				= 'Target Product';
$_['entry_contact_send'] 					= 'Submit Form';

// Help
$_['help_status']		 					= 'Enable/Disable extension.';
$_['help_button_status']					= 'When activated PayPal will display personalized Smart Buttons avalible to your customers based on their location.';
$_['help_applepay_button_status']			= 'PayPal verifies if you are eligible for Apple Pay payment and will display this option on the checkout step if available.';
$_['help_card_status']						= 'PayPal verifies if you are eligible for advanced card payment and will display this option on the checkout step if available.';
$_['help_message_status']					= 'Add pay later messaging to your site.';
$_['help_checkout_mode']		 			= 'If your checkout is incompatible with this payment, then we advise you to set the "One Button" mode.';
$_['help_total']		 					= 'The checkout total the order must reach before this payment method becomes active.';
$_['help_country_code']		 				= 'Select the default country for PayPal.';
$_['help_currency_code']		 			= 'Select the default currency for PayPal.';
$_['help_currency_value']		 			= 'Set to 1.00000 if this is your default currency.';
$_['help_card_currency_code']		 		= 'Select the default currency for PayPal Card.';
$_['help_card_currency_value']		 		= 'Set to 1.00000 if this is your default currency.';
$_['help_card_secure_status'] 				= '3D Secure enables you to authenticate card holders through card issuers. It reduces the likelihood of fraud when you use supported cards and improves transaction perfomance. A successful 3D Secure authentication can shift liability for chargebacks due to fraud from you -the merchant- to the card issuer.';
$_['help_card_secure_scenario'] 			= '3D Secure authentication is perfomed only if the card is enrolled for the service. In scenarios where the 3D Secure authentication has not been successful, you have the option to complete the payment at your own risk, meaning that you -the merchant- will be liable in case of a chargeback.';

// Button
$_['button_connect'] 						= 'Connect';
$_['button_disconnect'] 					= 'Disconnect';
$_['button_all_settings']					= 'All Settings';
$_['button_download']						= 'Download';
$_['button_download_host']					= 'Download and host';
$_['button_send']							= 'Submit';
$_['button_cart']							= 'Add to cart';
$_['button_checkout']						= 'Checkout';
$_['button_pay']							= 'Pay with Card';

// Success
$_['success_save']		 					= 'تم اتعديل !';
$_['success_send']		 					= 'Success: Your contact details have been successfully sent to paypal!';
$_['success_download_host']					= 'Success: Association file has been successfully uploaded and hosted!';
$_['success_agree']		 					= 'Success: Deactivation was successful!';

// Error
$_['error_permission']	 					= 'تنبيه: ليس لديك إذن لتعديل طريقة الدفع بواسطة PayPal !';
$_['error_timeout'] 	  					= 'Sorry, PayPal is currently busy. Please try again later!';
$_['error_agree'] 	  						= 'We discovered that a few countries under sanctions are featured on your website. Please click the button to deactivate them (Cuba, Iran, Syria, North Korea, Crimea, Donetsk, and Lugansk regions of Ukraine).<br /><br /><button type="button" class="button-agree btn btn-danger">Comply with PayPal regulations</button>';