<?php
// Heading
$_['heading_title']                    = 'Advanced Audit Trail';

// Text
$_['text_success']                     = 'Success: You have modified advanced audit trail!';
$_['text_list']                        = 'Advanced Audit Trail List';
$_['text_add']                         = 'Add Audit Trail';
$_['text_edit']                        = 'Edit Audit Trail';
$_['text_view']                        = 'View Audit Trail';
$_['text_default']                     = 'Default';
$_['text_enabled']                     = 'Enabled';
$_['text_disabled']                    = 'Disabled';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_none']                        = 'None';
$_['text_select']                      = 'Select';
$_['text_loading']                     = 'Loading...';
$_['text_no_results']                  = 'No results!';
$_['text_confirm']                     = 'Are you sure?';

// Audit Trail specific
$_['text_audit_info']                  = 'Audit Information';
$_['text_audit_name']                  = 'Audit Name';
$_['text_audit_type']                  = 'Audit Type';
$_['text_audit_scope']                 = 'Audit Scope';
$_['text_audit_period']                = 'Audit Period';
$_['text_start_date']                  = 'Start Date';
$_['text_end_date']                    = 'End Date';
$_['text_auditor']                     = 'Auditor';
$_['text_audit_status']                = 'Audit Status';
$_['text_risk_level']                  = 'Risk Level';
$_['text_compliance_status']           = 'Compliance Status';

// Audit Types
$_['text_type_financial']              = 'Financial';
$_['text_type_operational']            = 'Operational';
$_['text_type_compliance']             = 'Compliance';
$_['text_type_it']                     = 'IT';
$_['text_type_internal']               = 'Internal';
$_['text_type_external']               = 'External';

// Audit Status
$_['text_status_planned']              = 'Planned';
$_['text_status_in_progress']          = 'In Progress';
$_['text_status_fieldwork']            = 'Fieldwork';
$_['text_status_reporting']            = 'Reporting';
$_['text_status_completed']            = 'Completed';
$_['text_status_follow_up']            = 'Follow-up';

// Risk Levels
$_['text_risk_low']                    = 'Low';
$_['text_risk_medium']                 = 'Medium';
$_['text_risk_high']                   = 'High';
$_['text_risk_critical']               = 'Critical';

// Audit Activities
$_['text_activity_planning']           = 'Planning';
$_['text_activity_risk_assessment']    = 'Risk Assessment';
$_['text_activity_testing']            = 'Testing';
$_['text_activity_sampling']           = 'Sampling';
$_['text_activity_documentation']      = 'Documentation';
$_['text_activity_review']             = 'Review';
$_['text_activity_reporting']          = 'Reporting';

// Findings and Observations
$_['text_findings']                    = 'Findings';
$_['text_observations']                = 'Observations';
$_['text_recommendations']             = 'Recommendations';
$_['text_management_response']         = 'Management Response';
$_['text_corrective_actions']          = 'Corrective Actions';
$_['text_implementation_date']         = 'Implementation Date';

// Finding Types
$_['text_finding_deficiency']          = 'Deficiency';
$_['text_finding_weakness']            = 'Weakness';
$_['text_finding_non_compliance']      = 'Non-compliance';
$_['text_finding_inefficiency']        = 'Inefficiency';
$_['text_finding_opportunity']         = 'Improvement Opportunity';

// Workflow Integration
$_['text_workflow_integration']        = 'Workflow Integration';
$_['text_workflow_status']             = 'Workflow Status';
$_['text_approval_workflow']           = 'Approval Workflow';
$_['text_review_workflow']             = 'Review Workflow';
$_['text_follow_up_workflow']          = 'Follow-up Workflow';

// AI Features
$_['text_ai_analysis']                 = 'AI Analysis';
$_['text_risk_prediction']             = 'Risk Prediction';
$_['text_anomaly_detection']           = 'Anomaly Detection';
$_['text_pattern_analysis']            = 'Pattern Analysis';
$_['text_automated_testing']           = 'Automated Testing';

// Documentation
$_['text_audit_documentation']         = 'Audit Documentation';
$_['text_working_papers']              = 'Working Papers';
$_['text_evidence']                    = 'Evidence';
$_['text_supporting_documents']        = 'Supporting Documents';
$_['text_audit_program']               = 'Audit Program';

// Statistics
$_['text_total_audits']                = 'Total Audits';
$_['text_active_audits']               = 'Active Audits';
$_['text_completed_audits']            = 'Completed Audits';
$_['text_overdue_audits']              = 'Overdue Audits';
$_['text_high_risk_findings']          = 'High Risk Findings';
$_['text_compliance_rate']             = 'Compliance Rate';

// Buttons
$_['button_add']                       = 'Add';
$_['button_edit']                      = 'Edit';
$_['button_delete']                    = 'Delete';
$_['button_save']                      = 'Save';
$_['button_cancel']                    = 'Cancel';
$_['button_close']                     = 'Close';
$_['button_back']                      = 'Back';
$_['button_view']                      = 'View';
$_['button_print']                     = 'Print';
$_['button_export']                    = 'Export';
$_['button_start_audit']               = 'Start Audit';
$_['button_complete_audit']            = 'Complete Audit';
$_['button_add_finding']               = 'Add Finding';
$_['button_generate_report']           = 'Generate Report';
$_['button_ai_analyze']                = 'AI Analyze';

// Columns
$_['column_audit_name']                = 'Audit Name';
$_['column_audit_type']                = 'Type';
$_['column_auditor']                   = 'Auditor';
$_['column_start_date']                = 'Start Date';
$_['column_end_date']                  = 'End Date';
$_['column_status']                    = 'Status';
$_['column_risk_level']                = 'Risk Level';
$_['column_findings_count']            = 'Findings Count';
$_['column_compliance_status']         = 'Compliance Status';
$_['column_created_by']                = 'Created By';
$_['column_date_created']              = 'Date Created';
$_['column_action']                    = 'Action';

// Finding columns
$_['column_finding_title']             = 'Finding Title';
$_['column_finding_type']              = 'Type';
$_['column_severity']                  = 'Severity';
$_['column_department']                = 'Department';
$_['column_due_date']                  = 'Due Date';
$_['column_responsible_person']        = 'Responsible Person';

// Entry fields
$_['entry_audit_name']                 = 'Audit Name';
$_['entry_audit_type']                 = 'Audit Type';
$_['entry_audit_scope']                = 'Audit Scope';
$_['entry_start_date']                 = 'Start Date';
$_['entry_end_date']                   = 'End Date';
$_['entry_auditor']                    = 'Auditor';
$_['entry_risk_level']                 = 'Risk Level';
$_['entry_description']                = 'Description';
$_['entry_objectives']                 = 'Objectives';
$_['entry_methodology']                = 'Methodology';
$_['entry_finding_title']              = 'Finding Title';
$_['entry_finding_description']        = 'Finding Description';
$_['entry_recommendation']             = 'Recommendation';
$_['entry_management_response']        = 'Management Response';

// Help text
$_['help_audit_name']                  = 'Enter a descriptive name for the audit';
$_['help_audit_scope']                 = 'Define the scope and coverage of the audit';
$_['help_risk_assessment']             = 'Assess the risk level associated';
$_['help_ai_analysis']                 = 'Use AI for deeper analysis';

// Error messages
$_['error_permission']                 = 'Warning: You do not have permission to access advanced audit trail!';
$_['error_audit_name']                 = 'Audit name must be between 3 and 64 characters!';
$_['error_audit_type']                 = 'Please select audit type!';
$_['error_start_date']                 = 'Please enter start date!';
$_['error_end_date']                   = 'Please enter end date!';
$_['error_date_range']                 = 'End date must be after start date!';
$_['error_auditor']                    = 'Please select auditor!';
$_['error_audit_scope']                = 'Please define audit scope!';
$_['error_finding_title']              = 'Finding title must be between 3 and 128 characters!';
$_['error_finding_description']        = 'Please enter finding description!';
$_['error_recommendation']             = 'Please enter recommendation!';
$_['error_audit_in_progress']          = 'Cannot modify audit in progress!';

// Success messages
$_['success_audit_added']              = 'Audit added successfully!';
$_['success_audit_updated']            = 'Audit updated successfully!';
$_['success_audit_deleted']            = 'Audit deleted successfully!';
$_['success_audit_started']            = 'Audit started successfully!';
$_['success_audit_completed']          = 'Audit completed successfully!';
$_['success_finding_added']            = 'Finding added successfully!';
$_['success_report_generated']         = 'Report generated successfully!';
$_['success_ai_analysis_completed']    = 'AI analysis completed successfully!';

// Confirmation messages
$_['confirm_delete_audit']             = 'Are you sure you want to delete this audit?';
$_['confirm_start_audit']              = 'Are you sure you want to start the audit?';
$_['confirm_complete_audit']           = 'Are you sure you want to complete the audit?';
$_['confirm_delete_finding']           = 'Are you sure you want to delete this finding?';

// Tabs
$_['tab_general']                      = 'General';
$_['tab_scope']                        = 'Scope';
$_['tab_planning']                     = 'Planning';
$_['tab_execution']                    = 'Execution';
$_['tab_findings']                     = 'Findings';
$_['tab_recommendations']              = 'Recommendations';
$_['tab_follow_up']                    = 'Follow-up';
$_['tab_documentation']                = 'Documentation';
$_['tab_workflow']                     = 'Workflow';

// Reports
$_['text_audit_report']                = 'Audit Report';
$_['text_findings_report']             = 'Findings Report';
$_['text_compliance_report']           = 'Compliance Report';
$_['text_risk_assessment_report']      = 'Risk Assessment Report';
$_['text_management_letter']           = 'Management Letter';

// Filters
$_['text_filter_audit_type']           = 'Filter by Audit Type';
$_['text_filter_status']               = 'Filter by Status';
$_['text_filter_risk_level']           = 'Filter by Risk Level';
$_['text_filter_auditor']              = 'Filter by Auditor';
$_['text_filter_date_range']           = 'Filter by Date Range';

// Processing status
$_['text_processing']                  = 'Processing...';
$_['text_analyzing']                   = 'Analyzing...';
$_['text_generating_report']           = 'Generating Report...';
$_['text_ai_processing']               = 'AI Processing...';
$_['text_completed']                   = 'Completed';
$_['text_failed']                      = 'Failed';

// Additional features
$_['text_continuous_monitoring']       = 'Continuous Monitoring';
$_['text_automated_controls']          = 'Automated Controls';
$_['text_exception_reporting']         = 'Exception Reporting';
$_['text_data_analytics']              = 'Data Analytics';
$_['text_benchmarking']                = 'Benchmarking';
$_['text_quality_assurance']           = 'Quality Assurance';

// Compliance Standards
$_['text_sox_compliance']              = 'SOX Compliance';
$_['text_ifrs_compliance']             = 'IFRS Compliance';
$_['text_local_gaap']                  = 'Local GAAP';
$_['text_regulatory_compliance']       = 'Regulatory Compliance';
$_['text_internal_controls']           = 'Internal Controls';

// Notifications
$_['text_audit_notifications']         = 'Audit Notifications';
$_['text_deadline_reminders']          = 'Deadline Reminders';
$_['text_escalation_alerts']           = 'Escalation Alerts';
$_['text_status_updates']              = 'Status Updates';
?>
