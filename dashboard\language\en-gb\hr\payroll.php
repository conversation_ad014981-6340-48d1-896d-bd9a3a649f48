<?php
// Heading
$_['heading_title']          = 'Payroll Periods Management';

// Text
$_['text_filter']            = 'Filter';
$_['text_enter_period_name'] = 'Enter period name';
$_['text_all_statuses']      = 'All Statuses';
$_['text_status_open']       = 'Open';
$_['text_status_closed']     = 'Closed';
$_['button_filter']          = 'Filter';
$_['button_reset']           = 'Reset';
$_['button_add_period']      = 'Add Payroll Period';
$_['button_close']           = 'Close';
$_['button_save']            = 'Save';
$_['text_payroll_list']      = 'Payroll Periods List';
$_['text_add_period']        = 'Add Payroll Period';
$_['text_edit_period']       = 'Edit Payroll Period';
$_['text_ajax_error']        = 'An error occurred while communicating with the server';
$_['text_confirm_delete']    = 'Are you sure you want to delete?';
$_['text_view_entries']      = 'View Entries';
$_['text_view_entries_for_period'] = 'View payroll entries for: %s';

// Columns
$_['column_period_name']     = 'Period Name';
$_['column_start_date']      = 'Start Date';
$_['column_end_date']        = 'End Date';
$_['column_status']          = 'Status';
$_['column_actions']         = 'Actions';

$_['text_start_date']        = 'Start Date';
$_['text_end_date']          = 'End Date';

// Entries modal
$_['column_employee']        = 'Employee';
$_['column_base_salary']     = 'Base Salary';
$_['column_allowances']      = 'Allowances';
$_['column_deductions']      = 'Deductions';
$_['column_net_salary']      = 'Net Salary';
$_['column_payment_status']  = 'Payment Status';

$_['button_mark_paid']       = 'Mark as Paid';

// Success / Errors
$_['error_not_found']        = 'Record not found!';
$_['error_invalid_request']  = 'Invalid request!';
$_['error_permission']       = 'Warning: You do not have permission to modify payroll periods!';
$_['error_required']         = 'Warning: Please fill in the required fields!';
$_['text_success_add']       = 'Payroll period added successfully!';
$_['text_success_edit']      = 'Payroll period updated successfully!';
$_['text_success_delete']    = 'Payroll period deleted successfully!';
$_['text_success_mark_paid'] = 'Payment status updated successfully!';
