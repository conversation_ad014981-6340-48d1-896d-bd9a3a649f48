{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="inventory\product_management-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="inventory\product_management-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bulk_update">{{ text_bulk_update }}</label>
            <div class="col-sm-10">
              <input type="text" name="bulk_update" value="{{ bulk_update }}" placeholder="{{ text_bulk_update }}" id="input-bulk_update" class="form-control" />
              {% if error_bulk_update %}
                <div class="invalid-feedback">{{ error_bulk_update }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-calculate_pricing">{{ text_calculate_pricing }}</label>
            <div class="col-sm-10">
              <input type="text" name="calculate_pricing" value="{{ calculate_pricing }}" placeholder="{{ text_calculate_pricing }}" id="input-calculate_pricing" class="form-control" />
              {% if error_calculate_pricing %}
                <div class="invalid-feedback">{{ error_calculate_pricing }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-copy">{{ text_copy }}</label>
            <div class="col-sm-10">
              <input type="text" name="copy" value="{{ copy }}" placeholder="{{ text_copy }}" id="input-copy" class="form-control" />
              {% if error_copy %}
                <div class="invalid-feedback">{{ error_copy }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-edit">{{ text_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="edit" value="{{ edit }}" placeholder="{{ text_edit }}" id="input-edit" class="form-control" />
              {% if error_edit %}
                <div class="invalid-feedback">{{ error_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_excel">{{ text_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_excel" value="{{ export_excel }}" placeholder="{{ text_export_excel }}" id="input-export_excel" class="form-control" />
              {% if error_export_excel %}
                <div class="invalid-feedback">{{ error_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-generate_sku">{{ text_generate_sku }}</label>
            <div class="col-sm-10">
              <input type="text" name="generate_sku" value="{{ generate_sku }}" placeholder="{{ text_generate_sku }}" id="input-generate_sku" class="form-control" />
              {% if error_generate_sku %}
                <div class="invalid-feedback">{{ error_generate_sku }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-import">{{ text_import }}</label>
            <div class="col-sm-10">
              <input type="text" name="import" value="{{ import }}" placeholder="{{ text_import }}" id="input-import" class="form-control" />
              {% if error_import %}
                <div class="invalid-feedback">{{ error_import }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-length_classes">{{ text_length_classes }}</label>
            <div class="col-sm-10">
              <input type="text" name="length_classes" value="{{ length_classes }}" placeholder="{{ text_length_classes }}" id="input-length_classes" class="form-control" />
              {% if error_length_classes %}
                <div class="invalid-feedback">{{ error_length_classes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-low_stock_products">{{ text_low_stock_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="low_stock_products" value="{{ low_stock_products }}" placeholder="{{ text_low_stock_products }}" id="input-low_stock_products" class="form-control" />
              {% if error_low_stock_products %}
                <div class="invalid-feedback">{{ error_low_stock_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manufacturers">{{ text_manufacturers }}</label>
            <div class="col-sm-10">
              <input type="text" name="manufacturers" value="{{ manufacturers }}" placeholder="{{ text_manufacturers }}" id="input-manufacturers" class="form-control" />
              {% if error_manufacturers %}
                <div class="invalid-feedback">{{ error_manufacturers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-overstock_products">{{ text_overstock_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="overstock_products" value="{{ overstock_products }}" placeholder="{{ text_overstock_products }}" id="input-overstock_products" class="form-control" />
              {% if error_overstock_products %}
                <div class="invalid-feedback">{{ error_overstock_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_description">{{ text_product_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_description" value="{{ product_description }}" placeholder="{{ text_product_description }}" id="input-product_description" class="form-control" />
              {% if error_product_description %}
                <div class="invalid-feedback">{{ error_product_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_info">{{ text_product_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_info" value="{{ product_info }}" placeholder="{{ text_product_info }}" id="input-product_info" class="form-control" />
              {% if error_product_info %}
                <div class="invalid-feedback">{{ error_product_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-products">{{ text_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="products" value="{{ products }}" placeholder="{{ text_products }}" id="input-products" class="form-control" />
              {% if error_products %}
                <div class="invalid-feedback">{{ error_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-refresh">{{ text_refresh }}</label>
            <div class="col-sm-10">
              <input type="text" name="refresh" value="{{ refresh }}" placeholder="{{ text_refresh }}" id="input-refresh" class="form-control" />
              {% if error_refresh %}
                <div class="invalid-feedback">{{ error_refresh }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected_products">{{ text_selected_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected_products" value="{{ selected_products }}" placeholder="{{ text_selected_products }}" id="input-selected_products" class="form-control" />
              {% if error_selected_products %}
                <div class="invalid-feedback">{{ error_selected_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status_options">{{ text_status_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="status_options" value="{{ status_options }}" placeholder="{{ text_status_options }}" id="input-status_options" class="form-control" />
              {% if error_status_options %}
                <div class="invalid-feedback">{{ error_status_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_status_options">{{ text_stock_status_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_status_options" value="{{ stock_status_options }}" placeholder="{{ text_stock_status_options }}" id="input-stock_status_options" class="form-control" />
              {% if error_stock_status_options %}
                <div class="invalid-feedback">{{ error_stock_status_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_statuses">{{ text_stock_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_statuses" value="{{ stock_statuses }}" placeholder="{{ text_stock_statuses }}" id="input-stock_statuses" class="form-control" />
              {% if error_stock_statuses %}
                <div class="invalid-feedback">{{ error_stock_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tax_classes">{{ text_tax_classes }}</label>
            <div class="col-sm-10">
              <input type="text" name="tax_classes" value="{{ tax_classes }}" placeholder="{{ text_tax_classes }}" id="input-tax_classes" class="form-control" />
              {% if error_tax_classes %}
                <div class="invalid-feedback">{{ error_tax_classes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-top_selling_products">{{ text_top_selling_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="top_selling_products" value="{{ top_selling_products }}" placeholder="{{ text_top_selling_products }}" id="input-top_selling_products" class="form-control" />
              {% if error_top_selling_products %}
                <div class="invalid-feedback">{{ error_top_selling_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-units">{{ text_units }}</label>
            <div class="col-sm-10">
              <input type="text" name="units" value="{{ units }}" placeholder="{{ text_units }}" id="input-units" class="form-control" />
              {% if error_units %}
                <div class="invalid-feedback">{{ error_units }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-weight_classes">{{ text_weight_classes }}</label>
            <div class="col-sm-10">
              <input type="text" name="weight_classes" value="{{ weight_classes }}" placeholder="{{ text_weight_classes }}" id="input-weight_classes" class="form-control" />
              {% if error_weight_classes %}
                <div class="invalid-feedback">{{ error_weight_classes }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}