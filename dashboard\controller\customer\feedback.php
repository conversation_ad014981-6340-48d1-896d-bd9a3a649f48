<?php
class ControllerCustomerFeedback extends Controller {
    public function index() {
        $this->load->language('customer/feedback');
        $this->document->setTitle($this->language->get('heading_title'));
        
        $this->load->model('customer/customer');
        
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_customer'),
            'href' => $this->url->link('customer/customer', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('customer/feedback', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['add'] = $this->url->link('customer/feedback/add', 'user_token=' . $this->session->data['user_token'], true);
        $data['user_token'] = $this->session->data['user_token'];
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('customer/feedback', $data));
    }
    
    public function getList() {
        $this->load->language('customer/feedback');
        $this->load->model('customer/customer');
        
        $data['feedbacks'] = array();
        
        $results = $this->model_customer_customer->getCustomerFeedbacks();
        
        foreach ($results as $result) {
            $data['feedbacks'][] = array(
                'feedback_id' => $result['feedback_id'],
                'customer_name' => $result['firstname'] . ' ' . $result['lastname'],
                'customer_email' => $result['email'],
                'subject' => $result['subject'],
                'feedback_type' => $result['feedback_type'],
                'status' => $result['status'],
                'priority' => $result['priority'],
                'created_at' => date($this->language->get('date_format_short'), strtotime($result['created_at'])),
                'edit' => $this->url->link('customer/feedback/edit', 'user_token=' . $this->session->data['user_token'] . '&feedback_id=' . $result['feedback_id'], true)
            );
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }
    
    public function add() {
        $this->load->language('customer/feedback');
        $this->load->model('customer/customer');
        
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_customer_customer->addCustomerFeedback($this->request->post);
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('customer/feedback', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getForm();
    }
    
    public function edit() {
        $this->load->language('customer/feedback');
        $this->load->model('customer/customer');
        
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_customer_customer->updateCustomerFeedback($this->request->get['feedback_id'], $this->request->post);
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('customer/feedback', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getForm();
    }
    
    public function delete() {
        $this->load->language('customer/feedback');
        $this->load->model('customer/customer');
        
        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $feedback_id) {
                $this->model_customer_customer->deleteCustomerFeedback($feedback_id);
            }
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('customer/feedback', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getList();
    }
    
    protected function getForm() {
        $this->load->language('customer/feedback');
        $this->load->model('customer/customer');
        
        $data['text_form'] = !isset($this->request->get['feedback_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
        
        if (isset($this->request->get['feedback_id'])) {
            $feedback_info = $this->model_customer_customer->getCustomerFeedback($this->request->get['feedback_id']);
        }
        
        if (isset($this->request->post['customer_id'])) {
            $data['customer_id'] = $this->request->post['customer_id'];
        } elseif (!empty($feedback_info)) {
            $data['customer_id'] = $feedback_info['customer_id'];
        } else {
            $data['customer_id'] = '';
        }
        
        if (isset($this->request->post['subject'])) {
            $data['subject'] = $this->request->post['subject'];
        } elseif (!empty($feedback_info)) {
            $data['subject'] = $feedback_info['subject'];
        } else {
            $data['subject'] = '';
        }
        
        if (isset($this->request->post['description'])) {
            $data['description'] = $this->request->post['description'];
        } elseif (!empty($feedback_info)) {
            $data['description'] = $feedback_info['description'];
        } else {
            $data['description'] = '';
        }
        
        if (isset($this->request->post['feedback_type'])) {
            $data['feedback_type'] = $this->request->post['feedback_type'];
        } elseif (!empty($feedback_info)) {
            $data['feedback_type'] = $feedback_info['feedback_type'];
        } else {
            $data['feedback_type'] = 'inquiry';
        }
        
        if (isset($this->request->post['priority'])) {
            $data['priority'] = $this->request->post['priority'];
        } elseif (!empty($feedback_info)) {
            $data['priority'] = $feedback_info['priority'];
        } else {
            $data['priority'] = 'medium';
        }
        
        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($feedback_info)) {
            $data['status'] = $feedback_info['status'];
        } else {
            $data['status'] = 'new';
        }
        
        $data['customers'] = $this->model_customer_customer->getCustomers();
        
        $data['user_token'] = $this->session->data['user_token'];
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('customer/feedback_form', $data));
    }
    
    protected function validate() {
        if (!$this->user->hasPermission('modify', 'customer/feedback')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        
        if (empty($this->request->post['customer_id'])) {
            $this->error['customer'] = $this->language->get('error_customer');
        }
        
        if (empty($this->request->post['subject'])) {
            $this->error['subject'] = $this->language->get('error_subject');
        }
        
        if (empty($this->request->post['description'])) {
            $this->error['description'] = $this->language->get('error_description');
        }
        
        return !$this->error;
    }
    
    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'customer/feedback')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        
        return !$this->error;
    }
} 