{{ header }}{{ column_left }}
<div id="content" class="payment-paypal">
	<div class="page-header">
		<div class="container-fluid">
			<div class="pull-right">
				<button data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary button-save"><i class="fa fa-save"></i></button>
				<a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
			</div>
			<h1>{{ heading_title_main }}</h1>
			<ul class="breadcrumb">
				{% for breadcrumb in breadcrumbs %}
				<li><a href="{{ breadcrumb['href'] }}">{{ breadcrumb['text'] }}</a></li>
				{% endfor %}
			</ul>
		</div>
	</div>
	<div class="container-fluid">
		{% if error_warning %}
		<div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}</div>
		{% endif %}
		{% if text_version %}
		<div class="alert alert-info"><i class="fa fa-info-circle"></i> {{ text_version }}</div>
		{% endif %}
		<div class="panel panel-default">
			<div class="panel-heading">
				<h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
			</div>
			<div class="panel-body">
				<form action="{{ action }}" method="post" enctype="multipart/form-data" id="form_payment">
					<a href="{{ href_dashboard }}" class="back-dashboard"><i class="icon icon-back-dashboard"></i>{{ text_tab_dashboard }}</a>
					<ul class="nav nav-tabs">
						<li class="nav-tab"><a href="{{ href_general }}" class="tab"><i class="tab-icon tab-icon-general"></i><span class="tab-title">{{ text_tab_general }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_button }}" class="tab"><i class="tab-icon tab-icon-button"></i><span class="tab-title">{{ text_tab_button }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_googlepay_button }}" class="tab"><i class="tab-icon tab-icon-googlepay-button"></i><span class="tab-title">{{ text_tab_googlepay_button }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_applepay_button }}" class="tab"><i class="tab-icon tab-icon-applepay-button"></i><span class="tab-title">{{ text_tab_applepay_button }}</span></a></li>
						<li class="nav-tab active"><a href="{{ href_card }}" class="tab"><i class="tab-icon tab-icon-card"></i><span class="tab-title">{{ text_tab_card }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_message_configurator }}" class="tab"><i class="tab-icon tab-icon-message-configurator"></i><span class="tab-title">{{ text_tab_message_configurator }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_message_setting }}" class="tab"><i class="tab-icon tab-icon-message-setting"></i><span class="tab-title">{{ text_tab_message_setting }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_order_status }}" class="tab"><i class="tab-icon tab-icon-order-status"></i><span class="tab-title">{{ text_tab_order_status }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_contact }}" class="tab"><i class="tab-icon tab-icon-contact"></i><span class="tab-title">{{ text_tab_contact }}</span></a></li>
					</ul>
					<div class="section-content">
						<div class="row">
							<div class="col col-lg-6">
								<div class="section-checkout">
									<div class="section-title">{{ text_checkout }}</div>
									<div class="section-panel">
										<div class="section-panel-title">{{ text_step_payment_method }}<i class="icon icon-section-panel"></i></div>
									</div>
									<div class="section-panel">
										<div class="section-panel-title">{{ text_step_confirm_order }}<i class="icon icon-section-panel"></i></div>
									</div>
									<div class="table-totals">
										<div class="row">
											<div class="col col-md-offset-6 col-md-6">
												<div class="row row-total">
													<div class="col col-xs-6 col-title">{{ text_cart_sub_total }}</div>
													<div class="col col-xs-6 col-price">{{ text_cart_product_total_value }}</div>
												</div>
												<div class="row row-total">
													<div class="col col-xs-6 col-title">{{ text_cart_total }}</div>
													<div class="col col-xs-6 col-price">{{ text_cart_product_total_value }}</div>
												</div>
											</div>
										</div>
									</div>
									<div id="paypal_card" class="paypal-card">
										<div id="paypal_card_container" class="paypal-card-container paypal-spinner">
											<div id="paypal_card_form" class="paypal-card-form well">
												<div class="card-info-holder-name clearfix">
													<div id="card_holder_name" class="card-input-container"></div>
												</div>
												<div class="card-info-number clearfix">
													<div id="card_number" class="card-input-container"></div>
												</div>
												<div class="card-info-date-cvv clearfix">
													<div class="card-info-date">
														<div id="expiration_date" class="card-input-container"></div>
													</div>
													<div class="card-info-cvv">
														<div id="cvv" class="card-input-container"></div>
													</div>
												</div>
												<div class="card-button">
													<button type="button" id="paypal_card_button" class="btn paypal-card-button">{{ button_pay }}</button>
												</div>
											</div>
										</div>
									</div>
								</div>	
							</div>
							<div class="col col-lg-6">
								<div class="section-card-setting">
									<div class="row">
										<div class="col col-md-6">
											<legend class="legend">{{ text_card_settings }}</legend>
										</div>
										<div class="col col-md-6">
											<div class="form-group-status">
												<label class="control-label" for="input_card_status"><span data-toggle="tooltip" title="{{ help_card_status }}">{{ entry_status }}</span></label>
												<input type="hidden" name="payment_paypal_setting[card][status]" value="0" />
												<input type="checkbox" name="payment_paypal_setting[card][status]" value="1" class="switch" {% if setting['card']['status'] %}checked="checked"{% endif %} />
											</div>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label" for="input_card_align">{{ entry_card_align }}</label>
										<select name="payment_paypal_setting[card][align]" id="input_card_align" class="form-control control-paypal-card">
											{% for card_align in setting['card_align'] %}
											{% if (card_align['code'] == setting['card']['align']) %}
											<option value="{{ card_align['code'] }}" selected="selected">{{ attribute(_context, card_align['name']) }}</option>
											{% else %}
											<option value="{{ card_align['code'] }}">{{ attribute(_context, card_align['name']) }}</option>
											{% endif %}
											{% endfor %}
										</select>
									</div>
									<div class="form-group">
										<label class="control-label" for="input_card_size">{{ entry_card_size }}</label>
										<select name="payment_paypal_setting[card][size]" id="input_card_size" class="form-control control-paypal-card">
											{% for card_size in setting['card_size'] %}
											{% if (card_size['code'] == setting['card']['size']) %}
											<option value="{{ card_size['code'] }}" selected="selected">{{ attribute(_context, card_size['name']) }}</option>
											{% else %}
											<option value="{{ card_size['code'] }}">{{ attribute(_context, card_size['name']) }}</option>
											{% endif %}
											{% endfor %}
										</select>
									</div>
									<div class="form-group">
										<label class="control-label" for="input_card_secure_method"><span data-toggle="tooltip" title="{{ help_card_secure_method }}">{{ entry_card_secure_method }}</span></label>
										<select name="payment_paypal_setting[card][secure_method]" id="input_card_secure_method" class="form-control">
											{% for card_secure_method in setting['card_secure_method'] %}
											{% if (card_secure_method['code'] == setting['card']['secure_method']) %}
											<option value="{{ card_secure_method['code'] }}" selected="selected">{{ attribute(_context, card_secure_method['name']) }}</option>
											{% else %}
											<option value="{{ card_secure_method['code'] }}">{{ attribute(_context, card_secure_method['name']) }}</option>
											{% endif %}
											{% endfor %}
										</select>
									</div>
									<hr class="hr" />
									<button type="button" href="#all_settings" class="btn btn-default button-all-settings collapsed" data-toggle="collapse" role="button">{{ button_all_settings }}<i class="icon icon-all-settings"></i></button>	
									<div id="all_settings" class="all-settings collapse">
										<div class="form-group">
											<label class="control-label" for="input_card_secure_scenario">{{ entry_card_secure_scenario }}</label>
											<p class="alert alert-info">{{ help_card_secure_scenario }}</p>
										</div>
										{% for card_secure_scenario in setting['card_secure_scenario'] %}
										<div class="form-group">
											<label class="control-label" for="input_card_secure_scenario_{{ card_secure_scenario['code'] }}">{{ attribute(_context, card_secure_scenario['name']) }}</label>	
											<select name="payment_paypal_setting[card][secure_scenario][{{ card_secure_scenario['code'] }}]" id="input_card_secure_scenario_{{ card_secure_scenario['code'] }}" class="form-control">
												<option value="1" {% if setting['card']['secure_scenario'][card_secure_scenario['code']] %}selected="selected"{% endif %}>{{ text_accept }}{% if card_secure_scenario['recommended'] %} {{ text_recommended }}{% endif %}</option>
												<option value="0" {% if not setting['card']['secure_scenario'][card_secure_scenario['code']] %}selected="selected"{% endif %}>{{ text_decline }}{% if not card_secure_scenario['recommended'] %} {{ text_recommended }}{% endif %}</option>
											</select>
										</div>
										{% endfor %}
									</div>
								</div>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">

var card_width = JSON.parse('{{ setting['card_width']|json_encode() }}');

updatePayPalCard();

$('.payment-paypal .switch').bootstrapSwitch({
    'onColor': 'success',
    'onText': '{{ text_on }}',
    'offText': '{{ text_off }}'
});

$('.payment-paypal').on('change', '.control-paypal-card', function() {
	updatePayPalCard();
});

$('.payment-paypal').on('click', '.button-save', function() {
    $.ajax({
		type: 'post',
		url: $('#form_payment').attr('action'),
		data: $('#form_payment').serialize(),
		dataType: 'json',
		success: function(json) {
			$('.payment-paypal .alert-success').remove();
			
			if (json['success']) {
				$('.payment-paypal > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> ' + json['success'] + '</div>');
				
				$('html, body').animate({scrollTop: $('.payment-paypal > .container-fluid .alert-success').offset().top}, 'slow');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
    });  
});

$('.payment-paypal').on('click', '.button-agree', function() {
	$.ajax({
		type: 'post',
		url: '{{ agree_url }}',
		data: '',
		dataType: 'json',
		success: function(json) {
			$('.payment-paypal .alert').remove();
			
			if (json['success']) {
				$('.payment-paypal > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> ' + json['success'] + '</div>');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});

function updatePayPalCard() {								
	var paypal_data = {};

	paypal_data['client_id'] = '{{ client_id }}';
	paypal_data['secret'] = '{{ secret }}';
	paypal_data['merchant_id'] = '{{ merchant_id }}';
	paypal_data['environment'] = '{{ environment }}';
	paypal_data['partner_attribution_id'] = '{{ partner_attribution_id }}';
	paypal_data['locale'] = '{{ locale }}';
	paypal_data['currency_code'] = '{{ currency_code }}';
	paypal_data['currency_value'] = '{{ currency_value }}';
	paypal_data['decimal_place'] = '{{ decimal_place }}';
	paypal_data['client_token'] = '{{ client_token }}';
	paypal_data['transaction_method'] = '{{ setting['general']['transaction_method'] }}';
	paypal_data['components'] = ['card-fields'];
	paypal_data['card_align'] = $('.payment-paypal #input_card_align').val();
	paypal_data['card_size'] = $('.payment-paypal #input_card_size').val();
		
	paypal_data['card_width'] = card_width[paypal_data['card_size']];
			
	PayPalAPI.init(paypal_data);
}

</script>
{{ footer }}