{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" id="button-save" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary">
          <i class="fa-solid fa-save"></i>
        </button>
        <button type="button" id="button-approve" data-bs-toggle="tooltip" title="{{ button_approve }}" class="btn btn-success"{% if voucher_id and status == 'approved' %} disabled{% endif %}>
          <i class="fa-solid fa-check"></i>
        </button>
        <button type="button" id="button-post" data-bs-toggle="tooltip" title="{{ button_post }}" class="btn btn-warning"{% if not voucher_id or status != 'approved' or is_posted %} disabled{% endif %}>
          <i class="fa-solid fa-paper-plane"></i>
        </button>
        <button type="button" id="button-print" data-bs-toggle="tooltip" title="{{ button_print }}" class="btn btn-info"{% if not voucher_id %} disabled{% endif %}>
          <i class="fa-solid fa-print"></i>
        </button>
        <div class="btn-group">
          <button type="button" class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fa-solid fa-cog"></i> {{ text_actions }}
          </button>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" id="btn-duplicate"{% if not voucher_id %} disabled{% endif %}><i class="fa-solid fa-copy"></i> {{ button_duplicate }}</a></li>
            <li><a class="dropdown-item" href="#" id="btn-reverse"{% if not voucher_id or not is_posted %} disabled{% endif %}><i class="fa-solid fa-undo"></i> {{ button_reverse }}</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#" id="btn-export"><i class="fa-solid fa-download"></i> {{ button_export }}</a></li>
            <li><a class="dropdown-item" href="#" id="btn-search"><i class="fa-solid fa-search"></i> {{ button_search }}</a></li>
          </ul>
        </div>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-secondary">
          <i class="fa-solid fa-reply"></i>
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
      <div class="alert alert-danger alert-dismissible"><i class="fa-solid fa-exclamation-circle"></i> {{ error_warning }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    {% if success %}
      <div class="alert alert-success alert-dismissible"><i class="fa-solid fa-check-circle"></i> {{ success }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    <div class="card">
      <div class="card-header">
        <i class="fa-solid fa-receipt"></i> {{ text_form }}
        {% if voucher_id %}
          <span class="badge bg-{{ status == 'draft' ? 'secondary' : (status == 'approved' ? 'success' : 'primary') }} ms-2">
            {{ status_text }}
          </span>
        {% endif %}
      </div>
      <div class="card-body">
        <form id="form-voucher">
          <input type="hidden" name="voucher_id" value="{{ voucher_id }}">

          <!-- معلومات السند الأساسية -->
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="row mb-3">
                <label for="input-voucher-number" class="col-sm-3 col-form-label required">{{ entry_voucher_number }}</label>
                <div class="col-sm-9">
                  <input type="text" name="voucher_number" value="{{ voucher_number }}" placeholder="{{ entry_voucher_number }}" id="input-voucher-number" class="form-control" readonly/>
                </div>
              </div>
              <div class="row mb-3">
                <label for="input-voucher-date" class="col-sm-3 col-form-label required">{{ entry_voucher_date }}</label>
                <div class="col-sm-9">
                  <input type="date" name="voucher_date" value="{{ voucher_date }}" id="input-voucher-date" class="form-control"/>
                  {% if error_voucher_date %}
                    <div class="text-danger">{{ error_voucher_date }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="row mb-3">
                <label for="input-customer" class="col-sm-3 col-form-label required">{{ entry_customer }}</label>
                <div class="col-sm-9">
                  <select name="customer_id" id="input-customer" class="form-select">
                    <option value="">{{ text_select }}</option>
                    {% for customer in customers %}
                      <option value="{{ customer.customer_id }}"{% if customer.customer_id == customer_id %} selected{% endif %}>{{ customer.name }}</option>
                    {% endfor %}
                  </select>
                  {% if error_customer %}
                    <div class="text-danger">{{ error_customer }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="row mb-3">
                <label for="input-amount" class="col-sm-3 col-form-label required">{{ entry_amount }}</label>
                <div class="col-sm-9">
                  <input type="number" step="0.01" name="amount" value="{{ amount }}" placeholder="{{ entry_amount }}" id="input-amount" class="form-control"/>
                  {% if error_amount %}
                    <div class="text-danger">{{ error_amount }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="row mb-3">
                <label for="input-currency" class="col-sm-3 col-form-label">{{ entry_currency }}</label>
                <div class="col-sm-9">
                  <select name="currency_id" id="input-currency" class="form-select">
                    {% for currency in currencies %}
                      <option value="{{ currency.currency_id }}"{% if currency.currency_id == currency_id %} selected{% endif %}>{{ currency.title }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="row mb-3">
                <label for="input-reference" class="col-sm-3 col-form-label">{{ entry_reference }}</label>
                <div class="col-sm-9">
                  <input type="text" name="reference_number" value="{{ reference_number }}" placeholder="{{ entry_reference }}" id="input-reference" class="form-control"/>
                </div>
              </div>
            </div>
          </div>

          <!-- طريقة الدفع -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">{{ text_payment_method }}</h5>
            </div>
            <div class="card-body">
              <div class="row mb-3">
                <label class="col-sm-2 col-form-label">{{ entry_payment_method }}</label>
                <div class="col-sm-10">
                  <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="payment_method" id="payment-cash" value="cash"{% if payment_method == 'cash' %} checked{% endif %}>
                    <label class="form-check-label" for="payment-cash">{{ text_cash }}</label>
                  </div>
                  <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="payment_method" id="payment-bank" value="bank"{% if payment_method == 'bank' %} checked{% endif %}>
                    <label class="form-check-label" for="payment-bank">{{ text_bank }}</label>
                  </div>
                  <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="payment_method" id="payment-check" value="check"{% if payment_method == 'check' %} checked{% endif %}>
                    <label class="form-check-label" for="payment-check">{{ text_check }}</label>
                  </div>
                </div>
              </div>

              <!-- تفاصيل النقدية -->
              <div id="cash-details" class="payment-details" style="display: {% if payment_method == 'cash' %}block{% else %}none{% endif %};">
                <div class="row mb-3">
                  <label for="input-cash-account" class="col-sm-2 col-form-label">{{ entry_cash_account }}</label>
                  <div class="col-sm-10">
                    <select name="cash_account_id" id="input-cash-account" class="form-select">
                      <option value="">{{ text_select }}</option>
                      {% for cash_account in cash_accounts %}
                        <option value="{{ cash_account.cash_id }}"{% if cash_account.cash_id == cash_account_id %} selected{% endif %}>{{ cash_account.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>

              <!-- تفاصيل البنك -->
              <div id="bank-details" class="payment-details" style="display: {% if payment_method == 'bank' %}block{% else %}none{% endif %};">
                <div class="row mb-3">
                  <label for="input-bank-account" class="col-sm-2 col-form-label">{{ entry_bank_account }}</label>
                  <div class="col-sm-10">
                    <select name="bank_account_id" id="input-bank-account" class="form-select">
                      <option value="">{{ text_select }}</option>
                      {% for bank_account in bank_accounts %}
                        <option value="{{ bank_account.account_id }}"{% if bank_account.account_id == bank_account_id %} selected{% endif %}>{{ bank_account.account_name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>

              <!-- تفاصيل الشيك -->
              <div id="check-details" class="payment-details" style="display: {% if payment_method == 'check' %}block{% else %}none{% endif %};">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="input-check-number" class="form-label">{{ entry_check_number }}</label>
                    <input type="text" name="check_number" value="{{ check_number }}" placeholder="{{ entry_check_number }}" id="input-check-number" class="form-control"/>
                  </div>
                  <div class="col-md-6">
                    <label for="input-check-date" class="form-label">{{ entry_check_date }}</label>
                    <input type="date" name="check_date" value="{{ check_date }}" id="input-check-date" class="form-control"/>
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label for="input-bank-name" class="form-label">{{ entry_bank_name }}</label>
                    <input type="text" name="bank_name" value="{{ bank_name }}" placeholder="{{ entry_bank_name }}" id="input-bank-name" class="form-control"/>
                  </div>
                  <div class="col-md-6">
                    <label for="input-check-bank-account" class="form-label">{{ entry_bank_account }}</label>
                    <select name="bank_account_id_check" id="input-check-bank-account" class="form-select">
                      <option value="">{{ text_select }}</option>
                      {% for bank_account in bank_accounts %}
                        <option value="{{ bank_account.account_id }}"{% if bank_account.account_id == bank_account_id %} selected{% endif %}>{{ bank_account.account_name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- تخصيص الفواتير -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">{{ text_invoice_allocation }}</h5>
              <button type="button" id="button-load-invoices" class="btn btn-sm btn-outline-primary">{{ button_load_invoices }}</button>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-bordered" id="invoice-table">
                  <thead>
                    <tr>
                      <th>{{ column_invoice_number }}</th>
                      <th>{{ column_invoice_date }}</th>
                      <th>{{ column_invoice_amount }}</th>
                      <th>{{ column_paid_amount }}</th>
                      <th>{{ column_remaining_amount }}</th>
                      <th>{{ column_allocation_amount }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% if allocations %}
                      {% for allocation in allocations %}
                        <tr>
                          <td>{{ allocation.invoice_number }}</td>
                          <td>{{ allocation.invoice_date }}</td>
                          <td class="text-end">{{ allocation.invoice_total }}</td>
                          <td class="text-end">{{ allocation.paid_amount }}</td>
                          <td class="text-end">{{ allocation.remaining_amount }}</td>
                          <td>
                            <input type="number" step="0.01" name="allocations[{{ allocation.invoice_id }}]" value="{{ allocation.allocated_amount }}" class="form-control allocation-amount" data-max="{{ allocation.remaining_amount }}">
                          </td>
                        </tr>
                      {% endfor %}
                    {% else %}
                      <tr>
                        <td colspan="6" class="text-center">{{ text_no_invoices }}</td>
                      </tr>
                    {% endif %}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- ملاحظات -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">{{ text_notes }}</h5>
            </div>
            <div class="card-body">
              <textarea name="notes" rows="4" placeholder="{{ entry_notes }}" class="form-control">{{ notes }}</textarea>
            </div>
          </div>

          <!-- معلومات إضافية -->
          {% if voucher_id %}
            <div class="card">
              <div class="card-header">
                <h5 class="mb-0">{{ text_additional_info }}</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <p><strong>{{ text_created_by }}:</strong> {{ created_by_name }}</p>
                    <p><strong>{{ text_created_date }}:</strong> {{ created_date }}</p>
                    {% if approved_by_name %}
                      <p><strong>{{ text_approved_by }}:</strong> {{ approved_by_name }}</p>
                      <p><strong>{{ text_approved_date }}:</strong> {{ approved_date }}</p>
                    {% endif %}
                  </div>
                  <div class="col-md-6">
                    {% if posted_by_name %}
                      <p><strong>{{ text_posted_by }}:</strong> {{ posted_by_name }}</p>
                      <p><strong>{{ text_posted_date }}:</strong> {{ posted_date }}</p>
                    {% endif %}
                    {% if journal_id %}
                      <p><strong>{{ text_journal_id }}:</strong>
                        <a href="{{ journal_link }}" target="_blank">{{ journal_id }}</a>
                      </p>
                    {% endif %}
                  </div>
                </div>
              </div>
            </div>
          {% endif %}
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
// تبديل طريقة الدفع
$('input[name="payment_method"]').on('change', function() {
    $('.payment-details').hide();
    $('#' + $(this).val() + '-details').show();
});

// حفظ السند
$('#button-save').on('click', function() {
    var formData = $('#form-voucher').serialize();

    $.ajax({
        url: 'index.php?route=finance/receipt_voucher&user_token={{ user_token }}',
        type: 'post',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#button-save').button('loading');
        },
        complete: function() {
            $('#button-save').button('reset');
        },
        success: function(json) {
            $('.alert-dismissible').remove();

            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa-solid fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }

            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa-solid fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');

                if (json['voucher_id']) {
                    $('input[name="voucher_id"]').val(json['voucher_id']);
                    $('#input-voucher-number').val(json['voucher_number']);
                }
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// اعتماد السند
$('#button-approve').on('click', function() {
    if (confirm('{{ text_confirm_approve }}')) {
        $.ajax({
            url: 'index.php?route=finance/receipt_voucher/approve&user_token={{ user_token }}',
            type: 'post',
            data: {voucher_id: $('input[name="voucher_id"]').val()},
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    location.reload();
                } else if (json['error']) {
                    alert(json['error']);
                }
            }
        });
    }
});

// ترحيل السند
$('#button-post').on('click', function() {
    if (confirm('{{ text_confirm_post }}')) {
        $.ajax({
            url: 'index.php?route=finance/receipt_voucher/post&user_token={{ user_token }}',
            type: 'post',
            data: {voucher_id: $('input[name="voucher_id"]').val()},
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    location.reload();
                } else if (json['error']) {
                    alert(json['error']);
                }
            }
        });
    }
});

// طباعة السند
$('#button-print').on('click', function() {
    window.open('index.php?route=finance/receipt_voucher/print&voucher_id=' + $('input[name="voucher_id"]').val() + '&user_token={{ user_token }}', '_blank');
});

// تحميل فواتير العميل
$('#button-load-invoices').on('click', function() {
    var customer_id = $('#input-customer').val();

    if (!customer_id) {
        alert('{{ text_select_customer_first }}');
        return;
    }

    $.ajax({
        url: 'index.php?route=finance/receipt_voucher/getCustomerInvoices&user_token={{ user_token }}',
        type: 'post',
        data: {customer_id: customer_id},
        dataType: 'json',
        success: function(json) {
            if (json['invoices']) {
                var html = '';

                $.each(json['invoices'], function(index, invoice) {
                    html += '<tr>';
                    html += '<td>' + invoice.invoice_number + '</td>';
                    html += '<td>' + invoice.invoice_date + '</td>';
                    html += '<td class="text-end">' + invoice.total_amount + '</td>';
                    html += '<td class="text-end">' + invoice.paid_amount + '</td>';
                    html += '<td class="text-end">' + invoice.remaining_amount + '</td>';
                    html += '<td><input type="number" step="0.01" name="allocations[' + invoice.invoice_id + ']" value="0" class="form-control allocation-amount" data-max="' + invoice.remaining_amount + '"></td>';
                    html += '</tr>';
                });

                $('#invoice-table tbody').html(html);
            }
        }
    });
});

// التحقق من مبلغ التخصيص
$(document).on('input', '.allocation-amount', function() {
    var max = parseFloat($(this).data('max'));
    var value = parseFloat($(this).val());

    if (value > max) {
        $(this).val(max);
        alert('{{ text_allocation_exceeds_remaining }}');
    }
});

// تحديث إجمالي التخصيص
$(document).on('input', '.allocation-amount', function() {
    var total = 0;
    $('.allocation-amount').each(function() {
        total += parseFloat($(this).val()) || 0;
    });

    $('#input-amount').val(total.toFixed(2));
});

// نسخ السند
$('#btn-duplicate').on('click', function(e) {
    e.preventDefault();

    if (!$('input[name="voucher_id"]').val()) {
        alert('{{ error_voucher_not_found }}');
        return;
    }

    if (confirm('{{ text_confirm_duplicate }}')) {
        $.ajax({
            url: 'index.php?route=finance/receipt_voucher/duplicate&user_token={{ user_token }}',
            type: 'post',
            data: {voucher_id: $('input[name="voucher_id"]').val()},
            dataType: 'json',
            beforeSend: function() {
                $('#btn-duplicate').prop('disabled', true);
            },
            success: function(json) {
                $('#btn-duplicate').prop('disabled', false);

                if (json['success']) {
                    alert(json['success']);
                    if (json['new_voucher_id']) {
                        window.location = 'index.php?route=finance/receipt_voucher/edit&voucher_id=' + json['new_voucher_id'] + '&user_token={{ user_token }}';
                    }
                } else if (json['error']) {
                    alert(json['error']);
                }
            },
            error: function() {
                $('#btn-duplicate').prop('disabled', false);
                alert('{{ text_ajax_error }}');
            }
        });
    }
});

// عكس السند
$('#btn-reverse').on('click', function(e) {
    e.preventDefault();

    if (!$('input[name="voucher_id"]').val()) {
        alert('{{ error_voucher_not_found }}');
        return;
    }

    var reason = prompt('{{ text_enter_reverse_reason }}');
    if (reason !== null) {
        $.ajax({
            url: 'index.php?route=finance/receipt_voucher/reverse&user_token={{ user_token }}',
            type: 'post',
            data: {
                voucher_id: $('input[name="voucher_id"]').val(),
                reason: reason
            },
            dataType: 'json',
            beforeSend: function() {
                $('#btn-reverse').prop('disabled', true);
            },
            success: function(json) {
                $('#btn-reverse').prop('disabled', false);

                if (json['success']) {
                    alert(json['success']);
                    if (json['reverse_voucher_id']) {
                        window.location = 'index.php?route=finance/receipt_voucher/edit&voucher_id=' + json['reverse_voucher_id'] + '&user_token={{ user_token }}';
                    }
                } else if (json['error']) {
                    alert(json['error']);
                }
            },
            error: function() {
                $('#btn-reverse').prop('disabled', false);
                alert('{{ text_ajax_error }}');
            }
        });
    }
});

// تصدير البيانات
$('#btn-export').on('click', function(e) {
    e.preventDefault();

    var format = prompt('{{ text_select_export_format }}\n1. CSV\n2. Excel\n3. PDF', 'csv');
    if (format && ['csv', 'excel', 'pdf'].includes(format.toLowerCase())) {
        var url = 'index.php?route=finance/receipt_voucher/export&user_token={{ user_token }}&format=' + format.toLowerCase();

        // إضافة فلاتر إضافية إذا كانت متوفرة
        var filters = getExportFilters();
        if (filters) {
            url += '&' + filters;
        }

        window.open(url, '_blank');
    }
});

// البحث المتقدم
$('#btn-search').on('click', function(e) {
    e.preventDefault();
    showAdvancedSearchModal();
});

// الحصول على فلاتر التصدير
function getExportFilters() {
    var filters = [];

    if ($('#input-customer').val()) {
        filters.push('customer_id=' + $('#input-customer').val());
    }

    if ($('#input-voucher-date').val()) {
        filters.push('date_from=' + $('#input-voucher-date').val());
        filters.push('date_to=' + $('#input-voucher-date').val());
    }

    return filters.join('&');
}

// عرض مودال البحث المتقدم
function showAdvancedSearchModal() {
    var modalHtml = `
        <div class="modal fade" id="searchModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ text_advanced_search }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="search-form">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ entry_filter_voucher_number }}</label>
                                    <input type="text" name="voucher_number" class="form-control">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ entry_filter_customer }}</label>
                                    <select name="customer_id" class="form-select">
                                        <option value="">{{ text_select }}</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.customer_id }}">{{ customer.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ entry_filter_date_from }}</label>
                                    <input type="date" name="date_from" class="form-control">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ entry_filter_date_to }}</label>
                                    <input type="date" name="date_to" class="form-control">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ entry_filter_amount_from }}</label>
                                    <input type="number" step="0.01" name="amount_from" class="form-control">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ entry_filter_amount_to }}</label>
                                    <input type="number" step="0.01" name="amount_to" class="form-control">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ entry_filter_status }}</label>
                                    <select name="status" class="form-select">
                                        <option value="">{{ text_select }}</option>
                                        <option value="draft">{{ text_status_draft }}</option>
                                        <option value="approved">{{ text_status_approved }}</option>
                                        <option value="posted">{{ text_status_posted }}</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ entry_filter_payment_method }}</label>
                                    <select name="payment_method" class="form-select">
                                        <option value="">{{ text_select }}</option>
                                        <option value="cash">{{ text_cash }}</option>
                                        <option value="bank">{{ text_bank }}</option>
                                        <option value="check">{{ text_check }}</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ button_cancel }}</button>
                        <button type="button" class="btn btn-primary" id="btn-search-execute">{{ button_search }}</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('body').append(modalHtml);
    $('#searchModal').modal('show');

    // تنفيذ البحث
    $('#btn-search-execute').on('click', function() {
        var searchData = $('#search-form').serialize();

        $.ajax({
            url: 'index.php?route=finance/receipt_voucher/search&user_token={{ user_token }}',
            type: 'post',
            data: searchData,
            dataType: 'json',
            success: function(json) {
                if (json['success'] && json['data']) {
                    displaySearchResults(json['data'], json['total']);
                    $('#searchModal').modal('hide');
                } else if (json['error']) {
                    alert(json['error']);
                }
            },
            error: function() {
                alert('{{ text_ajax_error }}');
            }
        });
    });

    // إزالة المودال عند الإغلاق
    $('#searchModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
}

// عرض نتائج البحث
function displaySearchResults(data, total) {
    var resultsHtml = `
        <div class="modal fade" id="resultsModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ text_search_results }} (${total})</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>{{ column_voucher_number }}</th>
                                        <th>{{ column_voucher_date }}</th>
                                        <th>{{ column_customer }}</th>
                                        <th>{{ column_amount }}</th>
                                        <th>{{ column_payment_method }}</th>
                                        <th>{{ column_status }}</th>
                                        <th>{{ column_action }}</th>
                                    </tr>
                                </thead>
                                <tbody>
    `;

    data.forEach(function(voucher) {
        resultsHtml += `
            <tr>
                <td>${voucher.voucher_number}</td>
                <td>${voucher.voucher_date}</td>
                <td>${voucher.customer_name}</td>
                <td class="text-end">${voucher.amount}</td>
                <td>${voucher.payment_method}</td>
                <td>
                    <span class="badge bg-${voucher.status === 'draft' ? 'secondary' : (voucher.status === 'approved' ? 'success' : 'primary')}">
                        ${voucher.status}
                    </span>
                </td>
                <td>
                    <a href="${voucher.edit}" class="btn btn-sm btn-primary">{{ button_edit }}</a>
                    <a href="${voucher.print}" class="btn btn-sm btn-info" target="_blank">{{ button_print }}</a>
                </td>
            </tr>
        `;
    });

    resultsHtml += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ button_close }}</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('body').append(resultsHtml);
    $('#resultsModal').modal('show');

    // إزالة المودال عند الإغلاق
    $('#resultsModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
}

// حفظ تلقائي
var autoSaveTimeout;
$('#form-voucher input, #form-voucher select, #form-voucher textarea').on('change', function() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(function() {
        if ($('input[name="voucher_id"]').val()) {
            $('#button-save').trigger('click');
        }
    }, 3000); // حفظ تلقائي بعد 3 ثوان
});

// تفعيل التلميحات
$(document).ready(function() {
    $('[data-bs-toggle="tooltip"]').tooltip();
});
//--></script>

{{ footer }}
