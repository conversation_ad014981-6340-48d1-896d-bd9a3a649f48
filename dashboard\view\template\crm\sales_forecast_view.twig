{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="crm\sales_forecast-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="crm\sales_forecast-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-adjustment_factors">{{ text_adjustment_factors }}</label>
            <div class="col-sm-10">
              <input type="text" name="adjustment_factors" value="{{ adjustment_factors }}" placeholder="{{ text_adjustment_factors }}" id="input-adjustment_factors" class="form-control" />
              {% if error_adjustment_factors %}
                <div class="invalid-feedback">{{ error_adjustment_factors }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics">{{ text_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics" value="{{ analytics }}" placeholder="{{ text_analytics }}" id="input-analytics" class="form-control" />
              {% if error_analytics %}
                <div class="invalid-feedback">{{ error_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-auto_generate">{{ text_auto_generate }}</label>
            <div class="col-sm-10">
              <input type="text" name="auto_generate" value="{{ auto_generate }}" placeholder="{{ text_auto_generate }}" id="input-auto_generate" class="form-control" />
              {% if error_auto_generate %}
                <div class="invalid-feedback">{{ error_auto_generate }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-auto_methods">{{ text_auto_methods }}</label>
            <div class="col-sm-10">
              <input type="text" name="auto_methods" value="{{ auto_methods }}" placeholder="{{ text_auto_methods }}" id="input-auto_methods" class="form-control" />
              {% if error_auto_methods %}
                <div class="invalid-feedback">{{ error_auto_methods }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-charts">{{ text_charts }}</label>
            <div class="col-sm-10">
              <input type="text" name="charts" value="{{ charts }}" placeholder="{{ text_charts }}" id="input-charts" class="form-control" />
              {% if error_charts %}
                <div class="invalid-feedback">{{ error_charts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comparisons">{{ text_comparisons }}</label>
            <div class="col-sm-10">
              <input type="text" name="comparisons" value="{{ comparisons }}" placeholder="{{ text_comparisons }}" id="input-comparisons" class="form-control" />
              {% if error_comparisons %}
                <div class="invalid-feedback">{{ error_comparisons }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-create">{{ text_create }}</label>
            <div class="col-sm-10">
              <input type="text" name="create" value="{{ create }}" placeholder="{{ text_create }}" id="input-create" class="form-control" />
              {% if error_create %}
                <div class="invalid-feedback">{{ error_create }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export">{{ text_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="export" value="{{ export }}" placeholder="{{ text_export }}" id="input-export" class="form-control" />
              {% if error_export %}
                <div class="invalid-feedback">{{ error_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-forecast">{{ text_forecast }}</label>
            <div class="col-sm-10">
              <input type="text" name="forecast" value="{{ forecast }}" placeholder="{{ text_forecast }}" id="input-forecast" class="form-control" />
              {% if error_forecast %}
                <div class="invalid-feedback">{{ error_forecast }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-forecast_charts">{{ text_forecast_charts }}</label>
            <div class="col-sm-10">
              <input type="text" name="forecast_charts" value="{{ forecast_charts }}" placeholder="{{ text_forecast_charts }}" id="input-forecast_charts" class="form-control" />
              {% if error_forecast_charts %}
                <div class="invalid-feedback">{{ error_forecast_charts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-forecast_details">{{ text_forecast_details }}</label>
            <div class="col-sm-10">
              <input type="text" name="forecast_details" value="{{ forecast_details }}" placeholder="{{ text_forecast_details }}" id="input-forecast_details" class="form-control" />
              {% if error_forecast_details %}
                <div class="invalid-feedback">{{ error_forecast_details }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-forecast_statistics">{{ text_forecast_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="forecast_statistics" value="{{ forecast_statistics }}" placeholder="{{ text_forecast_statistics }}" id="input-forecast_statistics" class="form-control" />
              {% if error_forecast_statistics %}
                <div class="invalid-feedback">{{ error_forecast_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-forecast_types">{{ text_forecast_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="forecast_types" value="{{ forecast_types }}" placeholder="{{ text_forecast_types }}" id="input-forecast_types" class="form-control" />
              {% if error_forecast_types %}
                <div class="invalid-feedback">{{ error_forecast_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-forecasts">{{ text_forecasts }}</label>
            <div class="col-sm-10">
              <input type="text" name="forecasts" value="{{ forecasts }}" placeholder="{{ text_forecasts }}" id="input-forecasts" class="form-control" />
              {% if error_forecasts %}
                <div class="invalid-feedback">{{ error_forecasts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-historical_data">{{ text_historical_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="historical_data" value="{{ historical_data }}" placeholder="{{ text_historical_data }}" id="input-historical_data" class="form-control" />
              {% if error_historical_data %}
                <div class="invalid-feedback">{{ error_historical_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-kpis">{{ text_kpis }}</label>
            <div class="col-sm-10">
              <input type="text" name="kpis" value="{{ kpis }}" placeholder="{{ text_kpis }}" id="input-kpis" class="form-control" />
              {% if error_kpis %}
                <div class="invalid-feedback">{{ error_kpis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-methods">{{ text_methods }}</label>
            <div class="col-sm-10">
              <input type="text" name="methods" value="{{ methods }}" placeholder="{{ text_methods }}" id="input-methods" class="form-control" />
              {% if error_methods %}
                <div class="invalid-feedback">{{ error_methods }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-periods">{{ text_periods }}</label>
            <div class="col-sm-10">
              <input type="text" name="periods" value="{{ periods }}" placeholder="{{ text_periods }}" id="input-periods" class="form-control" />
              {% if error_periods %}
                <div class="invalid-feedback">{{ error_periods }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-scenario_comparison">{{ text_scenario_comparison }}</label>
            <div class="col-sm-10">
              <input type="text" name="scenario_comparison" value="{{ scenario_comparison }}" placeholder="{{ text_scenario_comparison }}" id="input-scenario_comparison" class="form-control" />
              {% if error_scenario_comparison %}
                <div class="invalid-feedback">{{ error_scenario_comparison }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-scenario_types">{{ text_scenario_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="scenario_types" value="{{ scenario_types }}" placeholder="{{ text_scenario_types }}" id="input-scenario_types" class="form-control" />
              {% if error_scenario_types %}
                <div class="invalid-feedback">{{ error_scenario_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-scenarios">{{ text_scenarios }}</label>
            <div class="col-sm-10">
              <input type="text" name="scenarios" value="{{ scenarios }}" placeholder="{{ text_scenarios }}" id="input-scenarios" class="form-control" />
              {% if error_scenarios %}
                <div class="invalid-feedback">{{ error_scenarios }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}