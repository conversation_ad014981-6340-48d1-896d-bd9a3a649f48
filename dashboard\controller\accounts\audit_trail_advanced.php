<?php
/**
 * تحكم مسار التدقيق المتقدم
 * نظام تدقيق شامل مع تكامل محرر سير العمل المرئي
 * يدعم التدقيق التلقائي والذكي مع تتبع كامل للعمليات
 */
class ControllerAccountsAuditTrailAdvanced extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/audit_trail_advanced') ||
            !$this->user->hasKey('accounting_audit_trail_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_audit'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/audit_trail_advanced');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/audit_trail_advanced');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/audit_trail_advanced.css');
        $this->document->addScript('view/javascript/accounts/audit_trail_advanced.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');
        $this->document->addScript('view/javascript/workflow/visual_editor.js');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_audit_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'audit_trail_advanced'
        ]);

        // معالجة الطلبات
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            if (isset($this->request->post['action'])) {
                switch ($this->request->post['action']) {
                    case 'create_audit':
                        $this->createAudit();
                        break;
                    case 'start_audit':
                        $this->startAudit();
                        break;
                    case 'complete_audit':
                        $this->completeAudit();
                        break;
                    case 'add_finding':
                        $this->addFinding();
                        break;
                    case 'generate_report':
                        $this->generateReport();
                        break;
                }
            }
        }

        $data = $this->getCommonData();

        // جلب التدقيقات الحالية
        $filter = array(
            'status' => $this->request->get['status'] ?? '',
            'audit_type' => $this->request->get['audit_type'] ?? '',
            'date_from' => $this->request->get['date_from'] ?? '',
            'date_to' => $this->request->get['date_to'] ?? ''
        );

        $data['audits'] = $this->model_accounts_audit_trail_advanced->getAudits($filter);
        $data['filter'] = $filter;

        // جلب إحصائيات التدقيق
        $data['audit_statistics'] = $this->model_accounts_audit_trail_advanced->getAuditStatistics();

        // جلب التدقيقات النشطة
        $data['active_audits'] = $this->model_accounts_audit_trail_advanced->getActiveAudits();

        // جلب النتائج الحديثة
        $data['recent_findings'] = $this->model_accounts_audit_trail_advanced->getRecentFindings();

        // جلب قوالب التدقيق
        $data['audit_templates'] = $this->model_accounts_audit_trail_advanced->getAuditTemplates();

        // روابط Ajax
        $data['ajax_create_url'] = $this->url->link('accounts/audit_trail_advanced/create', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_start_url'] = $this->url->link('accounts/audit_trail_advanced/start', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_finding_url'] = $this->url->link('accounts/audit_trail_advanced/finding', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_workflow_url'] = $this->url->link('workflow/visual_editor/load', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/audit_trail_advanced', $data));
    }

    /**
     * إنشاء تدقيق جديد
     */
    public function create() {
        // فحص الصلاحيات المتقدمة
        if (!$this->user->hasKey('accounting_audit_trail_create')) {
            $json['error'] = $this->language->get('error_permission_create');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/audit_trail_advanced');
        $this->load->model('workflow/visual_workflow_engine');
        $json = array();

        try {
            $audit_data = array(
                'audit_name' => $this->request->post['audit_name'],
                'audit_type' => $this->request->post['audit_type'],
                'audit_scope' => $this->request->post['audit_scope'],
                'period_start' => $this->request->post['period_start'],
                'period_end' => $this->request->post['period_end'],
                'assigned_auditor' => $this->request->post['assigned_auditor'],
                'audit_objectives' => $this->request->post['audit_objectives'],
                'risk_level' => $this->request->post['risk_level'],
                'workflow_template_id' => $this->request->post['workflow_template_id'],
                'automated_checks' => isset($this->request->post['automated_checks']),
                'ai_assistance' => isset($this->request->post['ai_assistance'])
            );

            $audit_id = $this->model_accounts_audit_trail_advanced->createAudit($audit_data);

            if ($audit_id) {
                // إنشاء سير عمل التدقيق
                if (!empty($audit_data['workflow_template_id'])) {
                    $workflow_data = array(
                        'template_id' => $audit_data['workflow_template_id'],
                        'entity_type' => 'audit',
                        'entity_id' => $audit_id,
                        'initiated_by' => $this->user->getId()
                    );

                    $workflow_id = $this->model_workflow_visual_workflow_engine->createWorkflowInstance($workflow_data);
                    
                    // ربط سير العمل بالتدقيق
                    $this->model_accounts_audit_trail_advanced->linkWorkflow($audit_id, $workflow_id);
                }

                $json['success'] = $this->language->get('text_audit_created');
                $json['audit_id'] = $audit_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('create', 'accounts',
                    $this->language->get('log_audit_created'), [
                    'user_id' => $this->user->getId(),
                    'audit_id' => $audit_id,
                    'audit_name' => $audit_data['audit_name']
                ]);

                // إرسال إشعار للمدقق المعين
                $this->central_service->sendNotification([
                    'type' => 'audit_assigned',
                    'title' => $this->language->get('notification_audit_title'),
                    'message' => sprintf($this->language->get('notification_audit_message'), $audit_data['audit_name']),
                    'user_id' => $audit_data['assigned_auditor'],
                    'url' => $this->url->link('accounts/audit_trail_advanced/view', 'audit_id=' . $audit_id)
                ]);

            } else {
                $json['error'] = $this->language->get('error_audit_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
            
            // تسجيل الخطأ
            $this->central_service->logActivity('error', 'accounts',
                'Audit creation failed: ' . $e->getMessage(), [
                'user_id' => $this->user->getId(),
                'error_details' => $e->getTraceAsString()
            ]);
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * بدء التدقيق
     */
    public function start() {
        if (!$this->user->hasKey('accounting_audit_trail_execute')) {
            $json['error'] = $this->language->get('error_permission_start');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/audit_trail_advanced');
        $this->load->model('workflow/visual_workflow_engine');
        $json = array();

        try {
            $audit_id = $this->request->post['audit_id'];
            
            // بدء التدقيق
            $result = $this->model_accounts_audit_trail_advanced->startAudit($audit_id);

            if ($result) {
                // تشغيل سير العمل المرتبط
                $workflow_id = $this->model_accounts_audit_trail_advanced->getAuditWorkflow($audit_id);
                if ($workflow_id) {
                    $this->model_workflow_visual_workflow_engine->startWorkflow($workflow_id);
                }

                // تشغيل الفحوصات التلقائية إذا كانت مفعلة
                $audit = $this->model_accounts_audit_trail_advanced->getAudit($audit_id);
                if ($audit['automated_checks']) {
                    $this->runAutomatedChecks($audit_id);
                }

                $json['success'] = $this->language->get('text_audit_started');
                
                // تسجيل العملية
                $this->central_service->logActivity('start', 'accounts',
                    $this->language->get('log_audit_started'), [
                    'user_id' => $this->user->getId(),
                    'audit_id' => $audit_id
                ]);

            } else {
                $json['error'] = $this->language->get('error_start_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إضافة نتيجة تدقيق
     */
    public function finding() {
        if (!$this->user->hasKey('accounting_audit_trail_execute')) {
            $json['error'] = $this->language->get('error_permission_finding');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/audit_trail_advanced');
        $json = array();

        try {
            $finding_data = array(
                'audit_id' => $this->request->post['audit_id'],
                'finding_type' => $this->request->post['finding_type'],
                'severity' => $this->request->post['severity'],
                'finding_title' => $this->request->post['finding_title'],
                'finding_description' => $this->request->post['finding_description'],
                'affected_accounts' => $this->request->post['affected_accounts'],
                'financial_impact' => $this->request->post['financial_impact'],
                'recommendation' => $this->request->post['recommendation'],
                'responsible_party' => $this->request->post['responsible_party'],
                'due_date' => $this->request->post['due_date'],
                'evidence_files' => $this->request->post['evidence_files']
            );

            $finding_id = $this->model_accounts_audit_trail_advanced->addFinding($finding_data);

            if ($finding_id) {
                // إضافة الأدلة المرفقة
                if (!empty($finding_data['evidence_files'])) {
                    foreach ($finding_data['evidence_files'] as $file) {
                        $this->model_accounts_audit_trail_advanced->addEvidence($finding_id, $file);
                    }
                }

                $json['success'] = $this->language->get('text_finding_added');
                $json['finding_id'] = $finding_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('finding', 'accounts',
                    $this->language->get('log_finding_added'), [
                    'user_id' => $this->user->getId(),
                    'audit_id' => $finding_data['audit_id'],
                    'finding_id' => $finding_id,
                    'severity' => $finding_data['severity']
                ]);

                // إرسال إشعار للطرف المسؤول
                if (!empty($finding_data['responsible_party'])) {
                    $this->central_service->sendNotification([
                        'type' => 'audit_finding',
                        'title' => $this->language->get('notification_finding_title'),
                        'message' => sprintf($this->language->get('notification_finding_message'), 
                            $finding_data['finding_title']),
                        'user_id' => $finding_data['responsible_party'],
                        'url' => $this->url->link('accounts/audit_trail_advanced/view_finding', 'finding_id=' . $finding_id)
                    ]);
                }

            } else {
                $json['error'] = $this->language->get('error_finding_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إكمال التدقيق
     */
    public function complete() {
        if (!$this->user->hasKey('accounting_audit_trail_approve')) {
            $json['error'] = $this->language->get('error_permission_complete');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/audit_trail_advanced');
        $this->load->model('workflow/visual_workflow_engine');
        $json = array();

        try {
            $completion_data = array(
                'audit_id' => $this->request->post['audit_id'],
                'completion_notes' => $this->request->post['completion_notes'],
                'overall_rating' => $this->request->post['overall_rating'],
                'recommendations_summary' => $this->request->post['recommendations_summary'],
                'generate_report' => isset($this->request->post['generate_report'])
            );

            $result = $this->model_accounts_audit_trail_advanced->completeAudit($completion_data);

            if ($result) {
                // إكمال سير العمل المرتبط
                $workflow_id = $this->model_accounts_audit_trail_advanced->getAuditWorkflow($completion_data['audit_id']);
                if ($workflow_id) {
                    $this->model_workflow_visual_workflow_engine->completeWorkflow($workflow_id);
                }

                // توليد التقرير النهائي إذا كان مطلوباً
                if ($completion_data['generate_report']) {
                    $report_id = $this->model_accounts_audit_trail_advanced->generateFinalReport($completion_data['audit_id']);
                    $json['report_id'] = $report_id;
                }

                $json['success'] = $this->language->get('text_audit_completed');
                
                // تسجيل العملية
                $this->central_service->logActivity('complete', 'accounts',
                    $this->language->get('log_audit_completed'), [
                    'user_id' => $this->user->getId(),
                    'audit_id' => $completion_data['audit_id'],
                    'rating' => $completion_data['overall_rating']
                ]);

            } else {
                $json['error'] = $this->language->get('error_complete_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * عرض تفاصيل التدقيق
     */
    public function view() {
        if (!$this->user->hasPermission('access', 'accounts/audit_trail_advanced') ||
            !$this->user->hasKey('accounting_audit_trail_view')) {
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $audit_id = isset($this->request->get['audit_id']) ? (int)$this->request->get['audit_id'] : 0;

        if (!$audit_id) {
            $this->response->redirect($this->url->link('accounts/audit_trail_advanced'));
            return;
        }

        $this->load->language('accounts/audit_trail_advanced');
        $this->load->model('accounts/audit_trail_advanced');

        $data = $this->getCommonData();
        
        // جلب بيانات التدقيق
        $audit = $this->model_accounts_audit_trail_advanced->getAudit($audit_id);
        
        if (!$audit) {
            $this->response->redirect($this->url->link('accounts/audit_trail_advanced'));
            return;
        }

        $data['audit'] = $audit;
        $data['findings'] = $this->model_accounts_audit_trail_advanced->getAuditFindings($audit_id);
        $data['evidence'] = $this->model_accounts_audit_trail_advanced->getAuditEvidence($audit_id);
        $data['workflow_status'] = $this->model_accounts_audit_trail_advanced->getWorkflowStatus($audit_id);
        $data['progress'] = $this->model_accounts_audit_trail_advanced->getAuditProgress($audit_id);

        $this->document->setTitle($this->language->get('heading_title_view') . ' - ' . $audit['audit_name']);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/audit_trail_advanced_view', $data));
    }

    /**
     * تشغيل الفحوصات التلقائية
     */
    private function runAutomatedChecks($audit_id) {
        $this->load->model('ai/ai_assistant');
        
        // تشغيل فحوصات الذكاء الاصطناعي
        $ai_checks = $this->model_ai_ai_assistant->runAuditChecks($audit_id);
        
        // حفظ نتائج الفحوصات التلقائية
        foreach ($ai_checks as $check) {
            if ($check['anomaly_detected']) {
                $finding_data = array(
                    'audit_id' => $audit_id,
                    'finding_type' => 'automated',
                    'severity' => $check['severity'],
                    'finding_title' => $check['title'],
                    'finding_description' => $check['description'],
                    'affected_accounts' => $check['affected_accounts'],
                    'financial_impact' => $check['financial_impact'],
                    'recommendation' => $check['recommendation'],
                    'is_automated' => true
                );

                $this->model_accounts_audit_trail_advanced->addFinding($finding_data);
            }
        }
    }

    /**
     * جلب البيانات المشتركة
     */
    private function getCommonData() {
        $data = array();

        // النصوص
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_list'] = $this->language->get('text_list');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_confirm'] = $this->language->get('text_confirm');

        // الأزرار
        $data['button_create'] = $this->language->get('button_create');
        $data['button_start'] = $this->language->get('button_start');
        $data['button_complete'] = $this->language->get('button_complete');
        $data['button_add_finding'] = $this->language->get('button_add_finding');
        $data['button_view'] = $this->language->get('button_view');
        $data['button_cancel'] = $this->language->get('button_cancel');

        // الروابط
        $data['cancel'] = $this->url->link('accounts/audit_trail_advanced', 'user_token=' . $this->session->data['user_token'], true);
        $data['user_token'] = $this->session->data['user_token'];

        return $data;
    }
}
