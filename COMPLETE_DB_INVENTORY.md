# قائمة شاملة بجميع جداول db.txt - AYM ERP

**تاريخ الحصر:** 2025-07-28  
**إجمالي الجداول:** 451 جدول  
**الهدف:** حصر كامل لتجنب إنشاء جداول موجودة  

---

## 📋 **الجداول الموجودة فعلاً في db.txt**

### **A - الحسابات والمحاسبة (Accounts & Accounting)**
- ✅ `cod_accounts` - الحسابات المحاسبية
- ✅ `cod_accounts_backup` - نسخ احتياطية للحسابات
- ✅ `cod_account_description` - وصف الحسابات
- ✅ `cod_account_description_backup` - نسخ احتياطية لوصف الحسابات
- ✅ `cod_account_query_cache` - تخزين مؤقت لاستعلامات الحسابات
- ✅ `cod_account_query_favorites` - الاستعلامات المفضلة
- ✅ `cod_account_usage_stats` - إحصائيات استخدام الحسابات
- ✅ `cod_aging_analysis` - تحليل الأعمار
- ✅ `cod_aging_report_cache` - تخزين مؤقت لتقارير الأعمار
- ✅ `cod_annual_tax_cache` - تخزين مؤقت للضرائب السنوية
- ✅ `cod_balance_sheet_cache` - تخزين مؤقت للميزانية
- ✅ `cod_budget` - الموازنات
- ✅ `cod_budget_cache` - تخزين مؤقت للموازنات
- ✅ `cod_budget_line` - بنود الموازنة
- ✅ `cod_budget_performance` - أداء الموازنة
- ✅ `cod_cash` - النقدية
- ✅ `cod_cash_flow_analysis` - تحليل التدفق النقدي
- ✅ `cod_cash_flow_cache` - تخزين مؤقت للتدفق النقدي
- ✅ `cod_cash_transaction` - معاملات النقدية
- ✅ `cod_chart_account_cache` - تخزين مؤقت لدليل الحسابات
- ✅ `cod_checks` - الشيكات
- ✅ `cod_cost_center` - مراكز التكلفة
- ✅ `cod_cost_center_budgets` - موازنات مراكز التكلفة
- ✅ `cod_cost_center_cache` - تخزين مؤقت لمراكز التكلفة
- ✅ `cod_cost_center_performance` - أداء مراكز التكلفة
- ✅ `cod_financial_analysis` - التحليل المالي
- ✅ `cod_financial_forecast` - التنبؤات المالية
- ✅ `cod_financial_kpis` - مؤشرات الأداء المالية
- ✅ `cod_financial_ratios` - النسب المالية
- ✅ `cod_financial_reports_cache` - تخزين مؤقت للتقارير المالية
- ✅ `cod_general_ledger_analysis` - تحليل دفتر الأستاذ
- ✅ `cod_general_ledger_cache` - تخزين مؤقت لدفتر الأستاذ
- ✅ `cod_general_ledger_monthly` - دفتر الأستاذ الشهري
- ✅ `cod_income_statement_cache` - تخزين مؤقت لقائمة الدخل
- ✅ `cod_journals` - اليوميات
- ✅ `cod_journals_backup` - نسخ احتياطية لليوميات
- ✅ `cod_journal_attachments` - مرفقات اليومية
- ✅ `cod_journal_attachments_backup` - نسخ احتياطية لمرفقات اليومية
- ✅ `cod_journal_cache` - تخزين مؤقت لليوميات
- ✅ `cod_journal_entries` - قيود اليومية
- ✅ `cod_journal_entry_backup` - نسخ احتياطية لقيود اليومية
- ✅ `cod_journal_entry_cache` - تخزين مؤقت لقيود اليومية
- ✅ `cod_journal_review_cache` - تخزين مؤقت لمراجعة اليوميات
- ✅ `cod_journal_search_index` - فهرس البحث في اليوميات
- ✅ `cod_journal_security_cache` - تخزين مؤقت لأمان اليوميات
- ✅ `cod_period_closing` - إقفال الفترات
- ✅ `cod_period_closing_cache` - تخزين مؤقت لإقفال الفترات
- ✅ `cod_period_closing_validation` - التحقق من إقفال الفترات
- ✅ `cod_profitability_analysis_cache` - تخزين مؤقت لتحليل الربحية
- ✅ `cod_statement_account_cache` - تخزين مؤقت لكشوف الحسابات
- ✅ `cod_statement_range_cache` - تخزين مؤقت لنطاقات الكشوف
- ✅ `cod_trial_balance_cache` - تخزين مؤقت لميزان المراجعة

### **B - البنوك والمدفوعات (Banking & Payments)**
- ✅ `cod_bank` - البنوك
- ✅ `cod_bank_account` - حسابات البنوك
- ✅ `cod_bank_reconciliation` - تسوية البنوك
- ✅ `cod_bank_transaction` - معاملات البنوك
- ✅ `cod_payment_gateway` - بوابات الدفع
- ✅ `cod_payment_gateway_config` - إعدادات بوابات الدفع
- ✅ `cod_payment_invoice` - فواتير الدفع
- ✅ `cod_payment_settlement` - تسوية المدفوعات
- ✅ `cod_payment_settlement_transaction` - معاملات تسوية المدفوعات
- ✅ `cod_payment_transaction` - معاملات الدفع
- ✅ `cod_paymentlinks` - روابط الدفع
- ✅ `cod_vendor_payment` - مدفوعات الموردين

### **C - العملاء (Customers)**
- ✅ `cod_customer` - العملاء
- ✅ `cod_customer_activity` - نشاط العملاء
- ✅ `cod_customer_affiliate` - شراكة العملاء
- ✅ `cod_customer_approval` - موافقات العملاء
- ✅ `cod_customer_credit_limit` - حدود ائتمان العملاء
- ✅ `cod_customer_feedback` - تقييمات العملاء
- ✅ `cod_customer_group` - مجموعات العملاء
- ✅ `cod_customer_group_description` - وصف مجموعات العملاء
- ✅ `cod_customer_history` - تاريخ العملاء
- ✅ `cod_customer_ip` - عناوين IP للعملاء
- ✅ `cod_customer_login` - تسجيل دخول العملاء
- ✅ `cod_customer_note` - ملاحظات العملاء
- ✅ `cod_customer_online` - العملاء المتصلين
- ✅ `cod_customer_return_inventory` - مخزون مرتجعات العملاء
- ✅ `cod_customer_reward` - مكافآت العملاء
- ✅ `cod_customer_search` - بحث العملاء
- ✅ `cod_customer_transaction` - معاملات العملاء
- ✅ `cod_customer_wishlist` - قائمة أمنيات العملاء

### **D - المخزون والمنتجات (Inventory & Products)**
- ✅ `cod_product` - المنتجات
- ✅ `cod_product_attribute` - خصائص المنتجات
- ✅ `cod_product_barcode` - باركود المنتجات
- ✅ `cod_product_batch` - دفعات المنتجات
- ✅ `cod_product_bundle` - حزم المنتجات
- ✅ `cod_product_bundle_item` - عناصر حزم المنتجات
- ✅ `cod_product_description` - وصف المنتجات
- ✅ `cod_product_dynamic_pricing` - التسعير الديناميكي
- ✅ `cod_product_egs` - EGS للمنتجات
- ✅ `cod_product_filter` - فلاتر المنتجات
- ✅ `cod_product_image` - صور المنتجات
- ✅ `cod_product_inventory` - مخزون المنتجات
- ✅ `cod_product_inventory_history` - تاريخ مخزون المنتجات
- ✅ `cod_product_movement` - **حركة المنتجات (موجود!)**
- ✅ `cod_product_option` - خيارات المنتجات
- ✅ `cod_product_option_value` - قيم خيارات المنتجات
- ✅ `cod_product_price_history` - تاريخ أسعار المنتجات
- ✅ `cod_product_pricing` - تسعير المنتجات
- ✅ `cod_product_quantity_discounts` - خصومات الكمية
- ✅ `cod_product_recommendation` - توصيات المنتجات
- ✅ `cod_product_recurring` - المنتجات المتكررة
- ✅ `cod_product_related` - المنتجات ذات الصلة
- ✅ `cod_product_reward` - مكافآت المنتجات
- ✅ `cod_product_to_category` - ربط المنتجات بالفئات
- ✅ `cod_product_to_layout` - ربط المنتجات بالتخطيطات
- ✅ `cod_product_to_store` - ربط المنتجات بالمتاجر
- ✅ `cod_product_unit` - وحدات المنتجات
- ✅ `cod_inventory_abc_analysis` - تحليل ABC للمخزون
- ✅ `cod_inventory_accounting_reconciliation` - تسوية محاسبة المخزون
- ✅ `cod_inventory_accounting_reconciliation_item` - عناصر تسوية المخزون
- ✅ `cod_inventory_account_mapping` - ربط حسابات المخزون
- ✅ `cod_inventory_alert` - تنبيهات المخزون
- ✅ `cod_inventory_cost_history` - تاريخ تكلفة المخزون
- ✅ `cod_inventory_cost_update` - تحديث تكلفة المخزون
- ✅ `cod_inventory_count` - جرد المخزون
- ✅ `cod_inventory_reservation` - حجز المخزون
- ✅ `cod_inventory_role_permissions` - صلاحيات أدوار المخزون
- ✅ `cod_inventory_sheet` - ورقة المخزون
- ✅ `cod_inventory_sheet_item` - عناصر ورقة المخزون
- ✅ `cod_inventory_status_log` - سجل حالة المخزون
- ✅ `cod_inventory_sync_rules` - قواعد مزامنة المخزون
- ✅ `cod_inventory_transfer` - نقل المخزون
- ✅ `cod_inventory_transfer_item` - عناصر نقل المخزون
- ✅ `cod_inventory_turnover` - دوران المخزون
- ✅ `cod_inventory_turnover_analysis` - تحليل دوران المخزون
- ✅ `cod_inventory_valuation` - تقييم المخزون
- ✅ `cod_inventory_valuation_cache` - تخزين مؤقت لتقييم المخزون
- ✅ `cod_stock_adjustment` - تسوية المخزون
- ✅ `cod_stock_adjustment_history` - تاريخ تسوية المخزون
- ✅ `cod_stock_adjustment_item` - عناصر تسوية المخزون
- ✅ `cod_stock_count` - عد المخزون
- ✅ `cod_stock_count_item` - عناصر عد المخزون
- ✅ `cod_stock_status` - حالة المخزون
- ✅ `cod_stock_transfer` - نقل المخزون
- ✅ `cod_stock_transfer_history` - تاريخ نقل المخزون
- ✅ `cod_stock_transfer_item` - عناصر نقل المخزون

### **E - الطلبات والمبيعات (Orders & Sales)**
- ✅ `cod_order` - الطلبات
- ✅ `cod_order_cogs` - تكلفة البضاعة المباعة
- ✅ `cod_order_history` - تاريخ الطلبات
- ✅ `cod_order_option` - خيارات الطلبات
- ✅ `cod_order_product` - منتجات الطلبات
- ✅ `cod_order_shipment` - شحنات الطلبات
- ✅ `cod_order_shipment_history` - تاريخ شحنات الطلبات
- ✅ `cod_order_status` - حالة الطلبات
- ✅ `cod_order_total` - **إجماليات الطلبات (يحتوي على shipping_cost!)**
- ✅ `cod_order_voucher` - قسائم الطلبات
- ✅ `cod_sales_analysis_cache` - تخزين مؤقت لتحليل المبيعات
- ✅ `cod_sales_forecast` - توقعات المبيعات
- ✅ `cod_sales_quotation` - عروض أسعار المبيعات
- ✅ `cod_sales_quotation_item` - عناصر عروض أسعار المبيعات

### **F - المشتريات والموردين (Purchases & Suppliers)**
- ✅ `cod_supplier` - الموردين
- ✅ `cod_supplier_address` - عناوين الموردين
- ✅ `cod_supplier_evaluation` - تقييم الموردين
- ✅ `cod_supplier_group` - مجموعات الموردين
- ✅ `cod_supplier_group_description` - وصف مجموعات الموردين
- ✅ `cod_supplier_invoice` - فواتير الموردين
- ✅ `cod_supplier_invoice_history` - تاريخ فواتير الموردين
- ✅ `cod_supplier_invoice_item` - عناصر فواتير الموردين
- ✅ `cod_supplier_product_price` - أسعار منتجات الموردين
- ✅ `cod_purchase_analysis_cache` - تخزين مؤقت لتحليل المشتريات
- ✅ `cod_purchase_document` - مستندات المشتريات
- ✅ `cod_purchase_log` - سجل المشتريات
- ✅ `cod_purchase_matching` - مطابقة المشتريات
- ✅ `cod_purchase_matching_item` - عناصر مطابقة المشتريات
- ✅ `cod_purchase_order` - **أوامر الشراء (موجود!)**
- ✅ `cod_purchase_order_history` - تاريخ أوامر الشراء
- ✅ `cod_purchase_order_item` - عناصر أوامر الشراء
- ✅ `cod_purchase_order_tracking` - تتبع أوامر الشراء
- ✅ `cod_purchase_price_variance` - تباين أسعار المشتريات
- ✅ `cod_purchase_quotation` - عروض أسعار المشتريات
- ✅ `cod_purchase_quotation_history` - تاريخ عروض أسعار المشتريات
- ✅ `cod_purchase_quotation_item` - عناصر عروض أسعار المشتريات
- ✅ `cod_purchase_requisition` - طلبات الشراء
- ✅ `cod_purchase_requisition_history` - تاريخ طلبات الشراء
- ✅ `cod_purchase_requisition_item` - عناصر طلبات الشراء
- ✅ `cod_purchase_return` - مرتجعات المشتريات
- ✅ `cod_purchase_return_history` - تاريخ مرتجعات المشتريات
- ✅ `cod_purchase_return_item` - عناصر مرتجعات المشتريات
- ✅ `cod_goods_receipt` - استلام البضائع
- ✅ `cod_goods_receipt_item` - عناصر استلام البضائع

---

## ❌ **الجداول المفقودة الفعلية من error.txt**

بناءً على مراجعة error.txt وقائمة الجداول الموجودة، الجداول المفقودة الفعلية هي:

### **جداول الذكاء الاصطناعي:**
- ❌ `cod_ai_fraud_detection`
- ❌ `cod_ai_sentiment_analysis`
- ❌ `cod_ai_price_optimization`
- ❌ `cod_ai_demand_forecast`
- ❌ `cod_ai_chatbot_interactions`
- ❌ `cod_ai_customer_behavior`
- ❌ `cod_ai_supply_chain_optimization`
- ❌ `cod_ai_classification`

### **جداول التحليلات المتقدمة:**
- ❌ `cod_analytics_models`
- ❌ `cod_data_processing_log`
- ❌ `cod_data_quality_assessment`
- ❌ `cod_dashboard_usage_log`
- ❌ `cod_report_generation_log`

### **جداول الامتثال والأمان:**
- ❌ `cod_compliance_audit`
- ❌ `cod_internal_controls`
- ❌ `cod_security_incidents`

### **جداول التسويق الإلكتروني:**
- ❌ `cod_email_campaign`

### **جداول الأداء:**
- ❌ `cod_website_performance`

---

## 🎯 **الخلاصة المهمة**

1. **451 جدول موجود** في db.txt - النظام شامل جداً!
2. **حركة المخزون موجودة** في `cod_product_movement`
3. **shipping_cost موجود** في `cod_order_total`
4. **الجداول المفقودة** هي فقط المتقدمة (AI, Analytics, Compliance)
5. **المشكلة الأساسية** في الكود وليس في قاعدة البيانات

**🔥 النظام أكثر تطوراً مما توقعت! المطلوب فقط إصلاح الكود وإنشاء الجداول المتقدمة.**
