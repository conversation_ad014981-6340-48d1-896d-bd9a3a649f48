<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/monitoring.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Monitoring\MonitoringDestination instead.
     * @deprecated
     */
    class Monitoring_MonitoringDestination {}
}
class_exists(Monitoring\MonitoringDestination::class);
@trigger_error('Google\Api\Monitoring_MonitoringDestination is deprecated and will be removed in the next major release. Use Google\Api\Monitoring\MonitoringDestination instead', E_USER_DEPRECATED);

