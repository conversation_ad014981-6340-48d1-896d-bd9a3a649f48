{"name": "google/cloud-translate", "description": "Cloud Translation Client for PHP", "license": "Apache-2.0", "minimum-stability": "stable", "require": {"google/cloud-core": "^1.39", "google/gax": "^1.1"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.0", "squizlabs/php_codesniffer": "2.*", "phpdocumentor/reflection": "^3.0", "erusev/parsedown": "^1.6"}, "suggest": {"ext-grpc": "The gRPC extension enables use of the performant gRPC transport", "ext-protobuf": "Provides a significant increase in throughput over the pure PHP protobuf implementation. See https://cloud.google.com/php/grpc for installation instructions."}, "extra": {"component": {"id": "cloud-translate", "target": "googleapis/google-cloud-php-translate.git", "path": "Translate", "entry": "src/TranslateClient.php"}}, "autoload": {"psr-4": {"Google\\Cloud\\Translate\\": "src", "GPBMetadata\\Google\\Cloud\\Translate\\": "metadata"}}, "autoload-dev": {"psr-4": {"Google\\Cloud\\Translate\\Tests\\": "tests"}}}