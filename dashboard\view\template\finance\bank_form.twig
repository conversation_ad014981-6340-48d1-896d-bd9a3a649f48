{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-bank" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-bank" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
              {% if error_name %}
              <div class="text-danger">{{ error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-account-number">{{ entry_account_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="account_number" value="{{ account_number }}" placeholder="{{ entry_account_number }}" id="input-account-number" class="form-control" />
              {% if error_account_number %}
              <div class="text-danger">{{ error_account_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-branch">{{ entry_branch }}</label>
            <div class="col-sm-10">
              <input type="text" name="branch" value="{{ branch }}" placeholder="{{ entry_branch }}" id="input-branch" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-swift-code">{{ entry_swift_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="swift_code" value="{{ swift_code }}" placeholder="{{ entry_swift_code }}" id="input-swift-code" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-address">{{ entry_address }}</label>
            <div class="col-sm-10">
              <textarea name="address" placeholder="{{ entry_address }}" id="input-address" class="form-control">{{ address }}</textarea>
            </div>
            </div>
            <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
            <select name="status" id="input-status" class="form-control">
            {% if status %}
            <option value="1" selected="selected">{{ text_enabled }}</option>
            <option value="0">{{ text_disabled }}</option>
            {% else %}
            <option value="1">{{ text_enabled }}</option>
            <option value="0" selected="selected">{{ text_disabled }}</option>
            {% endif %}
            </select>
            </div>
            </div>
            </form>
            </div>
            </div>
              </div>
            </div>
            {{ footer }}
