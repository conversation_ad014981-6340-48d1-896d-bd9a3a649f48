<?php

// Heading
$_['heading_campaign']                      = 'Smart Shopping Ad Campaigns';
$_['heading_mapping']                       = 'Category Mapping';
$_['heading_merchant']                      = 'Google Merchant Center Account';
$_['heading_shipping_taxes']                = 'Shipping & Taxes';
$_['heading_title']                         = 'تسويق جوجل - Google Shopping';

// Steps
$_['step_campaigns']                        = 'Smart Shopping Ad Campaigns';
$_['step_connect']                          = 'Connect';
$_['step_mapping']                          = 'Category Mapping';
$_['step_merchant_account']                 = 'Merchant Center Account';
$_['step_shipping_taxes']                   = 'Shipping &amp; Taxes';

// Help
$_['help_adult']                            = 'Use this attribute to indicate that individual products are for adults only because they contain adult content such as nudity, sexually suggestive content, or are intended to enhance sexual activity. Google cares what they show to their users, so if your business model is not adult oriented but you&apos;re selling an individual adult-oriented product, you need to label that product clearly so Google can show appropriate and legally compliant content to people shopping online.';
$_['help_age_group']                        = 'Use this attribute to set the demographic that your product is designed for. When you use this attribute, your ad can appear in results that are filtered by age. For example, if results are filtered by Women instead of Girls. The Age Group can also work together with the Gender to help ensure that users see the correct size information.';
$_['help_budget']                           = 'Average daily budget to spend on this campaign. Any spending on ads will be deducted from your purchased credit. Credit is deducted only when someone clicks on your ad.';
$_['help_carrier_price_percentage']         = 'Estimate the shipment price as percentage of the product price.';
$_['help_color']                            = 'If the product a color option, select it here. This is a required attribute for all apparel items in feeds that target Brazil, France, Germany, Japan, the UK, and the US as well as all products available in different colors.';
$_['help_condition']                        = 'Tell users about the condition of the product you&apos;re selling. Setting this value correctly is important since Google uses it to refine search results.';
$_['help_cron_email']                       = 'A summary of the CRON task will be sent to this e-mail after completion.';
$_['help_cron_email_status']                = 'Enable to receive a summary after every CRON task.';
$_['help_feed']                             = 'If a language or currency is inactive, this means it was not found on your OpenCart store. To set it up, go to System > Localisation > Languages / Currencies';
$_['help_gender']                           = 'Specify the gender your product is designed for using the gender attribute. When you provide this information, potential customers can accurately filter products by gender to help narrow their search. Keep in mind that Google also uses the gender information together with the values you provide for Size and Age Group to standardize the sizes that are shown to users.';
$_['help_google_product_category']          = 'Use the this attribute to indicate the category of your item based on the Google product taxonomy. Categorizing your product helps ensure that your ad is shown with the right search results.';
$_['help_is_bundle']                        = 'Use the Bundle attribute to indicate that you&apos;ve created a bundle: a main product that you&apos;ve grouped with other, different products, sold together as one package for a single price. This attribute lets Google show your ad in the right situations by distinguishing your item from manufacturer-created bundles, multipacks, and other products without accessories.';
$_['help_local_cron']                       = '<strong>This method is recommended.</strong> Insert this command in your web server CRON tab. Set it up to run every hour.';
$_['help_multipack']                        = 'Use the Multipack attribute to indicate that you&apos;ve grouped multiple identical products for sale as one item. This attribute lets Google show your ad in the right situations by distinguishing your item from manufacturer-created multipacks, bundles, and other products.';
$_['help_remote_cron']                      = '<strong>Use this method in case Method #1 cannot be used.</strong> Use this URL to set up a CRON task via a web-based CRON service. Set it up to run every hour.';
$_['help_roas']                             = 'Target ROAS lets you bid based on a target return on ad spend (ROAS). This Google Ads Smart Bidding strategy helps you get more conversion value or revenue at the target return-on-ad-spend (ROAS) you set. Your bids are automatically optimized at auction-time, allowing you to tailor bids for each auction.';
$_['help_size']                             = 'Use the size attribute to describe the standardized size of your product. When you use this attribute, your ad can appear in results that are filtered by size. The size you submit will also affect how your product variants are shown.';
$_['help_size_system']                      = 'With this attribute you can explain which country&apos;s sizing system your product uses. This information helps create accurate filters, which users can use to narrow search results. The sizing system that you submit will affect search, filtering, and how variants are shown in your ad.';
$_['help_size_type']                        = 'Use this attribute to describe the cut of your product. This information helps create accurate filters, which users can use to narrow search results.';

// Entry
$_['entry_action']                          = 'Action';
$_['entry_adult']                           = 'Adult-Only Content';
$_['entry_age_group']                       = 'Age Group';
$_['entry_auto_advertise']                  = 'Automatically advertise new listings?';
$_['entry_budget']                          = 'Daily Campaign Budget';
$_['entry_campaign']                        = 'Smart Shopping Ad Campaigns';
$_['entry_campaign_name']                   = 'Campaign Name';
$_['entry_color']                           = 'Color Option';
$_['entry_condition']                       = 'Condition';
$_['entry_country']                         = 'Target Country';
$_['entry_feed']                            = 'Product Feeds';
$_['entry_gender']                          = 'Gender';
$_['entry_google_product_category']         = 'Google Product Category';
$_['entry_is_bundle']                       = 'Bundle';
$_['entry_max_transit_time']                = 'Maximum Transit Time (days)';
$_['entry_min_transit_time']                = 'Minimum Transit Time (days)';
$_['entry_multipack']                       = 'Multipack (number of items in a single package)';
$_['entry_oc_category']                     = 'OpenCart Category (autocomplete)';
$_['entry_roas']                            = 'ROAS';
$_['entry_setup_confirmation']              = 'Setup Confirmation';
$_['entry_size']                            = 'Size Option';
$_['entry_size_system']                     = 'Size System';
$_['entry_size_type']                       = 'Size Type';
$_['entry_status']                          = 'Status';

// Texts
$_['text_access_token']                     = 'Access token';
$_['text_acknowledge_add_campaign_1']       = 'I acknowledge that my campaigns will not become active until my product feeds get approved according to the <a href="https://support.google.com/merchants/answer/6149970" target="_blank"><strong>Google Merchant Center requirements</strong></a>.';
$_['text_acknowledge_add_campaign_2']       = 'I acknowledge that my campaigns will not become active until I set up <a href="https://support.google.com/merchants/topic/7293661?hl=en-GB&ref_topic=7259125" target="_blank"><strong>Shipping</strong></a> and <a href="https://support.google.com/merchants/topic/7294266?hl=en-GB&ref_topic=7259125" target="_blank"><strong>Tax (only in the US)</strong></a> details for my Merchant Center account.';
$_['text_acknowledge_cron']                 = 'I confirm that I have set up an automated CRON task using one of the methods above.';
$_['text_acknowledge_merchant_tos']         = 'By purchasing Google Shopping ads, I agree to comply with Google&apos;s terms and policies, including Google&apos;s <a href="https://support.google.com/merchants/answer/160173?hl=en" target="_blank">Merchant Center terms of service</a>, <a href="https://support.google.com/merchants/answer/6149970?hl=en" target="_blank">Shopping ads policies</a>, and <a href="https://billing.google.com/payments/u/0/paymentsinfofinder?style=:md#" target="_blank">Google Ads Terms and Conditions</a>.';
$_['text_action']                           = 'Action';
$_['text_active']                           = 'Active';
$_['text_active_states']                    = 'Select active states';
$_['text_add_target']                       = 'New Campaign';
$_['text_ads_intro']                        = '<h3>Important</h3><p>To have your products accepted by Google Merchant Center, please make sure to follow these requirements:</p><ul><li>Your OpenCart products must meet the <a href="https://support.google.com/merchants/answer/6149970" target="_blank"><strong>Google Shopping ads policies</strong></a>.</li><li>Please make sure to configure <a href="https://support.google.com/merchants/topic/7293661?hl=en-GB&ref_topic=7259125" target="_blank"><strong>Shipping</strong></a> and <a href="https://support.google.com/merchants/topic/7294266?hl=en-GB&ref_topic=7259125" target="_blank"><strong>Taxes (only in the US)</strong></a> in your Google Merchant Center account, or via this extension from the <a href="%s" target="_blank"><strong>Shipping &amp; Taxes</strong></a> section.</li><li>Refer to the columns <strong>Destination Statuses</strong> and <strong>Item Issues</strong> to resolve any approval issues with your products.</li></ul>';
$_['text_advertise']                        = 'Advertise';
$_['text_age_group_adult']                  = 'Adult (teens or older)';
$_['text_age_group_infant']                 = 'Infant (3-12 months old)';
$_['text_age_group_kids']                   = 'Kids (6-13 years old)';
$_['text_age_group_newborn']                = 'Newborn (0-2 months old)';
$_['text_age_group_toddler']                = 'Toddler (1-5 years old)';
$_['text_all']                              = 'All';
$_['text_app_id']                           = 'App ID';
$_['text_app_secret']                       = 'App Secret';
$_['text_approved']                         = 'Approved';
$_['text_campaign_more_info']               = '<h4>Campaign Duration</h4><p>Campaigns will run until paused. You can pause a campaign at any time.</p><hr /><h4>Campaign Optimization</h4><p>It usually takes around 30 days for Google to rank products and optimize shopping ad campaigns.</p><hr /><h4>Products in Campaign</h4><p>Google will create a unique shopping ad for each approved product synced to your Merchant Center account. Ads are optimized based to maximize your sales. Popular products will likely receive more of your budget.</p><hr /><h4>Campaign Duration</h4><p>Campaigns will run until paused. You can pause a campaign at any time.</p><hr /><h4>Shopping Ad Placement</h4><p>Your ads may appear on multiple platforms including Google Search, Google Display Network, Youtube, and Gmail.</p>';
$_['text_campaigns']                        = 'Campaigns';
$_['text_carrier_postcode']                 = 'Origin Postal Code';
$_['text_carrier_price_percentage']         = 'Price Percentage';
$_['text_checklist_acknowledge_0']          = '<h4>Visibility of product catalog</h4><p>I confirm my product catalog is publically accessible without requiring a password.</p>';
$_['text_checklist_acknowledge_1']          = '<h4>Secure checkout process</h4><p>Payment and transaction processing, as well as collection of any sensitive and financial personal information from the user, are conducted over a secure processing server (SSL-protected, with a valid SSL certificate - https://).</p>';
$_['text_checklist_acknowledge_2']          = '<h4>Return policy</h4><p>I confirm my website provides a clear and conspicuous return policy to users.</p>';
$_['text_checklist_acknowledge_3']          = '<h4>Billing terms and conditions</h4><p>I confirm my website provides clear and conspicuous billing terms and conditions.</p>';
$_['text_checklist_acknowledge_4']          = '<h4>Accurate contact information</h4><p>I confirm my website displays sufficient and accurate contact information with <em>at least two</em> of the following: phone number, physical address, email address.</p>';
$_['text_checklist_intro']                  = '<h3>Important</h3> Before you can post ads on Google, your webstore <em>must</em> meet the Google Merchant Center requirements. A full of list of all requirements and recommendations can be found <a href="https://support.google.com/merchants/answer/6363310?hl=en" target="_blank"><strong>here</strong></a>.';
$_['text_clicks']                           = 'Clicks';
$_['text_color']                            = 'Color';
$_['text_condition_new']                    = 'New';
$_['text_condition_refurbished']            = 'Refurbished';
$_['text_condition_used']                   = 'Used';
$_['text_connect_intro']                    = 'Your Google Shopping extension is not yet connected. Please go to the <a href="%s" target="_blank"><strong>Google Shopping for OpenCart website</strong></a> to obtain an App ID and App Secret.';
$_['text_connected']                        = 'Connected with Merchant ID <span class="label label-default">%s</span>';
$_['text_connecting']                       = 'Connecting...';
$_['text_connection']                       = 'Connection';
$_['text_conversion_value']                 = 'Conversion Value';
$_['text_conversions']                      = 'Conversions';
$_['text_cost']                             = 'Cost';
$_['text_critical']                         = 'Critical';
$_['text_cron_email']                       = 'Send Summary to E-Mail';
$_['text_cron_email_status']                = 'Send E-Mail Summary';
$_['text_cron_info']                        = 'Please make sure to set up a CRON task executing <strong>each hour</strong> using one of the methods below. Method #1 is recommended. CRON jobs help you with:<br /><br />&bull; Periodic syncing of your OpenCart catalog with Google Merchant Center<br />&bull; Automatic fetching of product statuses and product reports';
$_['text_cron_settings']                    = 'CRON Settings';
$_['text_data_quality_issues']              = 'Data Quality Issues';
$_['text_debug_log']                        = 'Debug Logging';
$_['text_destination_status']               = 'Status';
$_['text_disabled']                         = 'Disabled';
$_['text_disapproved']                      = 'Disapproved';
$_['text_disconnect_reminder']              = 'Even if you decide to disconnect this OpenCart extension, you will still have access to your Merchant Center account. It will not get deleted. Disconnecting will do the following:<ul><li>Disable this extension</li><li>Remove all active campaigns</li><li>Delete all datafeeds in the linked Google Merchant Center account</li><li>Remove any links between your existing Google Merchant Center account and OpenCart.</li></ul>';
$_['text_disconnecting_please_wait']        = 'Disconnecting...';
$_['text_does_not_apply']                   = '-- Does not apply --';
$_['text_download_debug_log']               = 'Download Debug Log';
$_['text_edit_target']                      = 'Edit Campaign: %s';
$_['text_enabled']                          = 'Enabled';
$_['text_error']                            = 'Error';
$_['text_existing_merchant']                = 'Use my own Google Merchant Center account (In case you want to use your active Google Merchant Center account.)';
$_['text_extension_settings']               = 'Extension Settings';
$_['text_extensions']                       = 'Extensions';
$_['text_filter']                           = 'Filter';
$_['text_gender_female']                    = 'Female';
$_['text_gender_male']                      = 'Male';
$_['text_gender_unisex']                    = 'Unisex';
$_['text_google_expiration_date']           = 'Google Expiration Date';
$_['text_heading_merchant_center_account']  = 'Merchant Center Account';
$_['text_home']                             = 'Home';
$_['text_image']                            = 'Image';
$_['text_impressions']                      = 'Impressions';
$_['text_info_popup_product']               = 'The information requested here is required to properly list your product on Google Shopping. <a href="https://support.google.com/merchants/answer/7052112?hl=en&ref_topic=6324338" target="_blank">Click here</a> for more information.';
$_['text_issues']                           = 'Issues';
$_['text_item_level_issues']                = 'Item Issues';
$_['text_label_active']                     = 'ACTIVE';
$_['text_label_approved']                   = 'APPROVED';
$_['text_label_critical']                   = 'CRITICAL';
$_['text_label_disapproved']                = 'DISAPPROVED';
$_['text_label_error']                      = 'ERROR';
$_['text_label_paused']                     = 'PAUSED';
$_['text_label_pending']                    = 'PENDING';
$_['text_label_suggestion']                 = 'SUGGESTION';
$_['text_label_unassigned']                 = 'UNASSIGNED';
$_['text_label_unavailable']                = 'UNAVAILABLE';
$_['text_learn_more']                       = 'Learn more';
$_['text_loading_please_wait']              = 'Please wait. This may take a few minutes...';
$_['text_local_cron']                       = 'Method #1 - CRON Task:';
$_['text_mapping_intro']                    = 'Select your OpenCart categories which best match the pre-defined Google categories. This helps Google understand what you\'re selling so that they can better connect your ads with search queries from potential customers. If none of your categories match the list below, just click "Proceed" to skip this step.';
$_['text_mapping_verify_intro']             = 'Some of your products are already mapped to Google categories. Should the new mapping edit all current products, or should it apply only for your future products?';
$_['text_mapping_verify_title']             = 'Confirm New Mapping';
$_['text_maximum_five']                     = 'Maximum 5 campaigns can be selected. Leaving a campaign unticked will <strong>unassign</strong> the products from this campaign.';
$_['text_merchant_intro']                   = 'Please select the account you wish to use:';
$_['text_merchant_website_claim']           = '<p>Upon clicking <strong>Proceed</strong>, you will be asked to authorize OpenCart to manage your listing and account in Google Shopping. Your website URL will be claimed by the selected Merchant Center account.</p>';
$_['text_na']                               = '&ndash;';
$_['text_new_merchant']                     = 'Use an account managed by OpenCart (For beginners who do not have a Google Merchant Center account.)';
$_['text_no']                               = 'No';
$_['text_no_results']                       = 'No results found!';
$_['text_no_targets']                       = 'No campaigns found! Click the button below to add your first campaign.';
$_['text_panel_connect']                    = 'Step 1 of 5: Connect the Google Shopping Extension';
$_['text_panel_heading']                    = 'Edit Google Shopping | Store: %s';
$_['text_panel_heading_campaign']           = 'Step 3 of 5: Set up Smart Shopping Ad Campaigns';
$_['text_panel_heading_campaign_2']         = 'Set up Smart Shopping Ad Campaigns';
$_['text_panel_heading_mapping']            = 'Step 5 of 5: Set up Category Mapping';
$_['text_panel_heading_mapping_2']          = 'Set up Category Mapping';
$_['text_panel_heading_merchant']           = 'Step 2 of 5: Set up Google Merchant Center Account';
$_['text_panel_heading_more_info']          = 'About Campaigns';
$_['text_panel_heading_preview']            = 'How an Ad Looks Like';
$_['text_panel_heading_shipping_taxes']     = 'Step 4 of 5: Set up Shipping &amp; Taxes';
$_['text_panel_heading_shipping_taxes_2']   = 'Set up Shipping &amp; Taxes';
$_['text_paused']                           = 'Paused';
$_['text_per_day']                          = '$%s / day';
$_['text_popup_error_body']                 = 'The following error has occurred while trying to fetch this resource: <strong>{error}</strong>';
$_['text_popup_error_title']                = 'Error';
$_['text_popup_loading_body']               = 'Loading... Please wait...';
$_['text_popup_loading_title']              = 'Loading form...';
$_['text_popup_title_multiple']             = 'Editing %s products';
$_['text_popup_title_single']               = '%s (%s)';
$_['text_product_category']                 = 'Category (incl. sub-categories)';
$_['text_product_is_modified']              = 'Google Fields Edited';
$_['text_product_model']                    = 'Model';
$_['text_product_name']                     = 'Product';
$_['text_refresh_token']                    = 'Re-create token';
$_['text_remote_cron']                      = 'Method #2 - Remote CRON:';
$_['text_report_campaign_name']             = 'Campaign Name';
$_['text_report_clicks']                    = 'Clicks';
$_['text_report_conversion_value']          = 'Conversion Value';
$_['text_report_conversions']               = 'Conversions';
$_['text_report_cost']                      = 'Cost';
$_['text_report_date_range']                = 'Campaign Reports for %s';
$_['text_report_impressions']               = 'Impressions';
$_['text_report_status']                    = 'Status';
$_['text_reporting_interval']               = 'Reporting Time Interval';
$_['text_reporting_interval_LAST_14_DAYS']  = 'Last 14 days';
$_['text_reporting_interval_LAST_30_DAYS']  = 'Last 30 days';
$_['text_reporting_interval_LAST_7_DAYS']   = 'Last 7 days';
$_['text_reporting_interval_LAST_BUSINESS_WEEK'] = 'Last business week';
$_['text_reporting_interval_LAST_WEEK']     = 'Last week';
$_['text_reporting_interval_LAST_WEEK_SUN_SAT'] = 'Last week (Sunday - Saturday)';
$_['text_reporting_interval_THIS_MONTH']    = 'This month';
$_['text_reporting_interval_THIS_WEEK_MON_TODAY'] = 'This week (Monday - Today)';
$_['text_reporting_interval_THIS_WEEK_SUN_TODAY'] = 'This week (Sunday - Today)';
$_['text_reporting_interval_TODAY']         = 'Today';
$_['text_reporting_interval_YESTERDAY']     = 'Yesterday';
$_['text_select_country']                   = '-- Country --';
$_['text_select_currency']                  = '-- Currency --';
$_['text_select_language']                  = '-- Language --';
$_['text_selection_all']                    = 'You have selected all <strong>{total}</strong> items on all pages. <a id="deselect_all_pages"><strong>Unselect Everything</strong></a>';
$_['text_selection_page']                   = 'You have selected <strong>{selected_page}</strong> item(s) on this page. <a id="select_all_pages"><strong>Click here</strong></a> to select all <strong>{total}</strong> items on all pages.';
$_['text_shipping_carrier']                 = 'Use a distribution center and carrier services';
$_['text_shipping_custom']                  = 'Set this up myself in the Google Merchant Center';
$_['text_shipping_flat']                    = 'Use a flat rate for all orders';
$_['text_shipping_services']                = 'Shipping Services';
$_['text_shipping_transit_times']           = 'Shipping Transit Times';
$_['text_size']                             = 'Size';
$_['text_size_type_big_and_tall']           = 'Big & Tall';
$_['text_size_type_maternity']              = 'Maternity';
$_['text_size_type_petite']                 = 'Petite';
$_['text_size_type_plus']                   = 'Plus';
$_['text_size_type_regular']                = 'Regular';
$_['text_status']                           = 'Status';
$_['text_suggestion']                       = 'Suggestion';
$_['text_tax_custom']                       = 'Set this up myself in the Google Merchant Center';
$_['text_tax_not_usa']                      = 'I am not based in the USA';
$_['text_tax_on_shipping']                  = 'Add tax on shipping';
$_['text_tax_usa']                          = 'Use Google&apos;s destination-based tax estimation';
$_['text_taxes']                            = 'Taxes (USA only)';
$_['text_tutorial_cron']                    = 'https://isenselabs.com/posts/how-to-auto-sync-opencart-products-with-google-shopping';
$_['text_usd']                              = 'USD';
$_['text_usd_day']                          = 'USD / day';
$_['text_video_tutorial_url_advertise']     = 'https://youtu.be/ZN7zz8raoVM?t=187';
$_['text_video_tutorial_url_install']       = 'https://www.youtube.com/watch?v=AvkBLWAUojI';
$_['text_video_tutorial_url_setup']         = 'https://www.youtube.com/watch?v=ZN7zz8raoVM';
$_['text_view_issues']                      = 'View Issues';
$_['text_yes']                              = 'Yes';

// Placeholders
$_['placeholder_access_token']              = 'Paste your access token here';

// Tabs
$_['tab_text_ads']                          = 'Product Ads / Reports';
$_['tab_text_reports']                      = 'Campaign Reports';
$_['tab_text_settings']                     = 'Settings';

// Buttons
$_['button_add_feed']                       = 'New Feed';
$_['button_add_target']                     = 'New Campaign';
$_['button_apply']                          = 'Assign Selected Products to Campaigns';
$_['button_bulk_edit_google_fields']        = 'Bulk Edit';
$_['button_campaign']                       = 'Smart Shopping Ad Campaigns';
$_['button_close']                          = 'Close';
$_['button_connect']                        = 'Connect';
$_['button_disconnect']                     = 'Disconnect';
$_['button_mapping']                        = 'Category Mapping';
$_['button_proceed']                        = 'Proceed';
$_['button_product_edit']                   = 'Edit Google Fields';
$_['button_product_set']                    = 'Set Google Fields';
$_['button_save']                           = 'Save';
$_['button_save_future']                    = 'Save &amp; Do Nothing';
$_['button_save_modify']                    = 'Save &amp; Modify Current Products';
$_['button_select_campaigns']               = 'Select Campaigns';
$_['button_shipping_taxes']                 = 'Shipping &amp; Taxes';
$_['button_video_tutorial_install']         = 'Watch Video Tutorial';
$_['button_video_tutorial_setup']           = 'Watch Video Tutorial';

// Success
$_['success_advertise_disable']             = 'Success! Advertising has been disabled for the selected products!';
$_['success_advertise_enable']              = 'Success! Advertising has been enabled for the selected products!';
$_['success_advertise_listed']              = 'The shopping ads are live! If your products are not yet approved, please allow up to 3 business days for the Merchant Center team to review them.';
$_['success_advertise_unlisted']            = 'The shopping ads have been stopped. The products you edited no longer belong to any campaigns.';
$_['success_campaign']                      = 'Success! You have set up Smart Shopping Ad Campaigns!';
$_['success_connect']                       = 'Success! You have connected your extension!';
$_['success_disconnect']                    = 'Success! The extension has been disconnected!';
$_['success_index']                         = 'Success! You have modified the extension!';
$_['success_mapping']                       = 'Success! You have set up mapping!';
$_['success_merchant']                      = 'Success! Your Google Merchant Center account has been set up! Your website has been claimed!';
$_['success_merchant_access']           = 'Success! Your Google Merchant Center account has been set up! Your website has been claimed! You can access the linked Google Merchant Center account from the <a href="https://merchants.google.com/mc/merchantdashboard?a=%s" target="_blank">Google Merchant Center dashboard</a>.';
$_['success_product']                       = 'Success! The product information has been updated.';
$_['success_shipping_taxes']                = 'Success! The merchant shipping and taxes have been updated.';
$_['success_target_add']                    = 'Success! Your new campaign has been created! It will become active as soon as your product feeds get approved by Google Merchant Center.';
$_['success_target_delete']                 = 'Success! Your campaign has been deleted!';
$_['success_target_edit']                   = 'Success! You have edited your campaign!';

// Error
$_['error_adblock']                         = "It looks like you are using an ad blocker. In order to use GoogleShopping, please disable your ad blocker for your OpenCart admin panel.";
$_['error_budget']                          = 'Please insert the campaign budget. The value must be numeric and no less than 5.';
$_['error_campaign_name_in_use']            = 'You are already using a campaign with the same name! Please choose another name.';
$_['error_campaign_name_total']             = '&quot;Total&quot; is a forbidden name for a campaign! Please choose another name.';
$_['error_carrier']                         = 'Please select at least one carrier!';
$_['error_carrier_postcode']                = 'Please provide the postcode for outgoing shipments.';
$_['error_carrier_price_percentage']        = 'Please provide a valid price percentage from 0 to 100.';
$_['error_cron_acknowledge']                = 'Please confirm you have set up a CRON job.';
$_['error_empty_app_id']                    = 'Please insert the App ID!';
$_['error_empty_app_secret']                = 'Please insert the App Secret!';
$_['error_empty_campaign_name']             = 'Please set a name for your campaign!';
$_['error_empty_country']                   = 'Please select a country!';
$_['error_empty_feed']                      = 'Please specify at least one campaign feed!';
$_['error_field_no_value']                  = 'Please provide a value!';
$_['error_flat_rate']                       = 'Please insert a flat rate value. The value must be numeric.';
$_['error_form']                            = 'Please check the form for errors and try to save agian.';
$_['error_invalid_email']                   = 'The provided e-mail address is not valid!';
$_['error_invalid_feed']                    = 'All feeds must have a language and currency!';
$_['error_max_transit_time']                = 'Please insert a maximum transit time (number of days) which is larger than the minimum.';
$_['error_min_transit_time']                = 'Please insert a minimum transit time (number of days).';
$_['error_no_targets']                      = 'Warning! No Smart Shopping Ad Campaigns have been set up.';
$_['error_permission']                      = 'Warning! You do not have permission to modify the extension Advertise &gt; Google Shopping!';
$_['error_popup_not_found_body']            = 'The system could not find the product.';
$_['error_popup_not_found_title']           = 'Not found';
$_['error_store_url_claim']                 = 'Your store URL has been claimed by another app. Please connect your merchant account to re-claim the store URL.';
$_['error_tax']                             = 'Please select at least one taxable state.';
$_['error_used_app_id']                     = 'You have already connected this App with another one of your stores. Please disconnect the other store, or use a different App ID.';
$_['error_warning']                         = 'Warning! Please check the form carefully for errors.';

// Warning
$_['warning_budget']                        = 'Campaigns with a daily budget of less than $10 may not yield good conversion results. For best results, we suggest a daily budget of at least $10.';
$_['warning_disabled']                      = 'The extension is disabled and all of your campaigns are stopped. Enable the extension to activate the Smart Shopping Ad Campaigns.';
$_['warning_last_cron_executed']            = 'It seems like your CRON task has not been run recently. Please ensure it is set up correctly. Follow <a href="%s" target="_blank"><strong>this tutorial</strong></a> to see how to do it.';
$_['warning_no_active_campaigns']           = 'You have no campaigns running. <a href="%s"><strong>Click here</strong></a> to activate your campaign.';
$_['warning_no_advertised_products']        = 'No products are being advertised. To start advertising, you must assign products to campaigns. Follow <a href="%s" target="_blank"><strong>this tutorial</strong></a> to see how to do it.';
$_['warning_roas']                          = 'Google Ads needs about a couple of weeks after a campaign gets created to work properly with ROAS. Please check back on %s to configure this setting.';
