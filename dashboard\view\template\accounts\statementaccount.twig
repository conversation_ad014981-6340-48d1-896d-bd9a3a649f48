{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Statement Account -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --statement-color: #9b59b6;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.statement-account-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.statement-account-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--statement-color), var(--primary-color), var(--secondary-color));
}

.statement-account-header {
    text-align: center;
    border-bottom: 3px solid var(--statement-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.statement-account-header h2 {
    color: var(--statement-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.range-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.range-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.range-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.range-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.range-card.account-start::before { background: var(--info-color); }
.range-card.account-end::before { background: var(--warning-color); }
.range-card.date-start::before { background: var(--success-color); }
.range-card.date-end::before { background: var(--statement-color); }

.range-card h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.range-card .form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 10px 12px;
    transition: var(--transition);
    width: 100%;
    font-size: 1rem;
}

.range-card .form-control:focus {
    border-color: var(--statement-color);
    box-shadow: 0 0 0 0.2rem rgba(155, 89, 182, 0.25);
    outline: none;
}

.statement-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.statement-table th {
    background: linear-gradient(135deg, var(--statement-color), #7d3c98);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.statement-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.statement-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.statement-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-debit { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-credit { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-balance { 
    color: var(--statement-color); 
    font-weight: 600;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.filter-panel h4 {
    color: var(--statement-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    transition: var(--transition);
    width: 100%;
}

.form-control:focus {
    border-color: var(--statement-color);
    box-shadow: 0 0 0 0.2rem rgba(155, 89, 182, 0.25);
    outline: none;
}

/* RTL Support */
[dir="rtl"] .statement-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .statement-account-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .statement-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .panel-heading, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .statement-table {
        font-size: 0.8rem;
    }
    
    .statement-table th,
    .statement-table td {
        padding: 8px 6px;
    }
    
    .range-selection {
        grid-template-columns: 1fr;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateRangeStatement()"
                  data-toggle="tooltip" title="{{ button_generate_statement }}">
            <i class="fa fa-file-text"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fa fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportRangeStatement('excel')">
                <i class="fa fa-file-excel text-success"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportRangeStatement('pdf')">
                <i class="fa fa-file-pdf text-danger"></i> PDF
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printRangeStatement()">
                <i class="fa fa-print text-muted"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-primary" onclick="emailRangeStatement()"
                  data-toggle="tooltip" title="{{ button_email }}">
            <i class="fa fa-envelope"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i>
      {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- Range Selection Panel -->
    <div class="statement-account-container">
      <div class="statement-account-header">
        <h2>{{ text_range_statement }}</h2>
        <p>{{ text_range_statement_description }}</p>
      </div>

      <form id="range-statement-form" method="post" action="{{ action }}">
        <div class="range-selection">
          <div class="range-card account-start">
            <h4>{{ text_account_start }}</h4>
            <select name="account_start" id="account_start" class="form-control" required>
              <option value="">{{ text_select_account_start }}</option>
              {% for account in accounts %}
              <option value="{{ account.account_id }}"{% if account.account_id == account_start %} selected{% endif %}>
                {{ account.code }} - {{ account.name }}
              </option>
              {% endfor %}
            </select>
          </div>
          
          <div class="range-card account-end">
            <h4>{{ text_account_end }}</h4>
            <select name="account_end" id="account_end" class="form-control" required>
              <option value="">{{ text_select_account_end }}</option>
              {% for account in accounts %}
              <option value="{{ account.account_id }}"{% if account.account_id == account_end %} selected{% endif %}>
                {{ account.code }} - {{ account.name }}
              </option>
              {% endfor %}
            </select>
          </div>
          
          <div class="range-card date-start">
            <h4>{{ text_date_start }}</h4>
            <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
          </div>
          
          <div class="range-card date-end">
            <h4>{{ text_date_end }}</h4>
            <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
          </div>
        </div>

        <div class="text-center">
          <button type="submit" class="btn btn-primary btn-lg">
            <i class="fa fa-search"></i> {{ button_generate_statement }}
          </button>
        </div>
      </form>
    </div>

    <!-- Range Statement Results -->
    {% if statement_data %}
    <div class="statement-account-container">
      <div class="statement-account-header">
        <h2>{{ text_range_statement_results }}</h2>
        <p>{{ text_accounts_range }}: {{ account_start_name }} {{ text_to }} {{ account_end_name }}</p>
        <p>{{ text_period }}: {{ date_start }} {{ text_to }} {{ date_end }}</p>
      </div>

      <!-- Range Statement Table -->
      <div class="table-responsive">
        <table class="statement-table" id="range-statement-table">
          <thead>
            <tr>
              <th>{{ column_account_code }}</th>
              <th>{{ column_account_name }}</th>
              <th>{{ column_opening_balance }}</th>
              <th>{{ column_debit }}</th>
              <th>{{ column_credit }}</th>
              <th>{{ column_closing_balance }}</th>
              <th>{{ column_net_movement }}</th>
            </tr>
          </thead>
          <tbody>
            {% for account in statement_data %}
            <tr>
              <td>{{ account.code }}</td>
              <td>{{ account.name }}</td>
              <td class="amount-cell amount-balance">{{ account.opening_balance_formatted }}</td>
              <td class="amount-cell amount-debit">{{ account.total_debit_formatted }}</td>
              <td class="amount-cell amount-credit">{{ account.total_credit_formatted }}</td>
              <td class="amount-cell amount-balance">{{ account.closing_balance_formatted }}</td>
              <td class="amount-cell {% if account.net_movement > 0 %}amount-credit{% elseif account.net_movement < 0 %}amount-debit{% else %}amount-balance{% endif %}">
                {{ account.net_movement_formatted }}
              </td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr style="background: #f8f9fa; font-weight: bold;">
              <td colspan="2"><strong>{{ text_totals }}</strong></td>
              <td class="amount-cell amount-balance">{{ total_opening_balance_formatted }}</td>
              <td class="amount-cell amount-debit">{{ total_debit_formatted }}</td>
              <td class="amount-cell amount-credit">{{ total_credit_formatted }}</td>
              <td class="amount-cell amount-balance">{{ total_closing_balance_formatted }}</td>
              <td class="amount-cell amount-balance">{{ total_net_movement_formatted }}</td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fa fa-info-circle"></i>
      {{ text_no_statement }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Statement Account
class StatementAccountManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeFormValidation();
        this.initializeAccountSelection();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeDataTable() {
        const table = document.getElementById('range-statement-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 50,
                order: [[0, 'asc']], // Sort by account code asc
                columnDefs: [
                    { targets: [2, 3, 4, 5, 6], className: 'text-end' }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateRangeStatement();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printRangeStatement();
                        break;
                    case 'm':
                        e.preventDefault();
                        this.emailRangeStatement();
                        break;
                }
            }
        });
    }

    initializeFormValidation() {
        const form = document.getElementById('range-statement-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.validateAndSubmitForm();
            });
        }
    }

    initializeAccountSelection() {
        const accountStart = document.getElementById('account_start');
        const accountEnd = document.getElementById('account_end');

        if (accountStart && accountEnd) {
            accountStart.addEventListener('change', () => {
                this.updateAccountEndOptions();
            });
        }
    }

    updateAccountEndOptions() {
        const accountStart = document.getElementById('account_start');
        const accountEnd = document.getElementById('account_end');

        if (accountStart.value) {
            const startIndex = accountStart.selectedIndex;

            // Enable all options in account_end that come after account_start
            Array.from(accountEnd.options).forEach((option, index) => {
                if (index > 0) { // Skip the first empty option
                    option.disabled = index <= startIndex;
                }
            });
        } else {
            // Enable all options if no start account is selected
            Array.from(accountEnd.options).forEach(option => {
                option.disabled = false;
            });
        }
    }

    validateAndSubmitForm() {
        const form = document.getElementById('range-statement-form');
        const formData = new FormData(form);

        // Validate account range
        if (!formData.get('account_start')) {
            this.showAlert('{{ error_account_start }}', 'danger');
            return;
        }

        if (!formData.get('account_end')) {
            this.showAlert('{{ error_account_end }}', 'danger');
            return;
        }

        // Validate date range
        const startDate = new Date(formData.get('date_start'));
        const endDate = new Date(formData.get('date_end'));

        if (startDate >= endDate) {
            this.showAlert('{{ error_invalid_date_range }}', 'danger');
            return;
        }

        // Submit form
        this.showLoadingState(true);
        form.submit();
    }

    generateRangeStatement() {
        const form = document.getElementById('range-statement-form');
        if (form) {
            this.validateAndSubmitForm();
        }
    }

    exportRangeStatement(format) {
        const accountStart = document.getElementById('account_start').value;
        const accountEnd = document.getElementById('account_end').value;
        const dateStart = document.getElementById('date_start').value;
        const dateEnd = document.getElementById('date_end').value;

        if (!accountStart || !accountEnd || !dateStart || !dateEnd) {
            this.showAlert('{{ error_complete_form }}', 'danger');
            return;
        }

        const params = new URLSearchParams({
            account_start: accountStart,
            account_end: accountEnd,
            date_start: dateStart,
            date_end: dateEnd,
            format: format
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ url_link('accounts/statementaccount', 'export') }}&' + params.toString(), '_blank');
    }

    printRangeStatement() {
        window.print();
    }

    emailRangeStatement() {
        const accountStart = document.getElementById('account_start').value;
        const accountEnd = document.getElementById('account_end').value;
        const dateStart = document.getElementById('date_start').value;
        const dateEnd = document.getElementById('date_end').value;

        if (!accountStart || !accountEnd || !dateStart || !dateEnd) {
            this.showAlert('{{ error_complete_form }}', 'danger');
            return;
        }

        this.showAlert('{{ text_processing }}...', 'info');

        fetch('{{ url_link('accounts/statementaccount', 'email') }}', {
            method: 'POST',
            body: JSON.stringify({
                account_start: accountStart,
                account_end: accountEnd,
                date_start: dateStart,
                date_end: dateEnd
            }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert('{{ success_email }}', 'success');
            } else {
                this.showAlert(data.error || '{{ error_export }}', 'danger');
            }
        })
        .catch(error => {
            this.showAlert('{{ error_export }}: ' + error.message, 'danger');
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
                if (btn.querySelector('i')) {
                    btn.querySelector('i').className = 'fa fa-spinner fa-spin';
                }
            } else {
                btn.disabled = false;
                location.reload();
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateRangeStatement() {
    statementAccountManager.generateRangeStatement();
}

function exportRangeStatement(format) {
    statementAccountManager.exportRangeStatement(format);
}

function printRangeStatement() {
    statementAccountManager.printRangeStatement();
}

function emailRangeStatement() {
    statementAccountManager.emailRangeStatement();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.statementAccountManager = new StatementAccountManager();
});
</script>

{{ footer }}
