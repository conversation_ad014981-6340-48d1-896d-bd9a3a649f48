<?php
/**
 * سجلات النظام - AYM ERP
 * System Logs Model
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    2.0.0
 */

class ModelLoggingSystemLogs extends Model {
    
    /**
     * تسجيل حدث في النظام
     */
    public function logSystemEvent($level, $message, $context = array()) {
        $sql = "INSERT INTO " . DB_PREFIX . "system_log SET
                level = '" . $this->db->escape($level) . "',
                message = '" . $this->db->escape($message) . "',
                context = '" . $this->db->escape(json_encode($context)) . "',
                user_id = '" . (int)($this->user->getId() ?? 0) . "',
                ip_address = '" . $this->db->escape($this->request->server['REMOTE_ADDR'] ?? '') . "',
                user_agent = '" . $this->db->escape($this->request->server['HTTP_USER_AGENT'] ?? '') . "',
                created_at = NOW()";
        
        $this->db->query($sql);
        
        return $this->db->getLastId();
    }
    
    /**
     * تسجيل خطأ
     */
    public function logError($message, $context = array()) {
        return $this->logSystemEvent('error', $message, $context);
    }
    
    /**
     * تسجيل تحذير
     */
    public function logWarning($message, $context = array()) {
        return $this->logSystemEvent('warning', $message, $context);
    }
    
    /**
     * تسجيل معلومات
     */
    public function logInfo($message, $context = array()) {
        return $this->logSystemEvent('info', $message, $context);
    }
    
    /**
     * تسجيل تصحيح
     */
    public function logDebug($message, $context = array()) {
        return $this->logSystemEvent('debug', $message, $context);
    }
    
    /**
     * الحصول على سجلات النظام
     */
    public function getSystemLogs($filters = array(), $limit = 100, $offset = 0) {
        $sql = "SELECT sl.*, u.firstname, u.lastname
                FROM " . DB_PREFIX . "system_log sl
                LEFT JOIN " . DB_PREFIX . "user u ON (sl.user_id = u.user_id)
                WHERE 1=1";
        
        if (!empty($filters['level'])) {
            $sql .= " AND sl.level = '" . $this->db->escape($filters['level']) . "'";
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND DATE(sl.created_at) >= '" . $this->db->escape($filters['date_from']) . "'";
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND DATE(sl.created_at) <= '" . $this->db->escape($filters['date_to']) . "'";
        }
        
        if (!empty($filters['search'])) {
            $sql .= " AND sl.message LIKE '%" . $this->db->escape($filters['search']) . "%'";
        }
        
        $sql .= " ORDER BY sl.created_at DESC";
        
        if ($limit > 0) {
            $sql .= " LIMIT " . (int)$offset . ", " . (int)$limit;
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * تنظيف السجلات القديمة
     */
    public function cleanupOldLogs($days = 30) {
        $sql = "DELETE FROM " . DB_PREFIX . "system_log 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)";
        
        $this->db->query($sql);
        
        return $this->db->countAffected();
    }
    
    /**
     * الحصول على إحصائيات السجلات
     */
    public function getLogStatistics() {
        $stats = array();
        
        // إجمالي السجلات
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "system_log";
        $query = $this->db->query($sql);
        $stats['total_logs'] = $query->row['total'];
        
        // الأخطاء اليوم
        $sql = "SELECT COUNT(*) as errors FROM " . DB_PREFIX . "system_log 
                WHERE level = 'error' AND DATE(created_at) = CURDATE()";
        $query = $this->db->query($sql);
        $stats['errors_today'] = $query->row['errors'];
        
        // التحذيرات اليوم
        $sql = "SELECT COUNT(*) as warnings FROM " . DB_PREFIX . "system_log 
                WHERE level = 'warning' AND DATE(created_at) = CURDATE()";
        $query = $this->db->query($sql);
        $stats['warnings_today'] = $query->row['warnings'];
        
        return $stats;
    }
}
?>
