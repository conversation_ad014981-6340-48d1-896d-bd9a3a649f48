<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/longrunning/operations.proto

namespace GPBMetadata\Google\Longrunning;

class Operations
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        \GPBMetadata\Google\Protobuf\GPBEmpty::initOnce();
        \GPBMetadata\Google\Rpc\Status::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0afd0a0a23676f6f676c652f6c6f6e6772756e6e696e672f6f7065726174" .
            "696f6e732e70726f746f1212676f6f676c652e6c6f6e6772756e6e696e67" .
            "1a19676f6f676c652f70726f746f6275662f616e792e70726f746f1a2067" .
            "6f6f676c652f70726f746f6275662f64657363726970746f722e70726f74" .
            "6f1a1b676f6f676c652f70726f746f6275662f656d7074792e70726f746f" .
            "1a17676f6f676c652f7270632f7374617475732e70726f746f22a8010a09" .
            "4f7065726174696f6e120c0a046e616d6518012001280912260a086d6574" .
            "616461746118022001280b32142e676f6f676c652e70726f746f6275662e" .
            "416e79120c0a04646f6e6518032001280812230a056572726f7218042001" .
            "280b32122e676f6f676c652e7270632e537461747573480012280a087265" .
            "73706f6e736518052001280b32142e676f6f676c652e70726f746f627566" .
            "2e416e79480042080a06726573756c7422230a134765744f706572617469" .
            "6f6e52657175657374120c0a046e616d65180120012809225c0a154c6973" .
            "744f7065726174696f6e7352657175657374120c0a046e616d6518042001" .
            "2809120e0a0666696c74657218012001280912110a09706167655f73697a" .
            "6518022001280512120a0a706167655f746f6b656e18032001280922640a" .
            "164c6973744f7065726174696f6e73526573706f6e736512310a0a6f7065" .
            "726174696f6e7318012003280b321d2e676f6f676c652e6c6f6e6772756e" .
            "6e696e672e4f7065726174696f6e12170a0f6e6578745f706167655f746f" .
            "6b656e18022001280922260a1643616e63656c4f7065726174696f6e5265" .
            "7175657374120c0a046e616d6518012001280922260a1644656c6574654f" .
            "7065726174696f6e52657175657374120c0a046e616d6518012001280922" .
            "3d0a0d4f7065726174696f6e496e666f12150a0d726573706f6e73655f74" .
            "79706518012001280912150a0d6d657461646174615f7479706518022001" .
            "2809328c040a0a4f7065726174696f6e731286010a0e4c6973744f706572" .
            "6174696f6e7312292e676f6f676c652e6c6f6e6772756e6e696e672e4c69" .
            "73744f7065726174696f6e73526571756573741a2a2e676f6f676c652e6c" .
            "6f6e6772756e6e696e672e4c6973744f7065726174696f6e73526573706f" .
            "6e7365221d82d3e493021712152f76312f7b6e616d653d6f706572617469" .
            "6f6e737d12780a0c4765744f7065726174696f6e12272e676f6f676c652e" .
            "6c6f6e6772756e6e696e672e4765744f7065726174696f6e526571756573" .
            "741a1d2e676f6f676c652e6c6f6e6772756e6e696e672e4f706572617469" .
            "6f6e222082d3e493021a12182f76312f7b6e616d653d6f7065726174696f" .
            "6e732f2a2a7d12770a0f44656c6574654f7065726174696f6e122a2e676f" .
            "6f676c652e6c6f6e6772756e6e696e672e44656c6574654f706572617469" .
            "6f6e526571756573741a162e676f6f676c652e70726f746f6275662e456d" .
            "707479222082d3e493021a2a182f76312f7b6e616d653d6f706572617469" .
            "6f6e732f2a2a7d1281010a0f43616e63656c4f7065726174696f6e122a2e" .
            "676f6f676c652e6c6f6e6772756e6e696e672e43616e63656c4f70657261" .
            "74696f6e526571756573741a162e676f6f676c652e70726f746f6275662e" .
            "456d707479222a82d3e4930224221f2f76312f7b6e616d653d6f70657261" .
            "74696f6e732f2a2a7d3a63616e63656c3a012a4294010a16636f6d2e676f" .
            "6f676c652e6c6f6e6772756e6e696e67420f4f7065726174696f6e735072" .
            "6f746f50015a3d676f6f676c652e676f6c616e672e6f72672f67656e7072" .
            "6f746f2f676f6f676c65617069732f6c6f6e6772756e6e696e673b6c6f6e" .
            "6772756e6e696e67aa0212476f6f676c652e4c6f6e6752756e6e696e67ca" .
            "0212476f6f676c655c4c6f6e6752756e6e696e67620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

