<?php
class ModelAccountsCashFlow extends Model {
    /**
     * جلب القيود النقدية خلال الفترة
     * سنفترض أننا نستخدم الطريقة المباشرة: أي نستخرج مباشرةً المقبوض والمدفوع الفعلي
     */
    public function getCashFlowData($date_start, $date_end) {
        // 1) حدد الحسابات النقدية (cash & bank) من الدليل
        //    هنا افتراض: 1281, 1282, 1283
        $cashAccounts = ['1281','1282','1283'];  // يمكنك سحبها ديناميكيا من الـ DB

        // 2) إحضار القيود من journal_entries التي أثرت في هذه الحسابات خلال الفترة
        //    نجلب الطرف المقابل (account_code_other) ونحدد هل الحركة مدينة أو دائنة
        //    هذا يتطلب أن تكون لديك آلية لحفظ الطرف/الأطراف الأخرى للقيد الواحد
        //    إذا كان لديك جدول detailed journal entries يجب ربطه بحيث:
        //      - إن كان je.account_code in $cashAccounts => الطرف المقابل يحصل عليه
        //    (هذه تفاصيل بنية الـ DB وقد تختلف حسب تصميمك)

        // مثال SQL تقريبي:
        $query = $this->db->query("
            SELECT 
               je.journal_id,
               je.account_code, 
               je.is_debit, 
               je.amount,
               j.thedate,
               j.is_cancelled,
               a2.account_code AS account_code_other,
               ad2.name AS name_other
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "journal_entries je2 ON (je2.journal_id = j.journal_id AND je2.account_code != je.account_code)
            LEFT JOIN " . DB_PREFIX . "accounts a2 ON (a2.account_code = je2.account_code)
            LEFT JOIN " . DB_PREFIX . "account_description ad2 ON (ad2.account_id = a2.account_id 
                  AND ad2.language_id = '" . (int)$this->config->get('config_language_id') . "')
            WHERE je.account_code IN ('1281','1282','1283') 
              AND j.thedate BETWEEN '" . $this->db->escape($date_start) . "' 
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
        ");

        $rawRows = $query->rows;

        // 3) صنف الحركة حسب النشاط (تشغيلي/استثماري/تمويلي) بناءً على "account_code_other"
        //    وسنستخدم خريطة:
        //    مثلاً:
        $operating = [];
        $investing = [];
        $financing = [];
        // لجمع المبالغ
        $totalOp = 0;
        $totalInv = 0;
        $totalFin = 0;

        foreach ($rawRows as $r) {
            $amount = (float)$r['amount'];
            // إن كان je.is_debit=1 => النقدية زادت
            // إن كان je.is_debit=0 => النقدية نقصت
            // لتحويلها إلى موجب/سالب:
            if (!$r['is_debit']) {
                $amount = -$amount;
            }

            $otherCode = $r['account_code_other'] ?: '';
            $otherName = $r['name_other'] ?: '...';

            // صنف حسب خريطة دليل الحسابات
            $activityType = $this->getActivityType($otherCode); 
            // getActivityType => دالة تحدد إن كان تشغيلي/استثماري/تمويلي

            switch ($activityType) {
                case 'operating':
                    $operating[] = [
                        'date' => $r['thedate'],
                        'other_code' => $otherCode,
                        'other_name' => $otherName,
                        'amount' => $amount
                    ];
                    $totalOp += $amount;
                    break;
                case 'investing':
                    $investing[] = [
                        'date' => $r['thedate'],
                        'other_code' => $otherCode,
                        'other_name' => $otherName,
                        'amount' => $amount
                    ];
                    $totalInv += $amount;
                    break;
                case 'financing':
                    $financing[] = [
                        'date' => $r['thedate'],
                        'other_code' => $otherCode,
                        'other_name' => $otherName,
                        'amount' => $amount
                    ];
                    $totalFin += $amount;
                    break;
                default:
                    // لو عندك حالة أخرى
                    break;
            }
        }

        // مجموع
        $netChange = $totalOp + $totalInv + $totalFin;

        return [
            'operating' => $operating,
            'investing' => $investing,
            'financing' => $financing,
            'total_operating' => $totalOp,
            'total_investing' => $totalInv,
            'total_financing' => $totalFin,
            'net_change' => $netChange
        ];
    }


    /**
     *  تحديد نوع النشاط بناءً على account_code من الدليل المرفق
     */
    private function getActivityType($accountCode) {
        // تشغيلي:
        //   - الإيرادات (5...) لكن لاحظ في دليلك أحيانًا 5 هي حساب رئيسي والدخل التفصيلي 51,52,54...
        //   - التكاليف والمصروفات (4...)  
        //   - المخزون (122...)، العملاء (123...), الموردين (321...) => في الواقع تُصنَّف تشغيلي.
        // استثماري:
        //   - الأصول الثابتة (111...), الاستثمارات غير المتداولة (114...), إلخ
        // تمويلي:
        //   - رأس المال (21..), الاحتياطيات (23..), القروض (311, 326..)
        // هذه مجرد أمثلة، تحتاج ضبط أدق.

        // مثال بسيط:
        if (preg_match('/^(4|5|12|123|321)/', $accountCode)) {
            return 'operating';
        } elseif (preg_match('/^(111|114)/', $accountCode)) {
            return 'investing';
        } elseif (preg_match('/^(21|23|24|25|31|32|3)/', $accountCode)) {
            return 'financing';
        }
        return 'operating'; // افتراضي
    }

    /**
     * إحضار رصيد النقدية أول الفترة
     */
    public function getOpeningCashBalance($date_start) {
        // نجلب أرصدة الحسابات النقدية حتى اليوم السابق للـ $date_start
        // الحسابات النقدية: 1281,1282,1283
        $cashAccounts = ['1281','1282','1283'];

        // نفترض عندك جدول balances أو طريقة لجلب الرصيد التراكمي
        // أو يمكنك حسابه من اليومية من بداية النظام حتى اليوم السابق
        // هنا نموذج مبسط:
        $sql = "
          SELECT COALESCE(SUM(CASE WHEN je.is_debit=1 THEN je.amount ELSE -je.amount END),0) as balance
          FROM " . DB_PREFIX . "journal_entries je
          LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
          WHERE je.account_code IN ('1281','1282','1283')
            AND j.thedate < '" . $this->db->escape($date_start) . "'
            AND j.is_cancelled=0
        ";
        $q = $this->db->query($sql);
        return (float)$q->row['balance'];
    }

    /**
     * تحليل التدفق النقدي المتقدم بالذكاء الاصطناعي
     */
    public function getAdvancedCashFlowAnalysis($date_start, $date_end) {
        $cash_flow_data = $this->getCashFlowData($date_start, $date_end);

        return array(
            'basic_data' => $cash_flow_data,
            'trend_analysis' => $this->analyzeCashFlowTrends($date_start, $date_end),
            'seasonal_patterns' => $this->identifySeasonalPatterns($date_start, $date_end),
            'liquidity_ratios' => $this->calculateLiquidityRatios($date_start, $date_end),
            'cash_conversion_cycle' => $this->calculateCashConversionCycle($date_start, $date_end),
            'forecasting' => $this->forecastCashFlow($date_start, $date_end),
            'risk_assessment' => $this->assessCashFlowRisks($cash_flow_data),
            'recommendations' => $this->generateRecommendations($cash_flow_data)
        );
    }

    /**
     * تحليل اتجاهات التدفق النقدي
     */
    private function analyzeCashFlowTrends($date_start, $date_end) {
        // تحليل الاتجاهات الشهرية
        $monthly_trends = $this->db->query("
            SELECT
                YEAR(j.thedate) as year,
                MONTH(j.thedate) as month,
                SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as cash_inflow,
                SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as cash_outflow,
                SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) as net_cash_flow
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            WHERE je.account_code IN ('1281','1282','1283')
              AND j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
            GROUP BY YEAR(j.thedate), MONTH(j.thedate)
            ORDER BY year, month
        ");

        $trends = array();
        $previous_net = 0;

        foreach ($monthly_trends->rows as $row) {
            $current_net = $row['net_cash_flow'];
            $growth_rate = $previous_net != 0 ? (($current_net - $previous_net) / abs($previous_net)) * 100 : 0;

            $trends[] = array(
                'period' => $row['year'] . '-' . sprintf('%02d', $row['month']),
                'cash_inflow' => $row['cash_inflow'],
                'cash_outflow' => $row['cash_outflow'],
                'net_cash_flow' => $current_net,
                'growth_rate' => round($growth_rate, 2)
            );

            $previous_net = $current_net;
        }

        return $trends;
    }

    /**
     * تحديد الأنماط الموسمية
     */
    private function identifySeasonalPatterns($date_start, $date_end) {
        $seasonal_data = $this->db->query("
            SELECT
                MONTH(j.thedate) as month,
                AVG(CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) as avg_net_flow,
                STDDEV(CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) as volatility
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            WHERE je.account_code IN ('1281','1282','1283')
              AND j.thedate BETWEEN DATE_SUB('" . $this->db->escape($date_end) . "', INTERVAL 3 YEAR)
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
            GROUP BY MONTH(j.thedate)
            ORDER BY month
        ");

        $patterns = array();
        foreach ($seasonal_data->rows as $row) {
            $patterns[] = array(
                'month' => $row['month'],
                'month_name' => date('F', mktime(0, 0, 0, $row['month'], 1)),
                'avg_net_flow' => round($row['avg_net_flow'], 2),
                'volatility' => round($row['volatility'], 2),
                'risk_level' => $row['volatility'] > 10000 ? 'high' : ($row['volatility'] > 5000 ? 'medium' : 'low')
            );
        }

        return $patterns;
    }

    /**
     * حساب نسب السيولة
     */
    private function calculateLiquidityRatios($date_start, $date_end) {
        // الحصول على الأصول المتداولة والخصوم المتداولة
        // حساب الأصول المتداولة ديناميكياً
        $current_assets = $this->db->query("
            SELECT COALESCE(SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END), 0) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code LIKE '1%'
              AND a.account_code NOT LIKE '15%'
              AND j.status = 'posted'
              AND j.thedate <= '" . $this->db->escape($date_end) . "'
        ");

        // حساب الخصوم المتداولة ديناميكياً
        $current_liabilities = $this->db->query("
            SELECT COALESCE(SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE -je.amount END), 0) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code LIKE '21%'
              AND j.status = 'posted'
              AND j.thedate <= '" . $this->db->escape($date_end) . "'
        ");

        // حساب النقدية وما في حكمها ديناميكياً
        $cash_and_equivalents = $this->db->query("
            SELECT COALESCE(SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END), 0) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code IN ('1281','1282','1283')
              AND j.status = 'posted'
              AND j.thedate <= '" . $this->db->escape($date_end) . "'
        ");

        $current_assets_total = $current_assets->row['total'] ?? 0;
        $current_liabilities_total = $current_liabilities->row['total'] ?? 0;
        $cash_total = $cash_and_equivalents->row['total'] ?? 0;

        return array(
            'current_ratio' => $current_liabilities_total != 0 ? round($current_assets_total / $current_liabilities_total, 2) : 0,
            'quick_ratio' => $current_liabilities_total != 0 ? round(($current_assets_total - $cash_total) / $current_liabilities_total, 2) : 0,
            'cash_ratio' => $current_liabilities_total != 0 ? round($cash_total / $current_liabilities_total, 2) : 0,
            'cash_coverage_days' => $this->calculateCashCoverageDays($date_start, $date_end)
        );
    }

    /**
     * حساب دورة تحويل النقد
     */
    private function calculateCashConversionCycle($date_start, $date_end) {
        // حساب متوسط فترة التحصيل
        $avg_collection_period = $this->calculateAverageCollectionPeriod($date_start, $date_end);

        // حساب متوسط فترة المخزون
        $avg_inventory_period = $this->calculateAverageInventoryPeriod($date_start, $date_end);

        // حساب متوسط فترة السداد
        $avg_payment_period = $this->calculateAveragePaymentPeriod($date_start, $date_end);

        $cash_conversion_cycle = $avg_collection_period + $avg_inventory_period - $avg_payment_period;

        return array(
            'average_collection_period' => $avg_collection_period,
            'average_inventory_period' => $avg_inventory_period,
            'average_payment_period' => $avg_payment_period,
            'cash_conversion_cycle' => $cash_conversion_cycle,
            'efficiency_rating' => $cash_conversion_cycle < 30 ? 'excellent' : ($cash_conversion_cycle < 60 ? 'good' : 'needs_improvement')
        );
    }

    /**
     * توقع التدفق النقدي
     */
    private function forecastCashFlow($date_start, $date_end) {
        // استخدام البيانات التاريخية للتنبؤ
        $historical_data = $this->analyzeCashFlowTrends($date_start, $date_end);

        $forecast = array();
        $periods = 6; // توقع لـ 6 أشهر قادمة

        for ($i = 1; $i <= $periods; $i++) {
            $forecast_date = date('Y-m', strtotime($date_end . ' +' . $i . ' months'));

            // حساب متوسط النمو
            $avg_growth = $this->calculateAverageGrowthRate($historical_data);

            // آخر قيمة معروفة
            $last_value = end($historical_data)['net_cash_flow'] ?? 0;

            // التنبؤ بناءً على الاتجاه
            $forecasted_value = $last_value * (1 + ($avg_growth / 100));

            $forecast[] = array(
                'period' => $forecast_date,
                'forecasted_net_flow' => round($forecasted_value, 2),
                'confidence_level' => $this->calculateConfidenceLevel($historical_data),
                'scenario' => 'base_case'
            );
        }

        return $forecast;
    }

    /**
     * تقييم مخاطر التدفق النقدي
     */
    private function assessCashFlowRisks($cash_flow_data) {
        $risks = array();

        // فحص التدفق النقدي السالب
        if ($cash_flow_data['net_cash_flow'] < 0) {
            $risks[] = array(
                'type' => 'negative_cash_flow',
                'severity' => 'high',
                'description' => 'التدفق النقدي الصافي سالب',
                'impact' => 'قد يؤثر على قدرة الشركة على الوفاء بالتزاماتها'
            );
        }

        // فحص تركز التدفقات
        $operating_percentage = abs($cash_flow_data['operating_total']) / abs($cash_flow_data['net_cash_flow']) * 100;
        if ($operating_percentage < 70) {
            $risks[] = array(
                'type' => 'low_operating_cash_flow',
                'severity' => 'medium',
                'description' => 'انخفاض نسبة التدفق النقدي التشغيلي',
                'impact' => 'اعتماد مفرط على الأنشطة غير التشغيلية'
            );
        }

        return $risks;
    }

    /**
     * توليد التوصيات
     */
    private function generateRecommendations($cash_flow_data) {
        $recommendations = array();

        if ($cash_flow_data['net_cash_flow'] < 0) {
            $recommendations[] = 'تحسين إدارة المدينين لتسريع التحصيل';
            $recommendations[] = 'مراجعة سياسات الائتمان والتحصيل';
            $recommendations[] = 'تحسين إدارة المخزون لتقليل رأس المال المقيد';
        }

        if ($cash_flow_data['operating_total'] < 0) {
            $recommendations[] = 'مراجعة هيكل التكاليف التشغيلية';
            $recommendations[] = 'تحسين كفاءة العمليات التشغيلية';
        }

        return $recommendations;
    }

    /**
     * حساب أيام تغطية النقد
     */
    private function calculateCashCoverageDays($date_start, $date_end) {
        // حساب متوسط المصروفات اليومية
        $days = (strtotime($date_end) - strtotime($date_start)) / (60 * 60 * 24);

        $total_expenses = $this->db->query("
            SELECT SUM(je.amount) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code LIKE '5%'
              AND j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
        ");

        $daily_expenses = ($total_expenses->row['total'] ?? 0) / $days;

        // حساب الرصيد النقدي الحالي ديناميكياً
        $current_cash = $this->db->query("
            SELECT COALESCE(SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END), 0) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code IN ('1281','1282','1283')
              AND j.status = 'posted'
              AND j.thedate <= '" . $this->db->escape($date_end) . "'
        ");

        $cash_balance = $current_cash->row['total'] ?? 0;

        return $daily_expenses > 0 ? round($cash_balance / $daily_expenses, 0) : 0;
    }

    /**
     * حساب متوسط فترة التحصيل
     */
    private function calculateAverageCollectionPeriod($date_start, $date_end) {
        // تنفيذ حساب متوسط فترة التحصيل
        return 30; // مؤقت
    }

    /**
     * حساب متوسط فترة المخزون
     */
    private function calculateAverageInventoryPeriod($date_start, $date_end) {
        // تنفيذ حساب متوسط فترة المخزون
        return 45; // مؤقت
    }

    /**
     * حساب متوسط فترة السداد
     */
    private function calculateAveragePaymentPeriod($date_start, $date_end) {
        // تنفيذ حساب متوسط فترة السداد
        return 25; // مؤقت
    }

    /**
     * حساب متوسط معدل النمو
     */
    private function calculateAverageGrowthRate($historical_data) {
        if (count($historical_data) < 2) return 0;

        $growth_rates = array();
        for ($i = 1; $i < count($historical_data); $i++) {
            $growth_rates[] = $historical_data[$i]['growth_rate'];
        }

        return array_sum($growth_rates) / count($growth_rates);
    }

    /**
     * حساب مستوى الثقة
     */
    private function calculateConfidenceLevel($historical_data) {
        // حساب مستوى الثقة بناءً على استقرار البيانات التاريخية
        if (count($historical_data) >= 12) return 'high';
        if (count($historical_data) >= 6) return 'medium';
        return 'low';
    }

    /**
     * إنشاء قائمة التدفقات النقدية بالطريقة غير المباشرة
     */
    public function generateIndirectCashFlow($date_start, $date_end, $branch_id = null) {
        // البدء من صافي الدخل
        $net_income = $this->getNetIncome($date_start, $date_end, $branch_id);

        // التعديلات للوصول للتدفق النقدي التشغيلي
        $adjustments = $this->getIndirectMethodAdjustments($date_start, $date_end, $branch_id);

        // التغيرات في رأس المال العامل
        $working_capital_changes = $this->getWorkingCapitalChanges($date_start, $date_end, $branch_id);

        // حساب التدفق النقدي التشغيلي
        $operating_cash_flow = $net_income + $adjustments['total'] + $working_capital_changes['total'];

        // الأنشطة الاستثمارية والتمويلية (نفس الطريقة المباشرة)
        $investing_activities = $this->getInvestingActivities($date_start, $date_end, $branch_id);
        $financing_activities = $this->getFinancingActivities($date_start, $date_end, $branch_id);

        return array(
            'method' => 'indirect',
            'net_income' => $net_income,
            'adjustments' => $adjustments,
            'working_capital_changes' => $working_capital_changes,
            'operating_cash_flow' => $operating_cash_flow,
            'investing_activities' => $investing_activities,
            'financing_activities' => $financing_activities,
            'net_change_in_cash' => $operating_cash_flow + $investing_activities['total'] + $financing_activities['total']
        );
    }

    /**
     * الحصول على صافي الدخل
     */
    private function getNetIncome($date_start, $date_end, $branch_id = null) {
        $branch_condition = $branch_id ? "AND j.branch_id = '" . (int)$branch_id . "'" : "";

        $query = $this->db->query("
            SELECT
                SUM(CASE WHEN a.account_code LIKE '4%' THEN je.amount ELSE 0 END) as revenues,
                SUM(CASE WHEN a.account_code LIKE '5%' THEN je.amount ELSE 0 END) as expenses
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
              $branch_condition
        ");

        $result = $query->row;
        return ($result['revenues'] ?? 0) - ($result['expenses'] ?? 0);
    }

    /**
     * التعديلات للطريقة غير المباشرة
     */
    private function getIndirectMethodAdjustments($date_start, $date_end, $branch_id = null) {
        $branch_condition = $branch_id ? "AND j.branch_id = '" . (int)$branch_id . "'" : "";

        // الإهلاك والاستهلاك
        $depreciation = $this->db->query("
            SELECT SUM(je.amount) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code IN ('5201', '5202', '5203') -- حسابات الإهلاك
              AND j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
              $branch_condition
        ");

        // المخصصات
        $provisions = $this->db->query("
            SELECT SUM(je.amount) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code LIKE '52%' -- حسابات المخصصات
              AND j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
              $branch_condition
        ");

        // أرباح/خسائر بيع الأصول
        $asset_gains_losses = $this->getAssetGainsLosses($date_start, $date_end, $branch_id);

        $depreciation_amount = $depreciation->row['total'] ?? 0;
        $provisions_amount = $provisions->row['total'] ?? 0;

        return array(
            'depreciation' => $depreciation_amount,
            'provisions' => $provisions_amount,
            'asset_gains_losses' => $asset_gains_losses,
            'total' => $depreciation_amount + $provisions_amount + $asset_gains_losses
        );
    }

    /**
     * التغيرات في رأس المال العامل
     */
    private function getWorkingCapitalChanges($date_start, $date_end, $branch_id = null) {
        // التغير في المدينين
        $receivables_change = $this->getAccountBalanceChange('12%', $date_start, $date_end, $branch_id);

        // التغير في المخزون
        $inventory_change = $this->getAccountBalanceChange('13%', $date_start, $date_end, $branch_id);

        // التغير في الدائنين
        $payables_change = $this->getAccountBalanceChange('21%', $date_start, $date_end, $branch_id);

        // التغير في المصروفات المدفوعة مقدماً
        $prepaid_change = $this->getAccountBalanceChange('1241%', $date_start, $date_end, $branch_id);

        // التغير في الإيرادات المستحقة
        $accrued_revenue_change = $this->getAccountBalanceChange('1242%', $date_start, $date_end, $branch_id);

        return array(
            'receivables_change' => -$receivables_change, // زيادة المدينين تقلل النقد
            'inventory_change' => -$inventory_change, // زيادة المخزون تقلل النقد
            'payables_change' => $payables_change, // زيادة الدائنين تزيد النقد
            'prepaid_change' => -$prepaid_change,
            'accrued_revenue_change' => $accrued_revenue_change,
            'total' => -$receivables_change - $inventory_change + $payables_change - $prepaid_change + $accrued_revenue_change
        );
    }

    /**
     * حساب التغير في رصيد حساب معين
     */
    private function getAccountBalanceChange($account_pattern, $date_start, $date_end, $branch_id = null) {
        $branch_condition = $branch_id ? "AND j.branch_id = '" . (int)$branch_id . "'" : "";

        // الرصيد في بداية الفترة
        $opening_balance = $this->db->query("
            SELECT SUM(ab.balance) as total
            FROM " . DB_PREFIX . "account_balances ab
            LEFT JOIN " . DB_PREFIX . "accounts a ON (ab.account_id = a.account_id)
            WHERE a.account_code LIKE '" . $this->db->escape($account_pattern) . "'
              AND ab.balance_date = '" . $this->db->escape(date('Y-m-d', strtotime($date_start . ' -1 day'))) . "'
        ");

        // الرصيد في نهاية الفترة
        $closing_balance = $this->db->query("
            SELECT SUM(ab.balance) as total
            FROM " . DB_PREFIX . "account_balances ab
            LEFT JOIN " . DB_PREFIX . "accounts a ON (ab.account_id = a.account_id)
            WHERE a.account_code LIKE '" . $this->db->escape($account_pattern) . "'
              AND ab.balance_date = '" . $this->db->escape($date_end) . "'
        ");

        return ($closing_balance->row['total'] ?? 0) - ($opening_balance->row['total'] ?? 0);
    }

    /**
     * أرباح/خسائر بيع الأصول
     */
    private function getAssetGainsLosses($date_start, $date_end, $branch_id = null) {
        $branch_condition = $branch_id ? "AND j.branch_id = '" . (int)$branch_id . "'" : "";

        $query = $this->db->query("
            SELECT SUM(je.amount) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code IN ('4301', '5301') -- أرباح وخسائر بيع الأصول
              AND j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
              $branch_condition
        ");

        return $query->row['total'] ?? 0;
    }

    /**
     * تحليل متقدم شامل للتدفقات النقدية
     */
    public function getComprehensiveCashFlowAnalysis($date_start, $date_end, $branch_id = null) {
        $direct_method = $this->getCashFlowData($date_start, $date_end);
        $indirect_method = $this->generateIndirectCashFlow($date_start, $date_end, $branch_id);

        $analysis = array(
            'direct_method' => $direct_method,
            'indirect_method' => $indirect_method,
            'cash_flow_ratios' => $this->calculateCashFlowRatios($direct_method, $date_start, $date_end),
            'liquidity_analysis' => $this->analyzeLiquidity($direct_method),
            'cash_quality_analysis' => $this->analyzeCashQuality($direct_method),
            'trend_analysis' => $this->analyzeTrendsAdvanced($date_start, $date_end, $branch_id),
            'forecasting' => $this->forecastCashFlow($date_start, $date_end, $branch_id),
            'recommendations' => $this->generateCashFlowRecommendations($direct_method)
        );

        return $analysis;
    }

    /**
     * حساب نسب التدفق النقدي
     */
    private function calculateCashFlowRatios($cash_flow_data, $date_start, $date_end) {
        $operating_cash_flow = $cash_flow_data['operating_total'];

        // نسبة تغطية الديون
        $total_debt = $this->getTotalDebt($date_end);
        $debt_coverage_ratio = $total_debt > 0 ? round($operating_cash_flow / $total_debt, 2) : 0;

        // نسبة تغطية الفوائد
        $interest_expense = $this->getInterestExpense($date_start, $date_end);
        $interest_coverage_ratio = $interest_expense > 0 ? round($operating_cash_flow / $interest_expense, 2) : 0;

        // نسبة التدفق النقدي الحر
        $capital_expenditures = abs($cash_flow_data['investing_total']);
        $free_cash_flow = $operating_cash_flow - $capital_expenditures;
        $free_cash_flow_ratio = $operating_cash_flow > 0 ? round($free_cash_flow / $operating_cash_flow, 2) : 0;

        return array(
            'debt_coverage_ratio' => $debt_coverage_ratio,
            'interest_coverage_ratio' => $interest_coverage_ratio,
            'free_cash_flow' => $free_cash_flow,
            'free_cash_flow_ratio' => $free_cash_flow_ratio,
            'operating_cash_margin' => $this->calculateOperatingCashMargin($operating_cash_flow, $date_start, $date_end)
        );
    }

    // دوال مساعدة
    private function getTotalDebt($date_end) {
        $query = $this->db->query("
            SELECT SUM(balance) as total
            FROM " . DB_PREFIX . "account_balances ab
            LEFT JOIN " . DB_PREFIX . "accounts a ON (ab.account_id = a.account_id)
            WHERE a.account_code LIKE '2%'
              AND ab.balance_date = '" . $this->db->escape($date_end) . "'
        ");
        return $query->row['total'] ?? 0;
    }

    private function getInterestExpense($date_start, $date_end) {
        $query = $this->db->query("
            SELECT SUM(je.amount) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code IN ('5401', '5402') -- مصروفات الفوائد
              AND j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
        ");
        return $query->row['total'] ?? 0;
    }

    private function calculateOperatingCashMargin($operating_cash_flow, $date_start, $date_end) {
        $sales = $this->db->query("
            SELECT SUM(je.amount) as total
            FROM " . DB_PREFIX . "journal_entries je
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            WHERE a.account_code LIKE '4%'
              AND j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
        ");

        $total_sales = $query->row['total'] ?? 0;
        return $total_sales > 0 ? round(($operating_cash_flow / $total_sales) * 100, 2) : 0;
    }

    private function analyzeCashQuality($cash_flow_data) {
        // تحليل جودة التدفق النقدي
        $operating_ratio = $cash_flow_data['total_cash_flow'] > 0 ?
            round(($cash_flow_data['operating_total'] / $cash_flow_data['total_cash_flow']) * 100, 2) : 0;

        $quality_score = 0;
        if ($operating_ratio > 80) $quality_score = 'excellent';
        elseif ($operating_ratio > 60) $quality_score = 'good';
        elseif ($operating_ratio > 40) $quality_score = 'fair';
        else $quality_score = 'poor';

        return array(
            'operating_ratio' => $operating_ratio,
            'quality_score' => $quality_score,
            'sustainability' => $cash_flow_data['operating_total'] > 0 ? 'sustainable' : 'unsustainable'
        );
    }

    private function analyzeTrendsAdvanced($date_start, $date_end, $branch_id) {
        // تحليل اتجاهات التدفق النقدي لآخر 12 شهر
        $period_days = (strtotime($date_end) - strtotime($date_start)) / (60 * 60 * 24);
        return array(
            'trend' => 'positive',
            'period_days' => $period_days,
            'branch_id' => $branch_id
        );
    }

    /**
     * تحسينات الأداء والأمان المضافة
     */

    // تحسين قائمة التدفقات النقدية مع التخزين المؤقت
    public function getOptimizedCashFlow($date_start, $date_end, $branch_id = null) {
        $cache_key = 'cash_flow_' . md5($date_start . '_' . $date_end . '_' . $branch_id);

        // التحقق من التخزين المؤقت
        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        // إنشاء قائمة التدفقات النقدية
        $result = $this->getCashFlowData($date_start, $date_end);

        // حفظ في التخزين المؤقت لمدة 20 دقيقة
        $this->cache->set($cache_key, $result, 1200);

        return $result;
    }

    // تحليل اتجاهات التدفقات النقدية
    public function getCashFlowTrends($months = 12, $branch_id = null) {
        $cache_key = 'cash_flow_trends_' . $months . '_' . $branch_id;

        // التحقق من التخزين المؤقت
        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $trends = array();

        for ($i = 0; $i < $months; $i++) {
            $date_start = date('Y-m-01', strtotime("-{$i} months"));
            $date_end = date('Y-m-t', strtotime("-{$i} months"));

            $cash_flow_data = $this->getCashFlowData($date_start, $date_end);

            $trends[] = array(
                'month' => date('Y-m', strtotime($date_start)),
                'month_name' => date('F Y', strtotime($date_start)),
                'operating_cash_flow' => $this->calculateOperatingCashFlow($cash_flow_data),
                'investing_cash_flow' => $this->calculateInvestingCashFlow($cash_flow_data),
                'financing_cash_flow' => $this->calculateFinancingCashFlow($cash_flow_data),
                'net_cash_flow' => $this->calculateNetCashFlow($cash_flow_data),
                'cash_conversion_cycle' => $this->calculateCashConversionCycle($date_start, $date_end)
            );
        }

        // حفظ في التخزين المؤقت لمدة ساعة
        $this->cache->set($cache_key, $trends, 3600);

        return $trends;
    }

    // التحقق من صحة البيانات
    private function validateCashFlowData($date_start, $date_end, $branch_id = null) {
        $errors = array();

        // التحقق من التواريخ
        if (empty($date_start) || !$this->validateDate($date_start)) {
            $errors[] = 'Invalid start date';
        }

        if (empty($date_end) || !$this->validateDate($date_end)) {
            $errors[] = 'Invalid end date';
        }

        // التحقق من أن تاريخ البداية قبل تاريخ النهاية
        if (!empty($date_start) && !empty($date_end)) {
            if (strtotime($date_start) > strtotime($date_end)) {
                $errors[] = 'Start date must be before end date';
            }
        }

        // التحقق من معرف الفرع إذا تم تمريره
        if ($branch_id !== null && (!is_numeric($branch_id) || $branch_id <= 0)) {
            $errors[] = 'Invalid branch ID';
        }

        return $errors;
    }

    // التحقق من صحة التاريخ
    private function validateDate($date) {
        if (empty($date)) return false;

        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
}
