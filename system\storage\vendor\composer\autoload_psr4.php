<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname(dirname(dirname(dirname($vendorDir))));

return array(
    'Wechat\\' => array($vendorDir . '/zoujingli/wechat-php-sdk/Wechat'),
    'WePay\\' => array($vendorDir . '/zoujingli/wechat-developer/WePay'),
    'WePayV3\\' => array($vendorDir . '/zoujingli/wechat-developer/WePayV3'),
    'WeMini\\' => array($vendorDir . '/zoujingli/wechat-developer/WeMini'),
    'WeChat\\' => array($vendorDir . '/zoujingli/wechat-developer/WeChat'),
    'Twig\\' => array($vendorDir . '/twig/twig/src'),
    'Tools\\PHPStan\\' => array($baseDir . '/tools/phpstan'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Php72\\' => array($vendorDir . '/symfony/polyfill-php72'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Translation\\' => array($vendorDir . '/symfony/translation-contracts'),
    'Symfony\\Component\\Validator\\' => array($vendorDir . '/symfony/validator'),
    'ScssPhp\\ScssPhp\\' => array($vendorDir . '/scssphp/scssphp/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-message/src'),
    'GuzzleHttp\\Subscriber\\Oauth\\' => array($vendorDir . '/guzzlehttp/oauth-subscriber/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'Cardinity\\' => array($vendorDir . '/cardinity/cardinity-sdk-php/src'),
    'Braintree\\' => array($vendorDir . '/braintree/braintree_php/lib/Braintree'),
    'AliPay\\' => array($vendorDir . '/zoujingli/wechat-developer/AliPay'),
);
