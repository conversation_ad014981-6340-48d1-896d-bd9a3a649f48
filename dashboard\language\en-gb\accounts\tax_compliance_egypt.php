<?php
// Heading
$_['heading_title']                    = 'Egyptian Tax Compliance - ETA Integration';
$_['heading_title_settings']           = 'Egyptian Tax Compliance Settings';

// Text
$_['text_success']                     = 'Success: You have modified Egyptian tax compliance!';
$_['text_success_settings']            = 'Success: You have saved ETA settings!';
$_['text_list']                        = 'Tax Compliance List';
$_['text_add']                         = 'Add Tax Compliance';
$_['text_edit']                        = 'Edit Tax Compliance';
$_['text_default']                     = 'Default';
$_['text_enabled']                     = 'Enabled';
$_['text_disabled']                    = 'Disabled';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_home']                        = 'Home';

// ETA Integration
$_['text_eta_integration']             = 'Egyptian Tax Authority Integration';
$_['text_connection_status']           = 'Connection Status';
$_['text_connected']                   = 'Connected';
$_['text_disconnected']                = 'Disconnected';
$_['text_connection_successful']       = 'Successfully connected to ETA';
$_['text_test_connection']             = 'Test Connection';
$_['text_sync_codes']                  = 'Sync Codes';
$_['text_codes_synced']                = 'Success: ETA codes synchronized successfully!';

// Dashboard
$_['text_compliance_dashboard']        = 'Tax Compliance Dashboard';
$_['text_invoice_stats']               = 'Invoice Statistics';
$_['text_vat_return_stats']            = 'VAT Return Statistics';
$_['text_compliance_score']            = 'Compliance Score';
$_['text_recent_errors']               = 'Recent Errors';
$_['text_pending_submissions']         = 'Pending Submissions';
$_['text_recent_activities']           = 'Recent Activities';

// Invoice Submission
$_['text_submit_invoices']             = 'Submit Invoices';
$_['text_invoices_submitted']          = 'Success: Invoices submitted to ETA successfully!';
$_['text_invoice_submission']          = 'Invoice Submission';
$_['text_select_period']               = 'Select Period';
$_['text_invoice_type']                = 'Invoice Type';
$_['text_all_invoices']                = 'All Invoices';
$_['text_sales_invoices']              = 'Sales Invoices';
$_['text_purchase_invoices']           = 'Purchase Invoices';

// VAT Return
$_['text_submit_vat_return']           = 'Submit VAT Return';
$_['text_vat_return_submitted']        = 'Success: VAT return submitted successfully!';
$_['text_vat_return']                  = 'VAT Return';
$_['text_select_month']                = 'Select Month';
$_['text_select_year']                 = 'Select Year';
$_['text_period_type']                 = 'Period Type';
$_['text_monthly']                     = 'Monthly';
$_['text_quarterly']                   = 'Quarterly';

// Settings
$_['text_eta_settings']                = 'ETA Settings';
$_['text_api_configuration']           = 'API Configuration';
$_['text_company_information']         = 'Company Information';
$_['text_submission_settings']         = 'Submission Settings';

// Entry
$_['entry_eta_api_url']                = 'ETA API URL';
$_['entry_eta_client_id']              = 'Client ID';
$_['entry_eta_client_secret']          = 'Client Secret';
$_['entry_eta_tax_number']             = 'Tax Number';
$_['entry_eta_environment']            = 'Environment';
$_['entry_eta_auto_submit']            = 'Auto Submit';
$_['entry_eta_branch_id']              = 'Branch ID';
$_['entry_eta_activity_code']          = 'Activity Code';

// Environment Options
$_['text_production']                  = 'Production';
$_['text_sandbox']                     = 'Sandbox';

// Status
$_['text_status_submitted']            = 'Submitted';
$_['text_status_pending']              = 'Pending';
$_['text_status_failed']               = 'Failed';
$_['text_status_not_submitted']        = 'Not Submitted';

// Statistics
$_['text_total_invoices']              = 'Total Invoices';
$_['text_submitted_invoices']          = 'Submitted Invoices';
$_['text_pending_invoices']            = 'Pending Invoices';
$_['text_failed_invoices']             = 'Failed Invoices';
$_['text_not_submitted_invoices']      = 'Not Submitted Invoices';
$_['text_total_returns']               = 'Total Returns';
$_['text_submitted_returns']           = 'Submitted Returns';
$_['text_pending_returns']             = 'Pending Returns';

// Activities
$_['text_activity_invoice_submission'] = 'Invoice Submission';
$_['text_activity_vat_return']         = 'VAT Return';
$_['text_activity_code_sync']          = 'Code Synchronization';
$_['text_activity_connection_test']    = 'Connection Test';

// Buttons
$_['button_submit']                    = 'Submit';
$_['button_test_connection']           = 'Test Connection';
$_['button_sync_codes']                = 'Sync Codes';
$_['button_submit_invoices']           = 'Submit Invoices';
$_['button_submit_vat_return']         = 'Submit VAT Return';
$_['button_settings']                  = 'Settings';
$_['button_refresh']                   = 'Refresh';
$_['button_export']                    = 'Export';
$_['button_save']                      = 'Save';
$_['button_cancel']                    = 'Cancel';

// Help
$_['help_eta_api_url']                 = 'Egyptian Tax Authority API URL';
$_['help_eta_client_id']               = 'Client ID provided by ETA';
$_['help_eta_client_secret']           = 'Client Secret provided by ETA';
$_['help_eta_tax_number']              = 'Company tax number registered with ETA';
$_['help_eta_environment']             = 'Select environment: Production for live use, Sandbox for testing';
$_['help_eta_auto_submit']             = 'Automatically submit invoices to ETA when created';

// Error
$_['error_permission']                 = 'Warning: You do not have permission to access Egyptian tax compliance!';
$_['error_client_id']                  = 'Client ID required!';
$_['error_client_secret']              = 'Client Secret required!';
$_['error_tax_number']                 = 'Tax Number required!';
$_['error_api_url']                    = 'API URL required!';
$_['error_connection_failed']          = 'Connection to ETA failed';
$_['error_authentication_failed']      = 'Authentication with ETA failed';
$_['error_invalid_response']           = 'Invalid response from ETA';
$_['error_submission_failed']          = 'Data submission to ETA failed';
$_['error_no_invoices_found']          = 'No invoices found for submission';
$_['error_invalid_period']             = 'Invalid period';

// Log Messages
$_['log_unauthorized_access']          = 'Unauthorized access attempt to Egyptian tax compliance';
$_['log_view_screen']                  = 'Viewed Egyptian tax compliance screen';
$_['log_invoices_submitted']           = 'Invoices submitted to ETA';
$_['log_vat_return_submitted']         = 'VAT return submitted to ETA';
$_['log_codes_synced']                 = 'ETA codes synchronized';
$_['log_connection_tested']            = 'ETA connection tested';
$_['log_settings_updated']             = 'ETA settings updated';

// Enhanced features
$_['text_digital_signature']           = 'Digital Signature';
$_['text_electronic_invoice']          = 'Electronic Invoice';
$_['text_compliance_report']           = 'Compliance Report';
$_['text_audit_trail']                 = 'Audit Trail';
$_['text_real_time_validation']        = 'Real-time Validation';
$_['text_bulk_submission']             = 'Bulk Submission';
$_['text_error_handling']              = 'Error Handling';
$_['text_retry_mechanism']             = 'Retry Mechanism';
$_['text_data_encryption']             = 'Data Encryption';
$_['text_secure_transmission']         = 'Secure Transmission';

// Performance and analytics
$_['text_submission_analytics']        = 'Submission Analytics';
$_['text_performance_metrics']         = 'Performance Metrics';
$_['text_success_rate']                = 'Success Rate';
$_['text_average_response_time']       = 'Average Response Time';
$_['text_error_analysis']              = 'Error Analysis';
$_['text_trend_analysis']              = 'Trend Analysis';

// Loading messages
$_['text_loading']                     = 'Loading...';
$_['text_loading_dashboard']           = 'Loading compliance dashboard...';
$_['text_loading_submission']          = 'Submitting data...';
$_['text_loading_sync']                = 'Synchronizing codes...';
$_['text_loading_test']                = 'Testing connection...';

// Success messages
$_['text_dashboard_loaded']            = 'Compliance dashboard loaded successfully';
$_['text_submission_completed']        = 'Submission completed successfully';
$_['text_sync_completed']              = 'Synchronization completed successfully';
$_['text_test_completed']              = 'Connection test completed successfully';
?>
