<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/credentials/v1/iamcredentials.proto

namespace GPBMetadata\Google\Iam\Credentials\V1;

class Iamcredentials
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Api\Client::initOnce();
        \GPBMetadata\Google\Iam\Credentials\V1\Common::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0acc090a2e676f6f676c652f69616d2f63726564656e7469616c732f7631" .
            "2f69616d63726564656e7469616c732e70726f746f1219676f6f676c652e" .
            "69616d2e63726564656e7469616c732e76311a17676f6f676c652f617069" .
            "2f636c69656e742e70726f746f1a26676f6f676c652f69616d2f63726564" .
            "656e7469616c732f76312f636f6d6d6f6e2e70726f746f32ad070a0e4941" .
            "4d43726564656e7469616c7312ec010a1347656e65726174654163636573" .
            "73546f6b656e12352e676f6f676c652e69616d2e63726564656e7469616c" .
            "732e76312e47656e6572617465416363657373546f6b656e526571756573" .
            "741a362e676f6f676c652e69616d2e63726564656e7469616c732e76312e" .
            "47656e6572617465416363657373546f6b656e526573706f6e7365226682" .
            "d3e4930240223b2f76312f7b6e616d653d70726f6a656374732f2a2f7365" .
            "72766963654163636f756e74732f2a7d3a67656e65726174654163636573" .
            "73546f6b656e3a012ada411d6e616d652c64656c6567617465732c73636f" .
            "70652c6c69666574696d6512e4010a0f47656e65726174654964546f6b65" .
            "6e12312e676f6f676c652e69616d2e63726564656e7469616c732e76312e" .
            "47656e65726174654964546f6b656e526571756573741a322e676f6f676c" .
            "652e69616d2e63726564656e7469616c732e76312e47656e657261746549" .
            "64546f6b656e526573706f6e7365226a82d3e493023c22372f76312f7b6e" .
            "616d653d70726f6a656374732f2a2f736572766963654163636f756e7473" .
            "2f2a7d3a67656e65726174654964546f6b656e3a012ada41256e616d652c" .
            "64656c6567617465732c61756469656e63652c696e636c7564655f656d61" .
            "696c12b9010a085369676e426c6f62122a2e676f6f676c652e69616d2e63" .
            "726564656e7469616c732e76312e5369676e426c6f62526571756573741a" .
            "2b2e676f6f676c652e69616d2e63726564656e7469616c732e76312e5369" .
            "676e426c6f62526573706f6e7365225482d3e493023522302f76312f7b6e" .
            "616d653d70726f6a656374732f2a2f736572766963654163636f756e7473" .
            "2f2a7d3a7369676e426c6f623a012ada41166e616d652c64656c65676174" .
            "65732c7061796c6f616412b5010a075369676e4a777412292e676f6f676c" .
            "652e69616d2e63726564656e7469616c732e76312e5369676e4a77745265" .
            "71756573741a2a2e676f6f676c652e69616d2e63726564656e7469616c73" .
            "2e76312e5369676e4a7774526573706f6e7365225382d3e4930234222f2f" .
            "76312f7b6e616d653d70726f6a656374732f2a2f73657276696365416363" .
            "6f756e74732f2a7d3a7369676e4a77743a012ada41166e616d652c64656c" .
            "6567617465732c7061796c6f61641a51ca411d69616d63726564656e7469" .
            "616c732e676f6f676c65617069732e636f6dd2412e68747470733a2f2f77" .
            "77772e676f6f676c65617069732e636f6d2f617574682f636c6f75642d70" .
            "6c6174666f726d4285010a23636f6d2e676f6f676c652e636c6f75642e69" .
            "616d2e63726564656e7469616c732e7631421349414d43726564656e7469" .
            "616c7350726f746f50015a44676f6f676c652e676f6c616e672e6f72672f" .
            "67656e70726f746f2f676f6f676c65617069732f69616d2f63726564656e" .
            "7469616c732f76313b63726564656e7469616c73f80101620670726f746f" .
            "33"
        ), true);

        static::$is_initialized = true;
    }
}

