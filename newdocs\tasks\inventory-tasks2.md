# المهام المتقدمة - تسوية ونقل المخزون
## Inventory Tasks 2 - Advanced Operations

### 📋 **معلومات المهام:**
- **الملف:** tasks2.md
- **المدة:** 5 أيام
- **الأولوية:** عالية
- **الاعتمادية:** يتطلب إكمال tasks1.md أولاً

---

## 🎯 **الهدف الأساسي**
تطوير العمليات المتقدمة للمخزون: التسوية والنقل بين المستودعات مع نظام موافقات متقدم.

---

## 📋 **المهمة الأولى: تسوية المخزون**
### **الملف:** `dashboard/controller/inventory/stock_adjustment.php`

#### **اليوم الأول: التحليل والتخطيط**
- [ ] **1.1** قراءة وتحليل جدول `cod_stock_adjustment` في inventory_ecommerce_updates.sql
- [ ] **1.2** مراجعة العلاقات مع `cod_product_inventory` و `cod_approval_workflow`
- [ ] **1.3** دراسة نموذج الإقفال `period_closing.php` كمرجع
- [ ] **1.4** تحديد أنواع التسوية (زيادة، نقص، تصحيح)
- [ ] **1.5** تحديد مستويات الموافقة المطلوبة

#### **اليوم الثاني: تطوير الكونترولر**
- [ ] **2.1** إنشاء `controller/inventory/stock_adjustment.php`
- [ ] **2.2** تطبيق الدستور الشامل (20 قاعدة)
- [ ] **2.3** ربط الخدمات المركزية:
  - `model/workflow/visual_workflow_engine.php` للموافقات
  - `model/activity_log.php` للتدقيق المتقدم
  - `model/accounts/journal.php` للقيود المحاسبية
- [ ] **2.4** تطبيق نظام الموافقات متعدد المستويات
- [ ] **2.5** إضافة دوال التسوية المختلفة

#### **اليوم الثالث: تطوير الموديل**
- [ ] **3.1** إنشاء `model/inventory/stock_adjustment.php`
- [ ] **3.2** تطوير دوال قاعدة البيانات:
  - `getAdjustments($filters)` - قائمة التسويات
  - `getAdjustment($adjustment_id)` - تفاصيل تسوية
  - `addAdjustment($data)` - إضافة تسوية
  - `approveAdjustment($adjustment_id)` - موافقة تسوية
  - `executeAdjustment($adjustment_id)` - تنفيذ تسوية
- [ ] **3.3** تطوير منطق حساب الفروقات
- [ ] **3.4** ربط مع النظام المحاسبي لقيود التسوية

#### **اليوم الرابع: تطوير التيمبليت**
- [ ] **4.1** إنشاء `view/template/inventory/adjustment_list.twig`
- [ ] **4.2** إنشاء `view/template/inventory/adjustment_form.twig`
- [ ] **4.3** إضافة واجهة الموافقات التفاعلية
- [ ] **4.4** إضافة تقارير الفروقات
- [ ] **4.5** ربط مع نظام الطباعة والتصدير

#### **اليوم الخامس: ملفات اللغة والاختبار**
- [ ] **5.1** إنشاء ملفات اللغة (عربي/إنجليزي)
- [ ] **5.2** اختبار سيناريوهات التسوية المختلفة
- [ ] **5.3** اختبار نظام الموافقات
- [ ] **5.4** اختبار القيود المحاسبية
- [ ] **5.5** توثيق العمليات والسياسات

---

## 📋 **المهمة الثانية: نقل المخزون بين المستودعات**
### **الملف:** `dashboard/controller/inventory/stock_transfer.php`

#### **اليوم السادس: التحليل والتخطيط**
- [ ] **6.1** قراءة وتحليل جدول `cod_stock_transfer` في inventory_ecommerce_updates.sql
- [ ] **6.2** مراجعة العلاقات مع `cod_warehouse` و `cod_product_inventory`
- [ ] **6.3** دراسة نموذج التدفق النقدي `cash_flow.php` كمرجع
- [ ] **6.4** تحديد مراحل النقل (طلب، موافقة، شحن، استلام)
- [ ] **6.5** تحديد القيود المحاسبية للنقل

#### **اليوم السابع: تطوير الكونترولر**
- [ ] **7.1** إنشاء `controller/inventory/stock_transfer.php`
- [ ] **7.2** تطبيق الدستور الشامل
- [ ] **7.3** ربط الخدمات المركزية
- [ ] **7.4** تطوير workflow النقل:
  - إنشاء طلب نقل
  - موافقة المستودع المرسل
  - موافقة المستودع المستقبل
  - تنفيذ النقل
- [ ] **7.5** إضافة تتبع حالة النقل

#### **اليوم الثامن: تطوير الموديل**
- [ ] **8.1** إنشاء `model/inventory/stock_transfer.php`
- [ ] **8.2** تطوير دوال قاعدة البيانات:
  - `getTransfers($filters)` - قائمة النقلات
  - `getTransfer($transfer_id)` - تفاصيل نقلة
  - `addTransfer($data)` - إضافة طلب نقل
  - `approveTransfer($transfer_id, $stage)` - موافقة مرحلة
  - `executeTransfer($transfer_id)` - تنفيذ النقل
- [ ] **8.3** تطوير منطق تحديث المخزون في المستودعين
- [ ] **8.4** ربط مع النظام المحاسبي

#### **اليوم التاسع: تطوير التيمبليت**
- [ ] **9.1** إنشاء `view/template/inventory/transfer_list.twig`
- [ ] **9.2** إنشاء `view/template/inventory/transfer_form.twig`
- [ ] **9.3** إضافة واجهة تتبع النقلات
- [ ] **9.4** إضافة تقارير النقل
- [ ] **9.5** ربط مع نظام الباركود للتتبع

#### **اليوم العاشر: ملفات اللغة والاختبار**
- [ ] **10.1** إنشاء ملفات اللغة
- [ ] **10.2** اختبار سيناريوهات النقل المختلفة
- [ ] **10.3** اختبار التكامل مع المستودعات
- [ ] **10.4** اختبار دقة تحديث المخزون
- [ ] **10.5** توثيق عمليات النقل

---

## 🔗 **الاعتمادية والترابط**

### **يعتمد على:**
- إكمال tasks1.md (المستودعات وحركات المخزون)
- نظام الموافقات الموجود
- النظام المحاسبي

### **يؤثر على:**
- tasks3.md (الباقات والوحدات)
- tasks4.md (المخزون الوهمي)
- تقارير المخزون المتقدمة

---

## 📊 **مؤشرات النجاح**

### **المؤشرات التقنية:**
- [ ] **100% دقة** في عمليات التسوية
- [ ] **تتبع كامل** لجميع مراحل النقل
- [ ] **قيود محاسبية** صحيحة لكل عملية
- [ ] **نظام موافقات** يعمل بسلاسة

### **المؤشرات الوظيفية:**
- [ ] **تسوية سهلة** للمخزون مع الموافقات
- [ ] **نقل آمن** بين المستودعات
- [ ] **تقارير دقيقة** للفروقات والنقلات
- [ ] **تدقيق شامل** لجميع العمليات

---

## 🚨 **تحذيرات مهمة**

### **نقاط حرجة:**
1. **لا تبدأ** قبل إكمال warehouse.php و stock_movement.php
2. **اختبر الموافقات** بعناية قبل التطبيق
3. **تأكد من دقة** تحديث المخزون في النقل
4. **راجع القيود المحاسبية** لكل عملية

### **متطلبات إلزامية:**
- نظام موافقات متعدد المستويات
- تدقيق شامل لجميع العمليات
- قيود محاسبية تلقائية ودقيقة
- تقارير مفصلة للمراجعة

---

## 📈 **التحسينات المتقدمة**

### **ميزات إضافية:**
- [ ] **تسوية جماعية** لعدة منتجات
- [ ] **نقل مجدول** بتواريخ محددة
- [ ] **تنبيهات تلقائية** للموافقات المعلقة
- [ ] **تكامل مع الباركود** للتتبع

### **تحسينات الأداء:**
- [ ] **معالجة مجمعة** للعمليات الكبيرة
- [ ] **تخزين مؤقت** للاستعلامات المتكررة
- [ ] **فهرسة محسنة** لجداول النقل
- [ ] **ضغط البيانات** للتقارير الكبيرة

---

**🎯 الهدف:** إنشاء نظام متقدم وموثوق لتسوية ونقل المخزون مع ضوابط صارمة وتدقيق شامل.
