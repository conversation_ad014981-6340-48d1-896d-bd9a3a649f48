<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/expr.proto

namespace Google\Api\Expr\V1beta1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Expr\V1beta1\Expr\Ident instead.
     * @deprecated
     */
    class Expr_Ident {}
}
class_exists(Expr\Ident::class);
@trigger_error('Google\Api\Expr\V1beta1\Expr_Ident is deprecated and will be removed in the next major release. Use Google\Api\Expr\V1beta1\Expr\Ident instead', E_USER_DEPRECATED);

