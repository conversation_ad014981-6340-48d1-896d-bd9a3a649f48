<?php
// Text
$_['text_title']            = 'Credit Card / Debit Card';
$_['text_card_details']     = 'Card Details';
$_['text_echeck_details']   = 'eCheck Details';
$_['text_card']             = 'Card';
$_['text_echeck']           = 'eCheck';
$_['text_wait']             = 'Please wait!';
$_['text_confirm_delete']   = 'Are you sure you want to delete the card?';
$_['text_no_cards']         = 'No cards saved yet';
$_['text_select_card']      = 'Please select a card';

// Entry
$_['entry_method']          = 'Method';
$_['entry_card_new_or_old'] = 'New / Existing Card';
$_['entry_card_new']        = 'New';
$_['entry_card_old']        = 'Existing';
$_['entry_card_type']       = 'Card Type';
$_['entry_card_number']     = 'Card Number';
$_['entry_card_expiry']     = 'Expiry';
$_['entry_card_cvv2']       = 'CVV2';
$_['entry_card_save']       = 'Save Card';
$_['entry_card_choice']     = 'Choose Your Card';
$_['entry_account_number']  = 'Account Number';
$_['entry_routing_number']  = 'Routing Number';

// Button
$_['button_confirm']        = 'Confirm Order';
$_['button_delete']         = 'Delete Selected Card';

// Error
$_['error_card_number']     = 'Card Number must be between 1 and 19 characters!';
$_['error_card_type']       = 'Card Type is not a valid selection!';
$_['error_card_cvv2']       = 'CVV2 must be between 1 and 4 characters!';
$_['error_data_missing']    = 'Missing data!';
$_['error_not_logged_in']   = 'Not logged in!';
$_['error_no_order']        = 'No matching order!';
$_['error_no_post_data']    = 'No $_POST data';
$_['error_select_card']     = 'Please select a card!';
$_['error_no_card']         = 'No such card found!';
$_['error_no_echeck']       = 'eCheck is not supported!';
$_['error_account_number']  = 'Account Number must be between 1 and 19 characters!';
$_['error_routing_number']  = 'Routing Number must be between 1 and 9 characters!';
$_['error_not_enabled']     = 'Module not enabled';