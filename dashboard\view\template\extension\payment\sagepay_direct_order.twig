<h2>{{ text_payment_info }}</h2>
<div class="alert alert-success" id="sagepay_direct_transaction_msg" style="display:none;"></div>
<table class="table table-striped table-bordered">
  <tr>
    <td>{{ text_order_ref }}</td>
    <td>{{ sagepay_direct_order.VendorTxCode }}</td>
  </tr>
  <tr>
    <td>{{ text_order_total }}</td>
    <td>{{ sagepay_direct_order.total_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_total_released }}</td>
    <td id="payment_sagepay_direct_total_released">{{ sagepay_direct_order.total_released_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_release_status }}</td>
    <td id="release_status">{% if sagepay_direct_order.release_status == 1 %}
      <span class="release_text">{{ text_yes }}</span>
      {% else %}
      <span class="release_text">{{ text_no }}</span>&nbsp;&nbsp;
      {% if sagepay_direct_order.void_status == 0 %}
      <input type="text" width="10" id="release_amount" value="{{ sagepay_direct_order.total }}"/>
      <a class="button btn btn-primary" id="button-release">{{ button_release }}</a> <span class="btn btn-primary" id="img_loading_release" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_void_status }}</td>
    <td id="void_status">{% if sagepay_direct_order.void_status == 1 %}
      <span class="void_text">{{ text_yes }}</span>
      {% elseif sagepay_direct_order.void_status == 0 and sagepay_direct_order.release_status != 1 and sagepay_direct_order.rebate_status != 1 %}
      <span class="void_text">{{ text_no }}</span>&nbsp;&nbsp; <a class="button btn btn-primary" id="button-void">{{ button_void }}</a> <span class="btn btn-primary" id="img_loading_void" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% else %}
      <span class="void_text">{{ text_no }}</span>
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_rebate_status }}</td>
    <td id="rebate_status">{% if sagepay_direct_order.rebate_status == 1 %}
      <span class="rebate_text">{{ text_yes }}</span>
      {% else %}
      <span class="rebate_text">{{ text_no }}</span>&nbsp;&nbsp;
      {% if sagepay_direct_order.total_released > 0 and sagepay_direct_order.void_status == 0 %}
      <input type="text" width="10" id="rebate_amount" />
      <a class="button btn btn-primary" id="button-rebate">{{ button_rebate }}</a> <span class="btn btn-primary" id="img_loading_rebate" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_transactions }}:</td>
    <td><table class="table table-striped table-bordered" id="sagepay_direct_transactions">
        <thead>
          <tr>
            <td class="text-left"><strong>{{ text_column_date_added }}</strong></td>
            <td class="text-left"><strong>{{ text_column_type }}</strong></td>
            <td class="text-left"><strong>{{ text_column_amount }}</strong></td>
          </tr>
        </thead>
        <tbody>
          {% for transaction in sagepay_direct_order.transactions %}
          <tr>
            <td class="text-left">{{ transaction.date_added }}</td>
            <td class="text-left">{{ transaction.type }}</td>
            <td class="text-left">{{ transaction.amount }}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table></td>
  </tr>
</table>
<script type="text/javascript"><!--
  $("#button-void").click(function() {
		if (confirm('{{ text_confirm_void }}')) {
			$.ajax({
				type: 'POST',
				dataType: 'json',
				data: {'order_id': {{ order_id }}},
				url: 'index.php?route=extension/payment/sagepay_direct/void&user_token={{ user_token }}',
				beforeSend: function() {
					$('#button-void').hide();
					$('#img_loading_void').show();
					$('#sagepay_direct_transaction_msg').hide();
				},
				success: function(data) {
					if (data.error == false) {
						html = '';
						html += '<tr>';
						html += '<td class="text-left">' + data.data.date_added + '</td>';
						html += '<td class="text-left">{{ text_void }}</td>';
						html += '<td class="text-left">0.00</td>';
						html += '</tr>';

						$('.void_text').text('{{ text_yes }}');
						$('#sagepay_direct_transactions').append(html);
						$('#button-release').hide();
						$('#release_amount').hide();

						if (data.msg != '') {
							$('#sagepay_direct_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
						}
					}
					if (data.error == true) {
						alert(data.msg);
						$('#button-void').show();
					}

					$('#img_loading_void').hide();
				}
			});
		}
	});
	$("#button-release").click(function() {
		if (confirm('{{ text_confirm_release }}')) {
			$.ajax({
				type: 'POST',
				dataType: 'json',
				data: {'order_id': {{ order_id }}, 'amount': $('#release_amount').val()},
				url: 'index.php?route=extension/payment/sagepay_direct/release&user_token={{ user_token }}',
				beforeSend: function() {
					$('#button-release').hide();
					$('#release_amount').hide();
					$('#img_loading_release').show();
					$('#sagepay_direct_transaction_msg').hide();
				},
				success: function(data) {
					if (data.error == false) {
						html = '';
						html += '<tr>';
						html += '<td class="text-left">' + data.data.date_added + '</td>';
						html += '<td class="text-left">{{ text_payment }}</td>';
						html += '<td class="text-left">' + data.data.amount + '</td>';
						html += '</tr>';

						$('#sagepay_direct_transactions').append(html);
						$('#sagepay_direct_total_released').text(data.data.total);

						if (data.data.release_status == 1) {
							$('#button-void').hide();
							$('.release_text').text('{{ text_yes }}');
						} else {
							$('#button-release').show();
							$('#release_amount').val(0.00);

							{% if auto_settle == 2 %}
								$('#release_amount').show();
							{% endif %}
						}

						if (data.msg != '') {
							$('#sagepay_direct_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
						}

						$('#button-rebate').show();
						$('#rebate_amount').val(0.00).show();
					}
					if (data.error == true) {
						alert(data.msg);
						$('#button-release').show();
						$('#release_amount').show();
					}

					$('#img_loading_release').hide();
				}
			});
		}
	});
	$("#button-rebate").click(function() {
		if (confirm('{{ text_confirm_rebate }}')) {
			$.ajax({
				type: 'POST',
				dataType: 'json',
				data: {'order_id': {{ order_id }}, 'amount': $('#rebate_amount').val()},
				url: 'index.php?route=extension/payment/sagepay_direct/rebate&user_token={{ user_token }}',
				beforeSend: function() {
					$('#button-rebate').hide();
					$('#rebate_amount').hide();
					$('#img_loading_rebate').show();
					$('#sagepay_direct_transaction_msg').hide();
				},
				success: function(data) {
					if (data.error == false) {
						html = '';
						html += '<tr>';
						html += '<td class="text-left">' + data.data.date_added + '</td>';
						html += '<td class="text-left">{{ text_rebate }}</td>';
						html += '<td class="text-left">' + data.data.amount + '</td>';
						html += '</tr>';

						$('#sagepay_direct_transactions').append(html);
						$('#sagepay_direct_total_released').text(data.data.total_released);

						if (data.data.rebate_status == 1) {
							$('.rebate_text').text('{{ text_yes }}');
						} else {
							$('#button-rebate').show();
							$('#rebate_amount').val(0.00).show();
						}

						if (data.msg != '') {
							$('#sagepay_direct_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
						}
					}
					if (data.error == true) {
						alert(data.msg);
						$('#button-rebate').show();
					}

					$('#img_loading_rebate').hide();
				}
			});
		}
	});
//--></script>