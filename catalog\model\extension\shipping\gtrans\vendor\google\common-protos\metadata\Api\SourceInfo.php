<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/source_info.proto

namespace GPBMetadata\Google\Api;

class SourceInfo
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0adf010a1c676f6f676c652f6170692f736f757263655f696e666f2e7072" .
            "6f746f120a676f6f676c652e61706922380a0a536f75726365496e666f12" .
            "2a0a0c736f757263655f66696c657318012003280b32142e676f6f676c65" .
            "2e70726f746f6275662e416e7942710a0e636f6d2e676f6f676c652e6170" .
            "69420f536f75726365496e666f50726f746f50015a45676f6f676c652e67" .
            "6f6c616e672e6f72672f67656e70726f746f2f676f6f676c65617069732f" .
            "6170692f73657276696365636f6e6669673b73657276696365636f6e6669" .
            "67a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

