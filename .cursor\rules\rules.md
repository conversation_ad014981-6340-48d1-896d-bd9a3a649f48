# قواعد تطوير AYM ERP الشاملة - Enterprise Grade

## القواعد الأساسية للتطوير

### 1. قاعدة القراءة الشاملة (Comprehensive Reading Rule)
- **اقرأ كل ملف سطرًا بسطر بالكامل** قبل أي تعديل أو تحليل
- **لا تعدل أو تحذف أي شيء** إلا بعد التأكد من فهم كل الترابطات (controller/model/view) والذاكرة الدائمة والخطة الموضوعة وما تم فيها
- **ابحث عن الدوال أو استخدام المتغيرات** فقط عند الحاجة، لكن القراءة الأساسية تكون شاملة لكل الأسطر
- **تحرك وفق ترتيب العمود الجانبي** (column_left.php)، وراجع كل وحدة مرتبطة (موديل، كنترولر، twig) بفهم كامل

### 2. قاعدة الاستمرارية والمثابرة (Persistence Rule)
- **لا تتوقف حتى إكمال كل المهام**، وأضف مهام فرعية لأي متطلبات تظهر أثناء التنفيذ
- **اعود لملف taskmemory.md قبل كل مهمة** وحدثه بأي ملاحظات أو قواعد جديدة
- **أي دالة لا تراعي الإعدادات أو الصلاحيات أو الخدمات المركزية الخمس** بنسبة كبيرة سيتم تصحيحها وتعديلها لكن نفهم استخداماتها وما نحتاجه منها حتى يكون التعديل والتصحيح نهائي

### 3. قاعدة التوثيق والمراجعة (Documentation Rule)
- **التوثيق يكون في مجلد newdocs** ونراعي أن نقرأ ما يتعلق بالخطة التي يتم تنفيذها والمهمة الحالية من توثيق أو من الأساسيات وفهمها ضروري
- **راجع الخطة والمتطلبات والتصميم** قبل البدء في أي مهمة
- **وثق أي اكتشافات أو تغييرات مهمة** في الملفات المناسبة

## القواعد التقنية المتقدمة

### 4. قاعدة الخدمات المركزية (Central Services Rule)
- **استخدم central_service_manager.php** كمصدر وحيد للخدمات المركزية الـ5
- **تأكد من التكامل مع:**
  - خدمة اللوج والتدقيق (4 كونترولرز)
  - خدمة الإشعارات (3 كونترولرز)
  - خدمة التواصل الداخلي (4 كونترولرز)
  - خدمة المستندات والمرفقات (4 كونترولرز + 7 جداول)
  - خدمة سير العمل المرئي (8 كونترولرز)

### 5. قاعدة الإعدادات والصلاحيات (Settings & Permissions Rule)
- **استخدم `$this->config->get()`** بدلاً من الأرقام الثابتة في الكود
- **تحقق من الصلاحيات** باستخدام `hasPermission()` للصلاحيات الأساسية و `hasKey()` للصلاحيات المتقدمة
- **راعي نظام الصلاحيات المزدوج** المتقدم في النظام
- **المجموعة 1 لها كل الصلاحيات تلقائياً** (إدارة الشركة)

### 6. قاعدة قاعدة البيانات (Database Rule)
- **راجع minidb.txt** قبل إنشاء أي جداول جديدة
- **استخدم البادئة `cod_`** لجميع الجداول
- **طبق نظام المتوسط المرجح للتكلفة (WAC)** في المخزون
- **أنشئ القيود المحاسبية التلقائية** لكل عملية

### 7. قاعدة MVC والتكامل (MVC Integration Rule)
- **اتبع نمط MVC** بدقة: Controller -> Model -> View
- **استخدم AJAX** لجميع العمليات التفاعلية
- **أرجع JSON responses** موحدة ومتسقة
- **تأكد من التكامل** بين الكونترولر والموديل والـ View

## القواعد الأمنية والجودة

### 8. قاعدة الأمان المتقدم (Advanced Security Rule)
- **تحقق من الصلاحيات** في بداية كل دالة
- **سجل جميع الأنشطة الحساسة** في نظام التدقيق
- **استخدم التشفير** للبيانات الحساسة
- **طبق معايير الأمان** لـ API والجلسات

### 9. قاعدة الأداء والاستجابة (Performance Rule)
- **تحميل أي شاشة في أقل من 3 ثوان**
- **استجابة الإشعارات في أقل من ثانية واحدة**
- **دعم 10000+ مستخدم متزامن**
- **استخدم الفهارس المحسنة** لقاعدة البيانات

### 10. قاعدة Enterprise Grade Quality
- **كل شاشة يجب أن تنافس SAP/Oracle/Microsoft/Odoo**
- **دعم البيع عبر الإنترنت والفروع الفعلية**
- **دعم المنتجات ذات الوحدات المتنوعة**
- **تكامل مع الأنظمة الخارجية** (الضرائب المصرية، بوابات الدفع، شركات الشحن)

## القواعد الخاصة بالمراجعة الشاملة

### 11. قاعدة مراجعة الشاشات (Screen Review Rule)
عند مراجعة أي شاشة، يجب الإجابة على:
1. **ما الذي نتوقعه من هذه الشاشة** وفق منافسينا الأقوياء (SAP, Oracle, Microsoft, Odoo, Shopify, Magento, WooCommerce)؟
2. **هل الوظائف الموجودة كافية** أم أن هناك نواقص؟
3. **هل هناك تعارض مع شاشات أخرى** أو نواقص في التكامل؟
4. **هل الشاشة مكتملة وتتوافق مع قاعدة البيانات** وترتبط بالخدمات المركزية والصلاحيات والإعدادات؟

### 12. قاعدة التكامل الشامل (Full Integration Rule)
- **كل شاشة يجب أن تتكامل مع الخدمات المركزية الـ5**
- **كل عملية يجب أن تنشئ القيود المحاسبية المناسبة**
- **كل تحديث في المخزون يجب أن يتبع نظام WAC**
- **كل نشاط يجب أن يُسجل في نظام التدقيق**

### 13. قاعدة الهيدر المتكامل (Integrated Header Rule)
- **كل صفحة يجب أن تعرض الهيدر المتكامل** مع الإشعارات
- **عداد الإشعارات يجب أن يتحدث في الوقت الفعلي**
- **لوحة الإشعارات يجب أن تحتوي على 4 تبويبات**: الإشعارات، الرسائل، المهام، الموافقات
- **قائمة التواصل السريع** مع المستخدمين المتصلين

### 14. قاعدة نظام المستندات المعقد (Complex Documents Rule)
- **استخدم الجداول الـ7 المتخصصة** للمستندات
- **طبق نظام الصلاحيات المتقدم** للمستندات
- **دعم إدارة الإصدارات** والموافقات
- **تكامل مع نظام البحث المتقدم**

## القواعد الاستراتيجية

### 15. قاعدة التفوق على المنافسين (Competitive Advantage Rule)
- **قارن كل ميزة مع المنافسين الأقوياء**
- **طور أدوات نقل البيانات** من الأنظمة المنافسة
- **اجعل الانتقال إلى AYM ERP سهلاً وسلساً**
- **قدم ميزات أفضل وأكثر سهولة في الاستخدام**

### 16. قاعدة SaaS والاستقلالية (SaaS Independence Rule)
- **صمم النظام ليكون قابلاً للفصل كـ SaaS platform**
- **دعم Multi-tenant architecture**
- **طور نظام Subscription Management**
- **اجعل النظام مستقلاً وقابلاً للنشر على الكلاود**

### 17. قاعدة الذكاء الاصطناعي (AI Integration Rule)
- **كل شاشة يجب أن تقدم اقتراحات ذكية**
- **طور نظام التنبؤ بالمبيعات والمخزون**
- **اضف أتمتة للعمليات المتكررة**
- **قدم رؤى ذكية وتوصيات**

### 18. قاعدة التجارة الإلكترونية المتكاملة (Integrated E-commerce Rule)
- **دمج كامل بين ERP والمتجر الإلكتروني**
- **دعم البيع عبر قنوات متعددة**
- **تكامل مع منصات التجارة الإلكترونية الشائعة**
- **واجهة متجر احترافية تنافس Shopify وMagento**

## قواعد التنفيذ والمتابعة

### 19. قاعدة تحديث المهام (Task Update Rule)
- **حدث حالة المهام** في tasks.md عند البدء والانتهاء
- **اتبع الترتيب المنطقي** للمهام في الخطة
- **لا تنتقل للمهمة التالية** حتى اكتمال الحالية
- **وثق أي تغييرات أو اكتشافات** مهمة

### 20. قاعدة الجودة النهائية (Final Quality Rule)
- **اختبر كل وظيفة قبل اعتبارها مكتملة**
- **تأكد من التوافق مع جميع المتصفحات**
- **اختبر الاستجابة على الأجهزة المختلفة**
- **راجع الأمان والأداء** قبل الإطلاق

---

## ملاحظة مهمة

هذه القواعد **إلزامية وغير قابلة للتفاوض**. أي انحراف عنها يعتبر فشلاً في المهمة. الهدف هو إنشاء أقوى نظام ERP في مصر والشرق الأوسط، يتفوق على جميع المنافسين ويوفر تجربة استخدام استثنائية للشركات التجارية المصرية.

**نحن نسعى لـ Enterprise Grade Quality - لا مجال للتساهل أو النقص.**