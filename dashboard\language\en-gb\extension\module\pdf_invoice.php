<?php
// Heading
$_['heading_title']    = 'PDF Invoice - ********';
$_['heading_name']     = 'PDF Invoice';
$_['heading_module']   = 'Edit Module';

$_['text_ltr']         = 'LTR (left to right)';
$_['text_rtl']         = 'RTL (right to left)';

$_['text_module']      = 'Modules';
$_['text_success']     = 'Success: You have modified \'PDF Invoice\' module!';

$_['text_help_each_page']      = 'Added to each page';
$_['text_help_extra_page']     = 'Added as extra page';
$_['text_help_invoice_after']  = 'Added below order table';

$_['text_shipping_method']     = 'Shipping Method:';
$_['text_payment_method']      = 'Payment Method:';
$_['text_payment_address']     = 'Payment Address';
$_['text_shipping_address']    = 'Shipping Address';
$_['text_date_added']          = 'Date:';
$_['text_order_id']            = 'Order ID:';
$_['text_order_status']        = 'Order Status:';
$_['text_help_complete']       = 'If order status is complete';
$_['text_help_download']       = 'Add download button to customer \'My Orders\'.';
$_['text_invoice_no']          = 'Invoice No.:';
$_['text_help_fonts_download'] = 'More font can be downloaded from <a href="https://github.com/tecnickcom/TCPDF" target="_blank" rel="noopener">tcpdf</a>. Upload to: system/library/shared/tcpdf/fonts';

$_['text_paging'] = 'Page %s of %s';

$_['tab_content'] = 'Content';
$_['tab_setting'] = 'Settings';

// Column
$_['column_total']             = 'Total';
$_['column_product']           = 'Product';
$_['column_model']             = 'Model';
$_['column_quantity']          = 'Quantity';
$_['column_price']             = 'Price';

// Entry
$_['button_preview']  = 'Preview PDF';
$_['entry_admin']     = '<span title="Attach invoice to admin order mail" data-toggle="tooltip">Admin Order Mail</span>';
$_['entry_after']     = '<span title="Added directly after order table" data-toggle="tooltip">After</span>';
$_['entry_append']    = '<span title="Content added to separate page at the end" data-toggle="tooltip">Append Page</span>';
$_['entry_attach']    = '<span title="Attach invoice to customer order mail" data-toggle="tooltip">Customer Order Mail</span>';
$_['entry_barcode']   = '<span title="Attach to order mail" data-toggle="tooltip">Barcode</span>';
$_['entry_before']    = '<span title="Added directly before order table" data-toggle="tooltip">Before</span>';
$_['entry_border']    = 'Border Color';
$_['entry_color']     = 'Header Background';
$_['entry_complete']  = '<span title="Invoice only available if order status is complete" data-toggle="tooltip">Order Complete</span>';
$_['entry_direction'] = 'Text Direction';
$_['entry_download']  = '<span title="Add button to download invoice from order information page" data-toggle="tooltip">Customer Download</span>';
$_['entry_font']      = '<span title="Select dejavusans if you require special characters as UTF8" data-toggle="tooltip">Font</span>';
$_['entry_font_size'] = 'Font Size';
$_['entry_footer']    = '<span title="Added to the footer of the last page" data-toggle="tooltip">Footer</span>';
$_['entry_header']    = '<span title="Added to the header on the first page" data-toggle="tooltip">Header</span>';
$_['entry_height']    = 'Height';
$_['entry_image']     = 'Product Image';
$_['entry_logo']      = 'Logo';
$_['entry_paging']    = '<span title="Display page numbering e.g \'2 of 4\'" data-toggle="tooltip">Paging</span>';
$_['entry_prepend']   = '<span title="Content added to separate page at the start" data-toggle="tooltip">Prepend Page</span>';
$_['entry_status']    = 'Status';
$_['entry_title']     = 'Title';
$_['entry_width']     = 'Width';

// Error
$_['error_permission'] = 'Warning: You do not have permission to modify \'PDF Invoice\' module!';
$_['error_font']       = 'Warning: You must download %s font from <a href="https://github.com/tecnickcom/TCPDF" target="_blank" rel="noopener">github</a> and upload to: /system/library/shared/tcpdf/fonts/';
