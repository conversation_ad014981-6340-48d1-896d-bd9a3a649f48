<h2>{{ text_payment_info }}</h2>
<div class="alert alert-success" id="firstdata_transaction_msg" style="display: none;"></div>
<table class="table table-striped table-bordered">
  <tr>
    <td>{{ text_order_ref }}</td>
    <td>{{ firstdata_order.order_ref }}</td>
  </tr>
  <tr>
    <td>{{ text_order_total }}</td>
    <td>{{ firstdata_order.total_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_total_captured }}</td>
    <td id="firstdata_total_captured">{{ firstdata_order.total_captured_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_capture_status }}</td>
    <td id="capture_status">{% if firstdata_order.capture_status == 1 %}
      <span class="capture_text">{{ text_yes }}</span>
      {% else %}
      <span class="capture_text">{{ text_no }}</span>&nbsp;&nbsp;
      {% if firstdata_order.void_status == 0 %}
      <a class="btn btn-primary" id="button-capture">{{ button_capture }}</a> <span class="btn btn-primary" id="img_loading_capture" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_void_status }}</td>
    <td id="void_status">{% if firstdata_order.void_status == 1 %}
      <span class="void_text">{{ text_yes }}</span>
      {% else %}
      <span class="void_text">{{ text_no }}</span>&nbsp;&nbsp;
      {% if firstdata_order.capture_status == 0 %}
      <a class="btn btn-primary" id="button-void">{{ button_void }}</a> <span class="btn btn-primary" id="img_loading_void" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
      {% endif %}></td>
  </tr>
  <tr>
    <td>{{ text_refund_status }}</td>
    <td id="refund_status">{% if firstdata_order.refund_status == 1 %}
      <span class="refund_text">{{ text_yes }}</span>
      {% else %}
      <span class="refund_text">{{ text_no }}</span>&nbsp;&nbsp;
      {% if firstdata_order.capture_status == 0 or firstdata_order.void_status == 1 %}
      <a class="btn btn-primary" id="button-refund" style="display:none;">{{ button_refund }}</a>
      {% else %}
      <a class="btn btn-primary" id="button-refund">{{ button_refund }}</a>
      {% endif %}
      <span class="btn btn-primary" id="img_loading_refund" style="display:none;">
        <i class="fa fa-circle-o-notch fa-spin fa-lg"></i>
      </span>
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_transactions }}:</td>
    <td><table class="table table-striped table-bordered" id="firstdata_transactions">
        <thead>
          <tr>
            <td class="text-left"><strong>{{ text_column_date_added }}</strong></td>
            <td class="text-left"><strong>{{ text_column_type }}</strong></td>
            <td class="text-left"><strong>{{ text_column_amount }}</strong></td>
          </tr>
        </thead>
        <tbody>
          {% for transaction in firstdata_order.transactions %}
          <tr>
            <td class="text-left">{{ transaction.date_added }}</td>
            <td class="text-left">{{ transaction.type }}</td>
            <td class="text-left">{{ transaction.amount }}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table></td>
  </tr>
</table>
<script type="text/javascript"><!--
$('#button-void').bind('click', function () {
	if (confirm('{{ text_confirm_void }}')) {
		$.ajax({
			type:'post',
			dataType: 'json',
			data: 'order_id={{ order_id }}',
			url: 'index.php?route=extension/payment/firstdata_remote/void&user_token={{ user_token }}',
			beforeSend: function() {
				$('#button-void').hide();
				$('#img_loading_void').show();
				$('#firstdata_transaction_msg').hide();
			},
			success: function(data) {
				if (data['error'] == false) {
					html = '';
					html += '<tr>';
					html += '<td class="text-left">' + data.data.date_added + '</td>';
					html += '<td class="text-left">{{text_void }}</td>';
					html += '<td class="text-left">0.00</td>';
					html += '</tr>';

					$('. void_text').text('{{ text_yes }}');

					$('#firstdata_transactions').append(html);

					$('#button-capture').hide();

					if (data.msg != '') {
						$('#firstdata_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data['msg']).fadeIn();
					}
				}

				if (data['error'] == true) {
					alert(data['msg']);

					$('#button-void').show();
				}

				$('#img_loading_void').hide();
			}
		});
	}
});

$('#button-capture').bind('click', function () {
	if (confirm('{{ text_confirm_capture }}')) {
		$.ajax({
		type:'POST',
		dataType: 'json',
		data: {'order_id': {{ order_id }} },
		url: 'index.php?route=extension/payment/firstdata_remote/capture&user_token={{ user_token }}',
		beforeSend: function() {
		$('#button-capture').hide();
		$('#img_loading_capture').show();
		$('#firstdata_transaction_msg').hide();
		},
		success: function(data) {
		if (data.error == false) {
		html = '';
		html += '<tr>';
		html += '<td class="text-left">' + data.data.date_added + '</td>';
		html += '<td class="text-left"{{ text_payment }}</td>';
		html += '<td class="text-left">' + data.data.amount + '</td>';
		html += '</tr>';

		$('#firstdata_transactions').append(html);
		$('#firstdata_total_captured').text(data.data.total_formatted);

		if (data.data.capture_status == 1) {
		$('#button-void').hide();
		$('#button-refund').show();
		$(' . capture_text').text('{{ text_yes }}');
		} else {
		$('#button-capture').show();
		}

		if (data.msg != '') {
		$('#firstdata_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
		}
		}
		if (data.error == true) {
		alert(data.msg);
		$('#button-capture').show();
		}

		$('#img_loading_capture').hide();
		}
		});
	}
});

$('#button-refund').bind('click', function () {
	if (confirm('{{ text_confirm_refund }}')) {
		$.ajax({
		type:'POST',
		dataType: 'json',
		data: {'order_id': {{ order_id }} },
		url: 'index.php?route=extension/payment/firstdata_remote/refund&user_token={{ user_token }}',
		beforeSend: function() {
		$('#button-refund').hide();
		$('#img_loading_refund').show();
		$('#firstdata_transaction_msg').hide();
		},
		success: function(data) {
		if (data.error == false) {
		html = '';
		html += '<tr>';
		html += '<td class="text-left">' + data.data.date_added + '</td>';
		html += '<td class="text-left">{{ text_refund }}</td>';
		html += '<td class="text-left">' + data.data.amount + '</td>';
		html += '</tr>';

		$('#firstdata_transactions').append(html);
		$('#firstdata_total_captured').text(data.data.total_captured);

		if (data.data.refund_status == 1) {
		$(' . refund_text').text('{{ text_yes }}');
		} else {
		$('#button-refund').show();
		}

		if (data.msg != '') {
		$('#firstdata_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
		}
		}
		if (data.error == true) {
		alert(data.msg);
		$('#button-refund').show();
		}

		$('#img_loading_refund').hide();
		}
		});
	}
});
//--></script>
