{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Advanced Financial Reports -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --financial-color: #8e44ad;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.financial-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.financial-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--financial-color), var(--primary-color), var(--secondary-color));
}

.financial-header {
    text-align: center;
    border-bottom: 3px solid var(--financial-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.financial-header h2 {
    color: var(--financial-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.financial-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.financial-summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.financial-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.financial-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.financial-summary-card.revenue::before { background: var(--success-color); }
.financial-summary-card.expenses::before { background: var(--danger-color); }
.financial-summary-card.profit::before { background: var(--info-color); }
.financial-summary-card.ratios::before { background: var(--financial-color); }

.financial-summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.financial-summary-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.financial-summary-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-revenue .amount { color: var(--success-color); }
.card-expenses .amount { color: var(--danger-color); }
.card-profit .amount { color: var(--info-color); }
.card-ratios .amount { color: var(--financial-color); }

.financial-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.financial-table th {
    background: linear-gradient(135deg, var(--financial-color), #7d3c98);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.financial-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.financial-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.financial-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

.financial-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.financial-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Chart Container */
.chart-container {
    background: #ffffff;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.chart-container canvas {
    max-height: 400px;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

/* RTL Support */
[dir="rtl"] .financial-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .financial-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .financial-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .financial-table {
        font-size: 0.8rem;
    }
    
    .financial-table th,
    .financial-table td {
        padding: 8px 6px;
    }
    
    .financial-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .financial-actions {
        flex-direction: column;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateReport()" 
                  data-bs-toggle="tooltip" title="{{ text_generate_report }}">
            <i class="fas fa-chart-line me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printReport()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-primary" onclick="showAnalysis()"
                  data-bs-toggle="tooltip" title="{{ text_financial_analysis }}">
            <i class="fas fa-analytics"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show">
      <i class="fas fa-check-circle"></i>
      {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_report_filters }}</h4>
      <form id="financial-reports-form" method="post">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="report_type" class="form-label">{{ entry_report_type }}</label>
              <select name="report_type" id="report_type" class="form-control select2" required>
                <option value="">{{ text_select_report_type }}</option>
                <option value="comprehensive">{{ text_comprehensive }}</option>
                <option value="income_statement">{{ text_income_statement }}</option>
                <option value="balance_sheet">{{ text_balance_sheet }}</option>
                <option value="cash_flow">{{ text_cash_flow }}</option>
                <option value="equity_changes">{{ text_equity_changes }}</option>
                <option value="financial_ratios">{{ text_financial_ratios }}</option>
                <option value="performance_analysis">{{ text_performance_analysis }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="date_start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="date_end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="comparison_period" class="form-label">{{ entry_comparison_period }}</label>
              <select name="comparison_period" id="comparison_period" class="form-control">
                <option value="none">{{ text_none }}</option>
                <option value="previous_month">{{ text_previous_month }}</option>
                <option value="previous_quarter">{{ text_previous_quarter }}</option>
                <option value="previous_year">{{ text_previous_year }}</option>
                <option value="budget">{{ text_budget }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-search me-2"></i>{{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Financial Reports Content -->
    {% if report_data %}
    <!-- Summary Cards -->
    <div class="financial-summary-cards">
      <div class="financial-summary-card card-revenue revenue">
        <h4>{{ text_total_revenue }}</h4>
        <div class="amount">{{ report_data.summary.total_revenue_formatted }}</div>
        <div class="description">{{ text_revenue }}</div>
      </div>
      <div class="financial-summary-card card-expenses expenses">
        <h4>{{ text_total_expenses }}</h4>
        <div class="amount">{{ report_data.summary.total_expenses_formatted }}</div>
        <div class="description">{{ text_expenses }}</div>
      </div>
      <div class="financial-summary-card card-profit profit">
        <h4>{{ text_net_profit }}</h4>
        <div class="amount">{{ report_data.summary.net_profit_formatted }}</div>
        <div class="description">{{ report_data.summary.profit_margin|number_format(2) }}%</div>
      </div>
      <div class="financial-summary-card card-ratios ratios">
        <h4>{{ text_financial_ratios }}</h4>
        <div class="amount">{{ report_data.summary.key_ratios_count }}</div>
        <div class="description">{{ text_ratios }}</div>
      </div>
    </div>

    <!-- Financial Report Table -->
    <div class="financial-container">
      <div class="financial-header">
        <h2>{{ text_financial_report_details }}</h2>
      </div>

      <div class="table-responsive">
        <table class="financial-table" id="financial-report-table">
          <thead>
            <tr>
              <th>{{ column_account }}</th>
              <th>{{ column_current_period }}</th>
              {% if comparison_period != 'none' %}
              <th>{{ column_previous_period }}</th>
              <th>{{ column_variance }}</th>
              <th>{{ column_percentage }}</th>
              {% endif %}
              <th>{{ column_ratio }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_action }}</th>
            </tr>
          </thead>
          <tbody>
            {% for item in report_data.items %}
            <tr data-account-id="{{ item.account_id }}">
              <td>
                <strong>{{ item.account_name }}</strong>
                <br>
                <small class="text-muted">{{ item.account_code }}</small>
              </td>
              <td class="amount-cell">
                <strong class="{% if item.current_amount >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ item.current_amount_formatted }}
                </strong>
              </td>
              {% if comparison_period != 'none' %}
              <td class="amount-cell">
                <span class="{% if item.previous_amount >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ item.previous_amount_formatted }}
                </span>
              </td>
              <td class="amount-cell">
                <span class="{% if item.variance >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ item.variance_formatted }}
                </span>
              </td>
              <td class="amount-cell">
                <span class="{% if item.variance_percentage >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ item.variance_percentage|number_format(2) }}%
                </span>
              </td>
              {% endif %}
              <td class="amount-cell">
                {% if item.ratio %}
                  {{ item.ratio|number_format(2) }}
                {% else %}
                  -
                {% endif %}
              </td>
              <td>
                <span class="badge bg-{% if item.status == 'good' %}success{% elseif item.status == 'warning' %}warning{% else %}danger{% endif %}">
                  {{ item.status }}
                </span>
              </td>
              <td>
                <div class="financial-actions">
                  <button type="button" class="btn btn-outline-info btn-sm"
                          onclick="viewAccountDetails({{ item.account_id }})"
                          data-bs-toggle="tooltip" title="{{ text_view_details }}">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-warning btn-sm"
                          onclick="analyzeAccount({{ item.account_id }})"
                          data-bs-toggle="tooltip" title="{{ text_analyze }}">
                    <i class="fas fa-chart-line"></i>
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="row">
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_revenue_trend_chart }}</h4>
          <canvas id="revenueTrendChart"></canvas>
        </div>
      </div>
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_profitability_chart }}</h4>
          <canvas id="profitabilityChart"></canvas>
        </div>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Advanced Financial Reports
class FinancialReportsManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeCharts();
        this.initializeSelect2();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeDataTable() {
        const table = document.getElementById('financial-report-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[1, 'desc']], // Sort by current period desc
                columnDefs: [
                    { targets: [1, 2, 3, 4, 5], className: 'text-end' },
                    { targets: [7], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printReport();
                        break;
                    case 'a':
                        e.preventDefault();
                        this.showAnalysis();
                        break;
                }
            }
        });
    }

    initializeCharts() {
        if (typeof Chart !== 'undefined') {
            this.createRevenueTrendChart();
            this.createProfitabilityChart();
        }
    }

    initializeSelect2() {
        if (typeof $ !== 'undefined' && $.fn.select2) {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        }
    }

    generateReport() {
        const form = document.getElementById('financial-reports-form');
        const formData = new FormData(form);

        this.showLoadingState(true);

        fetch('{{ generate_url }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_report_generated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate_report }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate_report }}: ' + error.message, 'danger');
        });
    }

    exportReport(format) {
        const params = new URLSearchParams({
            format: format,
            report_type: document.getElementById('report_type').value,
            date_start: document.getElementById('date_start').value,
            date_end: document.getElementById('date_end').value,
            comparison_period: document.getElementById('comparison_period').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printReport() {
        window.print();
    }

    showAnalysis() {
        window.open('{{ analysis_url }}', '_blank');
    }

    viewAccountDetails(accountId) {
        window.open('{{ url_link('accounts/account_statement_advanced', 'view') }}&account_id=' + accountId, '_blank');
    }

    analyzeAccount(accountId) {
        window.open('{{ analysis_url }}&account_id=' + accountId, '_blank');
    }

    createRevenueTrendChart() {
        const ctx = document.getElementById('revenueTrendChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ trend_labels|json_encode|raw }},
                datasets: [{
                    label: '{{ text_revenue }}',
                    data: {{ revenue_trend|json_encode|raw }},
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_revenue_trend_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createProfitabilityChart() {
        const ctx = document.getElementById('profitabilityChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['{{ text_gross_margin }}', '{{ text_net_margin }}', '{{ text_operating_margin }}'],
                datasets: [{
                    data: {{ profitability_ratios|json_encode|raw }},
                    backgroundColor: ['#8e44ad', '#3498db', '#e74c3c'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_profitability_chart }}'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateReport()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> {{ text_loading }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-chart-line me-2"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateReport() {
    financialReportsManager.generateReport();
}

function exportReport(format) {
    financialReportsManager.exportReport(format);
}

function printReport() {
    financialReportsManager.printReport();
}

function showAnalysis() {
    financialReportsManager.showAnalysis();
}

function viewAccountDetails(accountId) {
    financialReportsManager.viewAccountDetails(accountId);
}

function analyzeAccount(accountId) {
    financialReportsManager.analyzeAccount(accountId);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.financialReportsManager = new FinancialReportsManager();
});
</script>

{{ footer }}
