{% if (orders_iteration == 1) %}
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tbody>
    <tr>
      <td align="left" valign="top">
        <b style="font-size: 120%;">
          {% if (order['store_url']) %}<a href="{{ order['store_url'] }}" style="text-decoration:none; color:#000000" target="_blank">{{ store['config_name'] }}</a>{% else %}{{ store['config_name'] }}{% endif %}</b>
        {% if (store['config_address']) %}
          <br />{{ store['config_address'] }}
        {% endif %}
        {% if (store['config_telephone']) %}
          <br />{{ store['config_telephone'] }}
        {% endif %}
        {% if (store['config_email']) %}
          <br /><a href="mailto:{{ store['config_email'] }}" style="text-decoration:none; color:#000000" target="_blank">{{ store['config_email'] }}</a>
        {% endif %}
      </td>
      <td align="right" valign="top">
        {% if (store['config_logo']) %}
          <img alt="" src="{{ store['config_logo'] }}" />
        {% endif %}
      </td>
    </tr>
    </tbody>
  </table>
{% endif %}
<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tbody>
  <tr>
    <td>
      <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tbody>
        <tr>
          <td width="50%">
            <table border="0" cellpadding="0" cellspacing="0">
              <tbody>
              {% if (order['order_status']) %}
                <tr>
                  <td style="text-align:right;">{{ order['order_status'] }}</td>
                  <td width="120" style="text-align:right;"><b>{{ text_order_status }}</b></td>
                </tr>
              {% endif %}
              <tr>
                <td style="text-align:right;">{{ order['payment_method'] }}</td>
                <td width="120" style="text-align:right;"><b>{{ text_payment_method }}</b></td>
              </tr>
              {% if (order['shipping_address']) %}
                <tr>
                  <td style="text-align:right;">{{ order['shipping_method'] }}</td>
                  <td width="120" style="text-align:right;"><b>{{ text_shipping_method }}</b></td>
                </tr>
              {% endif %}
              </tbody>
            </table>
          </td>
          <td width="50%">
            <table border="0" cellpadding="0" cellspacing="0">
              <tbody>
              <tr>
                <td style="text-align:right;">{{ order['date_added'] }}</td>
                <td width="90" style="text-align:right;"><b>{{ text_date_added }}</b></td>
              </tr>
              {% if (order['invoice_no']) %}
                <tr>
                  <td style="text-align:right;">{{ order['invoice_prefix'] ~ order['invoice_no'] }}</td>
                  <td width="90" style="text-align:right;"><b>{{ text_invoice_no }}</b></td>
                </tr>
              {% endif %}
              <tr>
                <td style="text-align:right;">{{ order['order_id'] }}</td>
                <td width="90" style="text-align:right;"><b>{{ text_order_id }}</b></td>
              </tr>
              {% if (order['custom_field'] is not empty) %}
                {% for custom_field in order['custom_field'] %}
                  {% if (custom_field['value']) %}
                    <tr>
                      <td style="text-align:right;">{{ custom_field['value'] }}</td>
                      <td width="120" style="text-align:right;"><b>:{{ custom_field['name'] }}</b></td>
                    </tr>
                  {% endif %}
                {% endfor %}
              {% endif %}
              </tbody>
            </table>
          </td>
        </tr>
        {% if (config['module_pdf_invoice_comment'] and order['comment']) %}
          <tr>
            <td colspan="2">
              <table border="0" cellpadding="0" cellspacing="0">
                <tbody>
                <tr>
                  <td width="90"><b>{{ text_comment }}</b></td>
                  <td>{{ order['comment'] }}</td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
        {% endif %}
        </tbody>
      </table>
    </td>
  </tr>
  {% if (order['shipping_address'] or order['payment_address']) %}
    <tr>
      <td colspan="2" height="8">&nbsp;</td>
    </tr>
    <tr>
      <td colspan="2">
        <table border="0" cellpadding="5" cellspacing="0" width="100%" style="border-collapse: collapse;">
          <tbody>
          <tr>
            <td bgcolor="{{ config['module_pdf_invoice_color'] }}" style="border:0.7px solid #969696;line-height:15pt;font-size:9pt;color:#ffffff;text-align:center;"{% if (order['shipping_address']) %} width="50%"{% endif %}><b>{{ text_payment_address }}</b></td>
            {% if (order['shipping_address']) %}
              <td bgcolor="{{ config['module_pdf_invoice_color'] }}" style="border:0.7px solid #969696;line-height: 15pt; font-size:9pt;color:#ffffff;text-align:center;" width="50%"><b>{{ text_shipping_address }}</b></td>
            {% endif %}
          </tr>
          <tr>
            <td style="border:0.7px solid #969696;text-align:right;">
              <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                <tr>
                  <td>{{ order['payment_address'] }}</td>
                </tr>
                {% if (order['payment_custom_field'] is not empty) %}
                  {% for custom_field in order['payment_custom_field'] %}
                    {% if (custom_field['value']) %}
                      <tr>
                        <td style="text-align:right;">{{ custom_field['value'] }} <b>{{ custom_field['name'] }}</b></td>
                      </tr>
                    {% endif %}
                  {% endfor %}
                {% endif %}
              </table>
            </td>
            {% if (order['shipping_address']) %}
              <td width="50%" style="border:0.7px solid #969696;text-align:right;">
                <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
                  <tr>
                    <td>{{ order['shipping_address'] }}</td>
                  </tr>
                  {% if (order['shipping_custom_field'] is not empty) %}
                    {% for custom_field in order['shipping_custom_field'] %}
                      {% if (custom_field['value']) %}
                        <tr>
                          <td style="text-align:right;">{{ custom_field['value'] }} <b>{{ custom_field['name'] }}</b></td>
                        </tr>
                      {% endif %}
                    {% endfor %}
                  {% endif %}
                </table>
              </td>
            {% endif %}
          </tr>
          </tbody>
        </table>
      </td>
    </tr>
  {% endif %}
  </tbody>
</table>
<br />