{{ header }}{{ column_left }}
<div id="content">
<style>
/* تحسينات متقدمة لتقرير الضرائب - Enterprise Grade */
.panel-default {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.panel-default:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.panel-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    padding: 15px;
}

/* تحسين الجدول */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.table thead th {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين القيم الضريبية */
.vat-sales {
    color: #28a745;
    font-weight: 600;
}

.vat-purchases {
    color: #dc3545;
    font-weight: 600;
}

.net-vat {
    color: #007bff;
    font-weight: 700;
    font-size: 1.1em;
}

.vat-payable {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    font-weight: 700;
}

.vat-refundable {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    font-weight: 700;
}

/* تحسين الفلاتر */
.form-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-group {
    flex-grow: 1;
    margin-bottom: 10px;
    margin-right: 10px;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: scale(1.02);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.2s ease;
    border-radius: 6px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    border: none;
}

/* تحسين ملخص الضرائب */
.vat-summary {
    background: linear-gradient(135deg, #f1f8ff 0%, #e6f3ff 100%);
    border-left: 4px solid #007bff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.vat-summary h4 {
    color: #0056b3;
    margin-top: 0;
    font-weight: 600;
}

/* تحسين الأقسام */
.section-header {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    padding: 10px 15px;
    font-weight: 600;
    border-left: 4px solid #6c757d;
}

.sales-section {
    border-left: 4px solid #28a745;
}

.purchases-section {
    border-left: 4px solid #dc3545;
}

.summary-section {
    border-left: 4px solid #007bff;
}

/* تحسين الرسم البياني */
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    margin-bottom: 20px;
}

/* تحسين بطاقات الضرائب */
.vat-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.vat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.vat-card-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.vat-amount {
    font-size: 1.5em;
    font-weight: 700;
    text-align: center;
}
</style>

<div class="page-header">
    <div class="container-fluid">
        <div class="pull-right">
            <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-vat').toggle();" class="btn btn-default"><i class="fa fa-filter"></i></button>
            <button type="button" data-toggle="tooltip" title="{{ button_export }}" onclick="exportVATReport('excel')" class="btn btn-success"><i class="fa fa-download"></i></button>
            <button type="button" data-toggle="tooltip" title="{{ button_print }}" onclick="printVATReport()" class="btn btn-info"><i class="fa fa-print"></i></button>
        </div>
        <h1>{{ heading_title }}</h1>
        <ul class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
            <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="container-fluid">
    <div class="panel panel-default">
        <div class="panel-header">
            <h3 class="panel-title"><i class="fa fa-percent"></i> تقرير ضريبة القيمة المضافة</h3>
        </div>
        
        <!-- ملخص الضرائب -->
        <div class="vat-summary">
            <h4><i class="fa fa-chart-pie"></i> ملخص الضرائب للفترة</h4>
            <div class="row">
                <div class="col-md-3">
                    <div class="vat-card">
                        <div class="vat-card-title">ضريبة المبيعات</div>
                        <div class="vat-amount vat-sales">{{ vat_sales }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="vat-card">
                        <div class="vat-card-title">ضريبة المشتريات</div>
                        <div class="vat-amount vat-purchases">{{ vat_purchases }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="vat-card">
                        <div class="vat-card-title">صافي الضريبة</div>
                        <div class="vat-amount net-vat">{{ net_vat }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="vat-card">
                        <div class="vat-card-title">الحالة</div>
                        <div class="vat-amount">
                            {% if net_vat_value > 0 %}
                            <span class="vat-payable">مستحقة</span>
                            {% else %}
                            <span class="vat-refundable">مستردة</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div id="filter-vat" class="well" style="display: none;">
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="control-label" for="input-date-start">{{ entry_date_start }}</label>
                        <div class="input-group date">
                            <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ entry_date_start }}" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="control-label" for="input-date-end">{{ entry_date_end }}</label>
                        <div class="input-group date">
                            <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ entry_date_end }}" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                    <button type="button" id="button-filter" class="btn btn-primary pull-right"><i class="fa fa-search"></i> {{ button_filter }}</button>
                </div>
            </div>
        </div>

        <!-- الرسم البياني -->
        <div class="chart-container">
            <canvas id="vatChart" width="400" height="200"></canvas>
        </div>

        <!-- جدول التفاصيل -->
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <td class="text-left">{{ column_description }}</td>
                        <td class="text-right">{{ column_amount }}</td>
                        <td class="text-right">{{ column_vat_rate }}</td>
                        <td class="text-right">{{ column_vat_amount }}</td>
                    </tr>
                </thead>
                <tbody>
                    <!-- قسم المبيعات -->
                    <tr class="section-header sales-section">
                        <td colspan="4"><strong>ضريبة المبيعات</strong></td>
                    </tr>
                    {% if sales_items %}
                    {% for item in sales_items %}
                    <tr>
                        <td class="text-left">{{ item.description }}</td>
                        <td class="text-right">{{ item.amount }}</td>
                        <td class="text-right">{{ item.vat_rate }}%</td>
                        <td class="text-right vat-sales">{{ item.vat_amount }}</td>
                    </tr>
                    {% endfor %}
                    {% endif %}
                    
                    <!-- قسم المشتريات -->
                    <tr class="section-header purchases-section">
                        <td colspan="4"><strong>ضريبة المشتريات</strong></td>
                    </tr>
                    {% if purchases_items %}
                    {% for item in purchases_items %}
                    <tr>
                        <td class="text-left">{{ item.description }}</td>
                        <td class="text-right">{{ item.amount }}</td>
                        <td class="text-right">{{ item.vat_rate }}%</td>
                        <td class="text-right vat-purchases">{{ item.vat_amount }}</td>
                    </tr>
                    {% endfor %}
                    {% endif %}
                    
                    <!-- الملخص النهائي -->
                    <tr class="section-header summary-section">
                        <td colspan="3"><strong>صافي الضريبة المستحقة</strong></td>
                        <td class="text-right net-vat">{{ net_vat }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript"><!--
// تحسينات متقدمة لتقرير الضرائب
$(document).ready(function() {
    // إضافة تأثيرات متقدمة للجدول
    $('.table tbody tr').hover(function() {
        $(this).find('td').css('background-color', '#f8f9fa');
    }, function() {
        $(this).find('td').css('background-color', '');
    });
    
    // تحسين عرض القيم الضريبية
    $('.vat-sales').each(function() {
        const value = parseFloat($(this).text().replace(/[^0-9.-]+/g, ''));
        if (value > 0) {
            $(this).addClass('font-weight-bold');
        }
    });
    
    $('.vat-purchases').each(function() {
        const value = parseFloat($(this).text().replace(/[^0-9.-]+/g, ''));
        if (value > 0) {
            $(this).addClass('font-weight-bold');
        }
    });
    
    $('.net-vat').each(function() {
        const value = parseFloat($(this).text().replace(/[^0-9.-]+/g, ''));
        if (value > 0) {
            $(this).addClass('vat-payable');
        } else if (value < 0) {
            $(this).addClass('vat-refundable');
        }
    });
    
    // إضافة مؤشر تحميل للعمليات
    $(document).ajaxStart(function() {
        $('body').addClass('loading');
    }).ajaxStop(function() {
        $('body').removeClass('loading');
    });
    
    // تحسين فلاتر التاريخ
    $('.input-group.date').datetimepicker({
        pickTime: false,
        format: 'YYYY-MM-DD'
    });
    
    // إنشاء الرسم البياني
    if ($('#vatChart').length) {
        const ctx = document.getElementById('vatChart').getContext('2d');
        const vatChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['ضريبة المبيعات', 'ضريبة المشتريات'],
                datasets: [{
                    data: [
                        parseFloat('{{ vat_sales_value|default(0) }}'),
                        parseFloat('{{ vat_purchases_value|default(0) }}')
                    ],
                    backgroundColor: ['#28a745', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                },
                tooltips: {
                    callbacks: {
                        label: function(tooltipItem, data) {
                            const label = data.labels[tooltipItem.index];
                            const value = data.datasets[0].data[tooltipItem.index];
                            return label + ': ' + value.toLocaleString('ar-EG') + ' جنيه';
                        }
                    }
                }
            }
        });
    }
    
    // إضافة تأثيرات للبطاقات
    $('.vat-card').each(function() {
        $(this).on('click', function() {
            $(this).toggleClass('selected');
        });
    });
});

// دالة تصدير تقرير الضرائب
function exportVATReport(format) {
    const startDate = $('#input-date-start').val();
    const endDate = $('#input-date-end').val();
    const url = 'index.php?route=accounts/vat_report/export&format=' + format + 
                '&start_date=' + startDate + '&end_date=' + endDate;
    window.open(url, '_blank');
}

// دالة طباعة تقرير الضرائب
function printVATReport() {
    window.print();
}

// دالة تحديث البيانات
function refreshVATData() {
    location.reload();
}

// دالة فلترة البيانات
$('#button-filter').on('click', function() {
    const startDate = $('#input-date-start').val();
    const endDate = $('#input-date-end').val();
    
    if (startDate && endDate) {
        const url = 'index.php?route=accounts/vat_report&filter_date_start=' + 
                    startDate + '&filter_date_end=' + endDate;
        window.location = url;
    } else {
        alert('يرجى تحديد تاريخ البداية والنهاية');
    }
});
//--></script>
{{ footer }}
