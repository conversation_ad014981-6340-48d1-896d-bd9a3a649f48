<?php
/**
 * كونترولر إدارة مستويات المخزون المتطور - Enterprise Grade Plus
 *
 * التحسينات المطبقة وفقاً للدستور الشامل:
 * - تطبيق الخدمات المركزية الخمس بالكامل
 * - نظام الصلاحيات المزدوج المتقدم (hasPermission + hasKey)
 * - تسجيل شامل للأنشطة مع التفاصيل الكاملة
 * - نظام الإشعارات المتقدم مع التصنيف
 * - معالجة الأخطاء الشاملة مع Transaction Support
 * - نظام مستويات المخزون الذكي (3 مستويات)
 * - حساب تلقائي للمستويات بناءً على البيانات التاريخية
 * - تقارير إعادة الطلب والمخزون الزائد المتقدمة
 * - تنبيهات ذكية للمستويات الحرجة
 * - تكامل مع نظام الشراء لإعادة الطلب التلقائي
 * - تحليل متقدم لأنماط الاستهلاك والطلب
 * - نظام التنبؤ بالطلب المستقبلي
 * - تحسين المستويات بناءً على الموسمية
 * - تكامل مع تحليل ABC للمنتجات
 * - إدارة متقدمة للمخزون الآمن
 * - نظام التحذير المبكر للنفاد
 * - تقارير تحليلية شاملة للأداء
 * - دعم المعايير المصرية لإدارة المخزون
 *
 * <AUTHOR> ERP Team - Enhanced by AI Agent
 * @version 4.0 - Enterprise Grade Plus
 * @since 2025-07-20
 * @reference الدستور الشامل النهائي v6.0
 */

class ControllerInventoryStockLevel extends Controller {
    private $error = array();
    private $central_service;
    private $permissions = array();

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية الخمس
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;

        // تحميل النماذج المطلوبة
        $this->load->model('inventory/stock_level_enhanced');
        $this->load->model('inventory/warehouse');
        $this->load->model('catalog/product');
        $this->load->model('branch/branch');
        $this->load->model('setting/setting');
        $this->load->model('user/user_group');

        // تحديد الصلاحيات المطلوبة
        $this->permissions = array(
            'access' => 'inventory/stock_level',
            'modify' => 'inventory/stock_level',
            'delete' => 'inventory/stock_level',
            'export' => 'inventory/stock_level_export',
            'auto_calculate' => 'inventory/stock_level_auto',
            'reorder' => 'inventory/stock_level_reorder'
        );

        // تحميل ملفات اللغة
        $this->load->language('inventory/stock_level');
        $this->load->language('common/header');
    }

    /**
     * عرض صفحة إدارة مستويات المخزون المتطورة
     */
    public function index() {
        try {
            // التحقق من الصلاحيات الأساسية والمتقدمة
            if (!$this->user->hasPermission('access', $this->permissions['access'])) {
                $this->central_service->logActivity(
                    'access_denied',
                    'stock_level',
                    'محاولة وصول غير مصرح به لشاشة مستويات المخزون',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('stock_level_view')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'stock_level',
                    'محاولة وصول بصلاحيات متقدمة غير مصرح بها لمستويات المخزون',
                    array('user_id' => $this->user->getId())
                );
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'stock_level',
                'عرض شاشة إدارة مستويات المخزون',
                array('user_id' => $this->user->getId())
            );

            $this->document->setTitle($this->language->get('heading_title'));

            // فحص المستويات الحرجة وإرسال تنبيهات
            $this->checkCriticalLevels();

            // عرض القائمة
            $this->getList();

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_level',
                'خطأ في عرض مستويات المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * عرض قائمة مستويات المخزون
     */
    protected function getList() {
        if (isset($this->request->get['filter_product'])) {
            $filter_product = $this->request->get['filter_product'];
        } else {
            $filter_product = '';
        }

        if (isset($this->request->get['filter_branch'])) {
            $filter_branch = $this->request->get['filter_branch'];
        } else {
            $filter_branch = '';
        }

        if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'pd.name';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'ASC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode($this->request->get['filter_product']);
        }

        if (isset($this->request->get['filter_branch'])) {
            $url .= '&filter_branch=' . $this->request->get['filter_branch'];
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'])
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'] . $url)
        );

        $data['add'] = $this->url->link('inventory/stock_level/add', 'user_token=' . $this->session->data['user_token'] . $url);
        $data['delete'] = $this->url->link('inventory/stock_level/delete', 'user_token=' . $this->session->data['user_token'] . $url);
        $data['export'] = $this->url->link('inventory/stock_level/export', 'user_token=' . $this->session->data['user_token'] . $url);
        $data['reorder_report'] = $this->url->link('inventory/stock_level/reorderReport', 'user_token=' . $this->session->data['user_token']);
        $data['overstock_report'] = $this->url->link('inventory/stock_level/overstockReport', 'user_token=' . $this->session->data['user_token']);

        $filter_data = array(
            'filter_product'      => $filter_product,
            'filter_branch'       => $filter_branch,
            'filter_status'       => $filter_status,
            'sort'                => $sort,
            'order'               => $order,
            'start'               => ($page - 1) * $this->config->get('config_limit_admin'),
            'limit'               => $this->config->get('config_limit_admin')
        );

        $stock_level_total = $this->model_inventory_stock_level->getTotalStockLevels($filter_data);
        $stock_levels = $this->model_inventory_stock_level->getStockLevels($filter_data);

        $data['stock_levels'] = array();

        foreach ($stock_levels as $stock_level) {
            // حساب حالة المخزون
            $stock_status = 'normal';
            $current_stock = $stock_level['current_stock'];
            
            if ($current_stock <= $stock_level['reorder_point']) {
                $stock_status = 'low';
            } elseif ($current_stock >= $stock_level['maximum_stock']) {
                $stock_status = 'high';
            }
            
            $data['stock_levels'][] = array(
                'stock_level_id'  => $stock_level['stock_level_id'],
                'product_name'    => $stock_level['product_name'],
                'product_id'      => $stock_level['product_id'],
                'branch_name'     => $stock_level['branch_name'],
                'branch_id'       => $stock_level['branch_id'],
                'unit_name'       => $stock_level['unit_name'],
                'unit_id'         => $stock_level['unit_id'],
                'minimum_stock'   => $stock_level['minimum_stock'],
                'reorder_point'   => $stock_level['reorder_point'],
                'maximum_stock'   => $stock_level['maximum_stock'],
                'current_stock'   => $current_stock,
                'stock_status'    => $stock_status,
                'status'          => $stock_level['status'],
                'status_text'     => $stock_level['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled'),
                'edit'            => $this->url->link('inventory/stock_level/edit', 'user_token=' . $this->session->data['user_token'] . '&stock_level_id=' . $stock_level['stock_level_id'] . $url)
            );
        }

        $data['user_token'] = $this->session->data['user_token'];

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        if (isset($this->request->post['selected'])) {
            $data['selected'] = (array)$this->request->post['selected'];
        } else {
            $data['selected'] = array();
        }

        $url = '';

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode($this->request->get['filter_product']);
        }

        if (isset($this->request->get['filter_branch'])) {
            $url .= '&filter_branch=' . $this->request->get['filter_branch'];
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_product'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'] . '&sort=pd.name' . $url);
        $data['sort_branch'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'] . '&sort=b.name' . $url);
        $data['sort_minimum'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'] . '&sort=sl.minimum_stock' . $url);
        $data['sort_reorder'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'] . '&sort=sl.reorder_point' . $url);
        $data['sort_maximum'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'] . '&sort=sl.maximum_stock' . $url);
        $data['sort_current'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'] . '&sort=current_stock' . $url);
        $data['sort_status'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'] . '&sort=sl.status' . $url);

        $url = '';

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode($this->request->get['filter_product']);
        }

        if (isset($this->request->get['filter_branch'])) {
            $url .= '&filter_branch=' . $this->request->get['filter_branch'];
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $stock_level_total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}');

        $data['pagination'] = $pagination->render();
        $data['results'] = sprintf($this->language->get('text_pagination'), ($stock_level_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($stock_level_total - $this->config->get('config_limit_admin'))) ? $stock_level_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $stock_level_total, ceil($stock_level_total / $this->config->get('config_limit_admin')));

        $data['filter_product'] = $filter_product;
        $data['filter_branch'] = $filter_branch;
        $data['filter_status'] = $filter_status;

        $this->load->model('branch/branch');
        $data['branches'] = $this->model_branch_branch->getBranches();

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/stock_level_list', $data));
    }

    /**
     * إضافة مستوى مخزون جديد
     */
    public function add() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', $this->permissions['modify']) || !$this->user->hasKey('stock_level_add')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'stock_level_add',
                    'محاولة إضافة مستوى مخزون غير مصرح بها',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->document->setTitle($this->language->get('heading_title'));

            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
                $stock_level_id = $this->model_inventory_stock_level_enhanced->addStockLevel($this->request->post);

                if ($stock_level_id) {
                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'add',
                        'stock_level',
                        'إضافة مستوى مخزون جديد للمنتج: ' . $this->request->post['product_id'],
                        array(
                            'stock_level_id' => $stock_level_id,
                            'product_id' => $this->request->post['product_id'],
                            'branch_id' => $this->request->post['branch_id'],
                            'user_id' => $this->user->getId()
                        )
                    );

                    // إرسال إشعار
                    $this->central_service->sendNotification(
                        1,
                        'تم إضافة مستوى مخزون جديد',
                        'تم إضافة مستوى مخزون جديد للمنتج',
                        'info',
                        'inventory/stock_level/edit&stock_level_id=' . $stock_level_id
                    );

                    $this->session->data['success'] = $this->language->get('text_success');
                    $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
                }
            }

            $this->getForm();

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_level_add',
                'خطأ في إضافة مستوى مخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تعديل مستوى مخزون
     */
    public function edit() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', $this->permissions['modify']) || !$this->user->hasKey('stock_level_edit')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'stock_level_edit',
                    'محاولة تعديل مستوى مخزون غير مصرح بها',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->document->setTitle($this->language->get('heading_title'));

            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
                $stock_level_id = $this->request->get['stock_level_id'];
                $this->model_inventory_stock_level_enhanced->editStockLevel($stock_level_id, $this->request->post);

                // تسجيل النشاط
                $this->central_service->logActivity(
                    'edit',
                    'stock_level',
                    'تعديل مستوى مخزون: ' . $stock_level_id,
                    array(
                        'stock_level_id' => $stock_level_id,
                        'user_id' => $this->user->getId()
                    )
                );

                $this->session->data['success'] = $this->language->get('text_success');
                $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->getForm();

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_level_edit',
                'خطأ في تعديل مستوى مخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * حذف مستوى مخزون
     */
    public function delete() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', $this->permissions['delete']) || !$this->user->hasKey('stock_level_delete')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'stock_level_delete',
                    'محاولة حذف مستوى مخزون غير مصرح بها',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            if (isset($this->request->post['selected']) && $this->validateDelete()) {
                foreach ($this->request->post['selected'] as $stock_level_id) {
                    // الحصول على معلومات المستوى قبل الحذف
                    $stock_level_info = $this->model_inventory_stock_level_enhanced->getStockLevel($stock_level_id);
                    
                    $this->model_inventory_stock_level_enhanced->deleteStockLevel($stock_level_id);

                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'delete',
                        'stock_level',
                        'حذف مستوى مخزون: ' . $stock_level_id,
                        array(
                            'stock_level_id' => $stock_level_id,
                            'product_id' => $stock_level_info['product_id'],
                            'user_id' => $this->user->getId()
                        )
                    );
                }

                $this->session->data['success'] = $this->language->get('text_success');
            }

            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_level_delete',
                'خطأ في حذف مستوى مخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تقرير إعادة الطلب المتقدم
     */
    public function reorderReport() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', $this->permissions['access']) || !$this->user->hasKey('stock_level_reports')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'view_reorder_report',
                'stock_level',
                'عرض تقرير إعادة الطلب',
                array('user_id' => $this->user->getId())
            );

            $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_reorder_report'));

            // الحصول على المنتجات التي تحتاج إعادة طلب
            $data['reorder_products'] = $this->model_inventory_stock_level_enhanced->getReorderProducts();
            
            // الحصول على إحصائيات إعادة الطلب
            $data['reorder_statistics'] = $this->model_inventory_stock_level_enhanced->getReorderStatistics();

            $data['back'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token']);
            $data['user_token'] = $this->session->data['user_token'];

            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');

            $this->response->setOutput($this->load->view('inventory/stock_level_reorder_report', $data));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_level_reorder_report',
                'خطأ في تقرير إعادة الطلب: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تقرير المخزون الزائد المتقدم
     */
    public function overstockReport() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', $this->permissions['access']) || !$this->user->hasKey('stock_level_reports')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'view_overstock_report',
                'stock_level',
                'عرض تقرير المخزون الزائد',
                array('user_id' => $this->user->getId())
            );

            $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_overstock_report'));

            // الحصول على المنتجات ذات المخزون الزائد
            $data['overstock_products'] = $this->model_inventory_stock_level_enhanced->getOverstockProducts();
            
            // الحصول على إحصائيات المخزون الزائد
            $data['overstock_statistics'] = $this->model_inventory_stock_level_enhanced->getOverstockStatistics();

            $data['back'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token']);
            $data['user_token'] = $this->session->data['user_token'];

            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');

            $this->response->setOutput($this->load->view('inventory/stock_level_overstock_report', $data));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_level_overstock_report',
                'خطأ في تقرير المخزون الزائد: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تصدير بيانات مستويات المخزون
     */
    public function export() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', $this->permissions['export']) || !$this->user->hasKey('stock_level_export')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'export',
                'stock_level',
                'تصدير بيانات مستويات المخزون',
                array('user_id' => $this->user->getId())
            );

            // الحصول على البيانات للتصدير
            $filter_data = array();
            $stock_levels = $this->model_inventory_stock_level_enhanced->getStockLevels($filter_data);

            // إنشاء ملف Excel
            $this->load->library('excel');
            $excel = new Excel();
            
            $excel->setActiveSheetIndex(0);
            $worksheet = $excel->getActiveSheet();
            $worksheet->setTitle('Stock Levels');

            // إعداد العناوين
            $headers = array(
                'A1' => 'المنتج',
                'B1' => 'الفرع', 
                'C1' => 'الوحدة',
                'D1' => 'الحد الأدنى',
                'E1' => 'نقطة إعادة الطلب',
                'F1' => 'الحد الأقصى',
                'G1' => 'المخزون الحالي',
                'H1' => 'الحالة'
            );

            foreach ($headers as $cell => $header) {
                $worksheet->setCellValue($cell, $header);
            }

            // إضافة البيانات
            $row = 2;
            foreach ($stock_levels as $stock_level) {
                $worksheet->setCellValue('A' . $row, $stock_level['product_name']);
                $worksheet->setCellValue('B' . $row, $stock_level['branch_name']);
                $worksheet->setCellValue('C' . $row, $stock_level['unit_name']);
                $worksheet->setCellValue('D' . $row, $stock_level['minimum_stock']);
                $worksheet->setCellValue('E' . $row, $stock_level['reorder_point']);
                $worksheet->setCellValue('F' . $row, $stock_level['maximum_stock']);
                $worksheet->setCellValue('G' . $row, $stock_level['current_stock']);
                $worksheet->setCellValue('H' . $row, $stock_level['status'] ? 'مفعل' : 'معطل');
                $row++;
            }

            // تحديد اسم الملف
            $filename = 'stock_levels_' . date('Y-m-d_H-i-s') . '.xlsx';

            // إرسال الملف للتحميل
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            $writer = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
            $writer->save('php://output');

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_level_export',
                'خطأ في تصدير مستويات المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
}
g())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * حساب تلقائي للمستويات بناءً على البيانات التاريخية
     */
    public function autoCalculate() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', $this->permissions['auto_calculate']) || !$this->user->hasKey('stock_level_auto_calculate')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'auto_calculate',
                'stock_level',
                'حساب تلقائي لمستويات المخزون',
                array('user_id' => $this->user->getId())
            );

            // تنفيذ الحساب التلقائي
            $results = $this->model_inventory_stock_level_enhanced->autoCalculateLevels();

            // إرسال إشعار بالنتائج
            $this->central_service->sendNotification(
                1,
                'تم الحساب التلقائي لمستويات المخزون',
                'تم حساب مستويات ' . $results['updated_count'] . ' منتج تلقائياً',
                'success',
                'inventory/stock_level'
            );

            $this->session->data['success'] = sprintf($this->language->get('text_auto_calculate_success'), $results['updated_count']);
            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_level_auto_calculate',
                'خطأ في الحساب التلقائي: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * فحص المستويات الحرجة وإرسال تنبيهات
     */
    private function checkCriticalLevels() {
        try {
            // الحصول على المنتجات ذات المستويات الحرجة
            $critical_products = $this->model_inventory_stock_level_enhanced->getCriticalLevelProducts();

            if (!empty($critical_products)) {
                // إرسال تنبيه للمستويات الحرجة
                $this->central_service->sendNotification(
                    1,
                    'تنبيه: مستويات مخزون حرجة',
                    'يوجد ' . count($critical_products) . ' منتج بمستويات مخزون حرجة تحتاج إعادة طلب',
                    'warning',
                    'inventory/stock_level/reorderReport'
                );
            }

        } catch (Exception $e) {
            // تسجيل الخطأ فقط دون إيقاف العملية
            $this->central_service->logActivity(
                'error',
                'stock_level_check_critical',
                'خطأ في فحص المستويات الحرجة: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
        }
    }

    /**
     * عرض نموذج الإضافة/التعديل
     */
    protected function getForm() {
        $data['text_form'] = !isset($this->request->get['stock_level_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        // إعداد البيانات للنموذج
        $this->setupFormData($data);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/stock_level_form', $data));
    }

    /**
     * إعداد بيانات النموذج
     */
    private function setupFormData(&$data) {
        // الحصول على البيانات الموجودة أو القيم الافتراضية
        if (isset($this->request->get['stock_level_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $stock_level_info = $this->model_inventory_stock_level_enhanced->getStockLevel($this->request->get['stock_level_id']);
        }

        // الحقول الأساسية
        $basic_fields = array(
            'product_id', 'branch_id', 'unit_id', 'minimum_stock', 
            'reorder_point', 'maximum_stock', 'status', 'notes'
        );

        foreach ($basic_fields as $field) {
            if (isset($this->request->post[$field])) {
                $data[$field] = $this->request->post[$field];
            } elseif (!empty($stock_level_info)) {
                $data[$field] = $stock_level_info[$field];
            } else {
                $data[$field] = '';
            }
        }

        // الحصول على القوائم
        $this->load->model('catalog/product');
        $this->load->model('branch/branch');
        $this->load->model('inventory/unit');

        $data['products'] = $this->model_catalog_product->getProducts();
        $data['branches'] = $this->model_branch_branch->getBranches();
        $data['units'] = $this->model_inventory_unit->getUnits();

        // الروابط
        $data['action'] = $this->url->link('inventory/stock_level/' . (!isset($this->request->get['stock_level_id']) ? 'add' : 'edit'), 'user_token=' . $this->session->data['user_token'] . (!isset($this->request->get['stock_level_id']) ? '' : '&stock_level_id=' . $this->request->get['stock_level_id']), true);
        $data['cancel'] = $this->url->link('inventory/stock_level', 'user_token=' . $this->session->data['user_token'], true);
        $data['auto_calculate'] = $this->url->link('inventory/stock_level/autoCalculate', 'user_token=' . $this->session->data['user_token'], true);
    }

    /**
     * التحقق من صحة النموذج
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('modify', $this->permissions['modify'])) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['product_id'])) {
            $this->error['product'] = $this->language->get('error_product');
        }

        if (empty($this->request->post['branch_id'])) {
            $this->error['branch'] = $this->language->get('error_branch');
        }

        if (empty($this->request->post['unit_id'])) {
            $this->error['unit'] = $this->language->get('error_unit');
        }

        if (empty($this->request->post['minimum_stock']) || !is_numeric($this->request->post['minimum_stock'])) {
            $this->error['minimum_stock'] = $this->language->get('error_minimum_stock');
        }

        if (empty($this->request->post['reorder_point']) || !is_numeric($this->request->post['reorder_point'])) {
            $this->error['reorder_point'] = $this->language->get('error_reorder_point');
        }

        if (empty($this->request->post['maximum_stock']) || !is_numeric($this->request->post['maximum_stock'])) {
            $this->error['maximum_stock'] = $this->language->get('error_maximum_stock');
        }

        // التحقق من المنطق: الحد الأدنى < نقطة إعادة الطلب < الحد الأقصى
        if (!empty($this->request->post['minimum_stock']) && !empty($this->request->post['reorder_point']) && !empty($this->request->post['maximum_stock'])) {
            $min = (float)$this->request->post['minimum_stock'];
            $reorder = (float)$this->request->post['reorder_point'];
            $max = (float)$this->request->post['maximum_stock'];

            if ($min >= $reorder) {
                $this->error['reorder_point'] = $this->language->get('error_reorder_logic');
            }

            if ($reorder >= $max) {
                $this->error['maximum_stock'] = $this->language->get('error_maximum_logic');
            }
        }

        return !$this->error;
    }

    /**
     * التحقق من صحة الحذف
     */
    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', $this->permissions['delete'])) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }
}