/* ===================================================================
   AYM ERP - Login Page Styles
   Advanced Login Interface with Modern Design
   =================================================================== */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.login-page {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    bottom: 10%;
    right: 10%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* Main Container */
.login-container {
    display: flex;
    min-height: 100vh;
    align-items: center;
    justify-content: center;
    padding: 20px;
    gap: 40px;
}

/* Login Card */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    padding: 40px;
    width: 100%;
    max-width: 450px;
    animation: slideInLeft 0.8s ease-out;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Header Section */
.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.logo-circle {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    animation: pulse 2s infinite;
}

.logo-circle i {
    font-size: 32px;
    color: white;
}

@keyframes pulse {
    0% {
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    50% {
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
    }
    100% {
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
}

.system-title {
    font-size: 32px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
    letter-spacing: -1px;
}

.system-subtitle {
    color: #666;
    font-size: 16px;
    font-weight: 400;
}

/* Alert Messages */
.alert {
    padding: 15px 20px;
    border-radius: 12px;
    margin-bottom: 25px;
    border: none;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.alert-danger {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.alert-success {
    background: linear-gradient(135deg, #51cf66, #40c057);
    color: white;
    box-shadow: 0 5px 15px rgba(81, 207, 102, 0.3);
}

/* Form Styles */
.login-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 18px;
    color: #999;
    font-size: 16px;
    z-index: 2;
    transition: color 0.3s ease;
}

.form-control {
    width: 100%;
    padding: 18px 18px 18px 50px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    outline: none;
}

.form-control:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
}

.form-control:focus + .floating-label,
.form-control:not(:placeholder-shown) + .floating-label {
    transform: translateY(-28px) scale(0.85);
    color: #667eea;
}

.form-control:focus ~ .input-icon {
    color: #667eea;
}

.floating-label {
    position: absolute;
    left: 50px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    background: white;
    padding: 0 5px;
}

.password-toggle {
    position: absolute;
    right: 18px;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    user-select: none;
}

.checkbox-container input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input:checked ~ .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-container input:checked ~ .checkmark:after {
    content: '\f00c';
    font-family: 'FontAwesome';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
}

.forgot-link {
    color: #667eea;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Login Button */
.btn-login {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-login:active {
    transform: translateY(0);
}

.btn-login:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Security Info */
.security-info {
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #eee;
}

.security-features {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 15px;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: #666;
    font-size: 12px;
    flex: 1;
    min-width: 80px;
}

.feature i {
    font-size: 18px;
    color: #667eea;
    margin-bottom: 5px;
}

/* Footer */
.login-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #eee;
    margin-top: 20px;
}

.version-info {
    color: #999;
    font-size: 12px;
    margin-bottom: 10px;
}

.separator {
    margin: 0 8px;
}

.language-switcher {
    display: flex;
    justify-content: center;
    gap: 5px;
    align-items: center;
}

.lang-link {
    color: #666;
    text-decoration: none;
    font-size: 12px;
    padding: 5px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.lang-link:hover,
.lang-link.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

/* Features Panel */
.features-panel {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 40px;
    width: 100%;
    max-width: 400px;
    animation: slideInRight 0.8s ease-out;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.features-content h3 {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
    text-align: center;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 20px;
    color: white;
}

.feature-text h4 {
    color: white;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.feature-text p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .login-container {
        flex-direction: column;
        gap: 30px;
    }
    
    .features-panel {
        max-width: 450px;
    }
}

@media (max-width: 768px) {
    .login-container {
        padding: 15px;
    }
    
    .login-card,
    .features-panel {
        padding: 30px 25px;
    }
    
    .system-title {
        font-size: 28px;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .security-features {
        flex-direction: column;
        gap: 10px;
    }
    
    .feature {
        flex-direction: row;
        text-align: left;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .login-card,
    .features-panel {
        padding: 25px 20px;
    }
    
    .logo-circle {
        width: 60px;
        height: 60px;
    }
    
    .logo-circle i {
        font-size: 24px;
    }
    
    .system-title {
        font-size: 24px;
    }
    
    .form-control {
        padding: 15px 15px 15px 45px;
    }
    
    .floating-label {
        left: 45px;
    }
    
    .input-icon {
        left: 15px;
    }
}

/* Animation Classes */
.animated {
    animation-duration: 0.6s;
    animation-fill-mode: both;
}

.fadeInDown {
    animation-name: fadeInDown;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translate3d(0, -20px, 0);
    }
    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}
