#paypal_card_form {
	color: #717171;
	text-align: left;
	transition: all 600ms cubic-bezier(0.2, 1.3, 0.7, 1);
	-webkit-animation: cardIntro 500ms cubic-bezier(0.2, 1.3, 0.7, 1);
    animation: cardIntro 500ms cubic-bezier(0.2, 1.3, 0.7, 1);
	z-index: 1;
}
#paypal_card_form.american-express {
	color: #FFFFFF;
	background-color: #007CC3;
}
#paypal_card_form.diners-club {
	color: #FFFFFF;
	background-color: #0079BE;
}
#paypal_card_form.discover {
	color: #FFFFFF;
	background-color: #ff6000;
	background: linear-gradient(#d14310, #f7961e);
}
#paypal_card_form.jcb {
	color: #FFFFFF;
	background-color: #363636;
	background: linear-gradient(90deg, #005182, #005182 33%, #BE1833 33% 67%, #00933F 67%, #00933F);
}
#paypal_card_form.mastercard {
	color: #FFFFFF;
	background-color: #363636;
	background: linear-gradient(115deg, #D82332, #D82332 50%, #F1AD3D 50%, #F1AD3D);
}
#paypal_card_form.maestro {
	color: #FFFFFF;
	background-color: #363636;
	background: linear-gradient(115deg, #009DDD, #009DDD 50%, #ED1C2E 50%, #ED1C2E);
}
#paypal_card_form.unionpay {
	color: #FFFFFF;
	background-color: #363636;
	background: linear-gradient(100deg, #D10429, #D10429 33%, #022E64 33% 67%, #076F74 67%, #076F74);
}
#paypal_card_form.visa {
	color: #FFFFFF;
	background-color: #0D4AA2;
}
#paypal_card_form.elo {
	color: #FFFFFF;
	background-color: #000000;
}
#paypal_card_form.hiper {
	color: #FFFFFF;
	background-color: #F37421;
}
#paypal_card_form.hipercard {
	color: #FFFFFF;
	background-color: #BA1319
}
#paypal_card_form .card-info-holder-name,
#paypal_card_form .card-info-number,
#paypal_card_form .card-info-date-cvv {
	position: relative;
	text-align: left;
}
#paypal_card_form .card-info-date {
	width: 48%;
	float: left;
	margin-bottom: 0.5em;
}
#paypal_card_form .card-info-cvv {
	width: 48%;
	float: right;
	margin-bottom: 0.5em;
}
#paypal_card_form .card-info-holder-name,
#paypal_card_form .card-info-number,
#paypal_card_form .card-info-date,
#paypal_card_form .card-info-cvv {
	transition: -webkit-transform 0.3s;
	transition: transform 0.3s;
	transition: transform 0.3s, -webkit-transform 0.3s;
}
#paypal_card_form .card-button {
	position: relative;
	padding: 0rem 0.6rem;
}
#paypal_card_form .paypal-card-button {
	width: 100%;	
	padding: 16px 16px;
	color: #FFFFFF;
	background: #282C37;
	font-size: 15px;
	box-shadow: none;
	-moz-box-shadow: none;
	-webkit-box-shadow: none;
	border: 0;
	z-index: 0;
	opacity: 0.1;
	outline: 0;
	-webkit-appearance: none;
}
#paypal_card_form .paypal-card-button:hover {
	background: #535b72;
}
#paypal_card_form.elo .paypal-card-button {
	color: #000000;
	background: #FFFFFF;
}
#paypal_card_form.elo .paypal-card-button:hover {
	background: #F5F5F5;
}
#paypal_card_form .paypal-card-button:active {
	-webkit-animation: cardIntro 200ms cubic-bezier(0.2, 1.3, 0.7, 1);
    animation: cardIntro 200ms cubic-bezier(0.2, 1.3, 0.7, 1);
}
#paypal_card_form .paypal-card-button.show-button {
	opacity: 1;
	cursor: pointer;
}