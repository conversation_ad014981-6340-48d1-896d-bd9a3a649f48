{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ button_add }}
        </a>
        <div class="btn-group">
          <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-download"></i> {{ button_export }}
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu">
            <li><a href="{{ export_excel }}"><i class="fa fa-file-excel-o"></i> {{ button_export_excel }}</a></li>
            <li><a href="{{ export_pdf }}"><i class="fa fa-file-pdf-o"></i> {{ button_export_pdf }}</a></li>
            <li><a href="{{ print }}"><i class="fa fa-print"></i> {{ button_print }}</a></li>
          </ul>
        </div>
        <button type="button" class="btn btn-info" onclick="showStatistics()">
          <i class="fa fa-bar-chart"></i> {{ text_statistics }}
        </button>
        <a href="{{ refresh }}" class="btn btn-default">
          <i class="fa fa-refresh"></i> {{ button_refresh }}
        </a>
      </div>
      <h1><i class="fa fa-balance-scale"></i> {{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- رسائل التنبيه -->
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- لوحة الملخص السريع -->
    {% if summary %}
    <div class="row">
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-primary">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-list fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_adjustments }}</div>
                <div>{{ text_total_adjustments }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-warning">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-clock-o fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.pending_approval_count }}</div>
                <div>{{ text_pending_approval_count }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-success">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.posted_count }}</div>
                <div>{{ text_posted_count }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-info">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-arrow-up fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_increase_value }}</div>
                <div>{{ text_total_increase_value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-danger">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-arrow-down fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_decrease_value }}</div>
                <div>{{ text_total_decrease_value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-default">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-calculator fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.avg_items_per_adjustment }}</div>
                <div>{{ text_avg_items_per_adjustment }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- لوحة الفلاتر -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> {{ text_filter }}
          <button type="button" class="btn btn-xs btn-link pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div class="panel-body collapse" id="filter-panel">
        <form id="filter-form" class="form-horizontal">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_adjustment_number }}</label>
                <input type="text" name="filter_adjustment_number" value="{{ filter_adjustment_number }}" placeholder="{{ entry_adjustment_number }}" class="form-control">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_adjustment_name }}</label>
                <input type="text" name="filter_adjustment_name" value="{{ filter_adjustment_name }}" placeholder="{{ entry_adjustment_name }}" class="form-control">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ column_status }}</label>
                <select name="filter_status" class="form-control">
                  {% for option in status_options %}
                  <option value="{{ option.value }}"{% if filter_status == option.value %} selected{% endif %}>{{ option.text }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_adjustment_type }}</label>
                <select name="filter_adjustment_type" class="form-control">
                  {% for option in adjustment_type_options %}
                  <option value="{{ option.value }}"{% if filter_adjustment_type == option.value %} selected{% endif %}>{{ option.text }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_branch }}</label>
                <select name="filter_branch_id" class="form-control">
                  <option value="">{{ text_all }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"{% if filter_branch_id == branch.branch_id %} selected{% endif %}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_filter_date_from }}</label>
                <input type="date" name="filter_date_from" value="{{ filter_date_from }}" class="form-control">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_filter_date_to }}</label>
                <input type="date" name="filter_date_to" value="{{ filter_date_to }}" class="form-control">
              </div>
            </div>
            <div class="col-md-3 text-right">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <div>
                  <button type="button" class="btn btn-primary" onclick="applyFilters()">
                    <i class="fa fa-search"></i> {{ text_apply_filters }}
                  </button>
                  <button type="button" class="btn btn-default" onclick="clearFilters()">
                    <i class="fa fa-refresh"></i> {{ text_clear_filters }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- جدول التسويات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-list"></i> {{ text_list }}
          <span class="badge pull-right">{{ results }}</span>
        </h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="adjustment-table">
            <thead>
              <tr>
                <th class="text-center">{{ column_adjustment_number }}</th>
                <th class="text-left">{{ column_adjustment_name }}</th>
                <th class="text-center">{{ column_status }}</th>
                <th class="text-center">{{ column_adjustment_type }}</th>
                <th class="text-left">{{ column_branch }}</th>
                <th class="text-center">{{ column_total_items }}</th>
                <th class="text-right">{{ column_total_value }}</th>
                <th class="text-center">{{ column_adjustment_date }}</th>
                <th class="text-left">{{ column_user }}</th>
                <th class="text-center">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% if stock_adjustments %}
              {% for adjustment in stock_adjustments %}
              <tr data-adjustment-id="{{ adjustment.adjustment_id }}">
                <td class="text-center">
                  <strong>{{ adjustment.adjustment_number }}</strong>
                </td>
                <td class="text-left">
                  <strong>{{ adjustment.adjustment_name }}</strong>
                  {% if adjustment.reason_name %}
                  <br><small class="text-muted">{{ adjustment.reason_name }}</small>
                  {% endif %}
                </td>
                <td class="text-center">
                  <span class="label label-{{ adjustment.status_class }}">
                    {{ adjustment.status_text }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="label label-info">
                    {{ adjustment.adjustment_type_text }}
                  </span>
                </td>
                <td class="text-left">
                  {{ adjustment.branch_name }}
                  {% if adjustment.branch_type %}
                  <br><small class="text-muted">{{ adjustment.branch_type }}</small>
                  {% endif %}
                </td>
                <td class="text-center">
                  <span class="badge badge-primary">{{ adjustment.total_items }}</span>
                </td>
                <td class="text-right">
                  <strong class="text-{{ adjustment.value_class }}">{{ adjustment.total_value }}</strong>
                  {% if adjustment.total_increase_value != '0.00' %}
                  <br><small class="text-success">+{{ adjustment.total_increase_value }}</small>
                  {% endif %}
                  {% if adjustment.total_decrease_value != '0.00' %}
                  <br><small class="text-danger">-{{ adjustment.total_decrease_value }}</small>
                  {% endif %}
                </td>
                <td class="text-center">
                  <small>{{ adjustment.adjustment_date }}</small>
                  {% if adjustment.approval_date %}
                  <br><small class="text-muted">{{ adjustment.approval_date }}</small>
                  {% endif %}
                </td>
                <td class="text-left">
                  <small>{{ adjustment.user_name }}</small>
                  {% if adjustment.approved_by_name %}
                  <br><small class="text-success">{{ adjustment.approved_by_name }}</small>
                  {% endif %}
                </td>
                <td class="text-center">
                  <div class="btn-group">
                    <a href="{{ adjustment.view }}" class="btn btn-info btn-xs" data-toggle="tooltip" title="{{ button_view }}">
                      <i class="fa fa-eye"></i>
                    </a>
                    {% if adjustment.can_edit %}
                    <a href="{{ adjustment.edit }}" class="btn btn-primary btn-xs" data-toggle="tooltip" title="{{ button_edit }}">
                      <i class="fa fa-pencil"></i>
                    </a>
                    {% endif %}
                    {% if adjustment.can_approve %}
                    <a href="{{ adjustment.approve }}" class="btn btn-success btn-xs" data-toggle="tooltip" title="{{ button_approve }}" onclick="return confirm('{{ confirm_approve }}')">
                      <i class="fa fa-check"></i>
                    </a>
                    <button type="button" class="btn btn-warning btn-xs" data-toggle="tooltip" title="{{ button_reject }}" onclick="showRejectModal({{ adjustment.adjustment_id }})">
                      <i class="fa fa-times"></i>
                    </button>
                    {% endif %}
                    {% if adjustment.can_post %}
                    <a href="{{ adjustment.post }}" class="btn btn-success btn-xs" data-toggle="tooltip" title="{{ button_post }}" onclick="return confirm('{{ confirm_post }}')">
                      <i class="fa fa-share"></i>
                    </a>
                    {% endif %}
                  </div>
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="10">
                  <div class="alert alert-info" style="margin: 20px 0;">
                    <i class="fa fa-info-circle"></i> {{ text_no_results }}
                  </div>
                </td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
        
        <!-- الصفحات -->
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal للرفض -->
<div class="modal fade" id="reject-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-times-circle"></i> {{ button_reject }}</h4>
      </div>
      <form id="reject-form" method="post">
        <div class="modal-body">
          <div class="form-group">
            <label for="rejection_reason">{{ text_rejection_reason }}</label>
            <textarea name="rejection_reason" id="rejection_reason" class="form-control" rows="4" required></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="submit" class="btn btn-warning">
            <i class="fa fa-times"></i> {{ button_reject }}
          </button>
          <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script type="text/javascript">
// متغيرات عامة
var user_token = '{{ user_token }}';
var adjustment_url = 'index.php?route=inventory/stock_adjustment';

// تطبيق الفلاتر
function applyFilters() {
    var url = adjustment_url + '&user_token=' + user_token;
    var filters = $('#filter-form').serialize();
    
    if (filters) {
        url += '&' + filters;
    }
    
    location = url;
}

// مسح الفلاتر
function clearFilters() {
    $('#filter-form')[0].reset();
    location = adjustment_url + '&user_token=' + user_token;
}

// عرض الإحصائيات
function showStatistics() {
    // يمكن إضافة modal للإحصائيات المفصلة
    alert('سيتم إضافة الإحصائيات المفصلة قريباً');
}

// عرض modal الرفض
function showRejectModal(adjustmentId) {
    $('#reject-form').attr('action', adjustment_url + '/reject&user_token=' + user_token + '&adjustment_id=' + adjustmentId);
    $('#reject-modal').modal('show');
}

// تهيئة الصفحة
$(document).ready(function() {
    // تفعيل tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // تفعيل البحث السريع
    $('#quick-search').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#adjustment-table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
});
</script>

<style>
.huge {
    font-size: 30px;
}

.panel-body {
    padding: 15px;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

#adjustment-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

#adjustment-table tbody tr:hover {
    background-color: #f9f9f9;
}

@media (max-width: 768px) {
    .huge {
        font-size: 20px;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
    }
}
</style>

{{ footer }}